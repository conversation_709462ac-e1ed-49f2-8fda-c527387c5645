<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('staff/staff_menu'); ?>">Staff Master</a></li>
  <li><a href="<?php echo site_url('staff/attendance/my_attendance_desktop'); ?>">My Attendance</a></li>
  <li>Previous Attendance</li>
</ul>

<div class="col-md-12">
  <div class="card panel_new_style">
    <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
      <h3 class="card-title panel_title_new_style">
        <strong>Previous Attendance</strong>
      </h3>
    </div>
    <!-- <div class="card-header panel_heading_new_style" style="display: inline;">
          <h3 class="card-title panel_title_new_style" ><strong>Previous Attendance</strong></h3>
      </div> -->
    <div class="col-md-3 px-2" style="float: right; margin-bottom: 1.2rem;">
      <select style="width: 100%;text-align:center;" onchange="change_month()" class="form-control select" id="month_list"
        name="month_list">
        <?php
        $current_month = date('F', strtotime(date('d-m-Y')));
        foreach ($months as $key => $type) { ?>
          <option value="<?php echo $key ?>" <?php echo ($current_month == $type) ? 'selected' : ''; ?>>
            <?php echo $type ?>
          </option>
        <?php } ?>
      </select>
    </div>
    <div class="card-body px-2 att">
    </div>
  </div>
</div>
<!-- </div> -->

<div class="visible-xs visible-sm">
    <a href="<?php echo site_url('staff/attendance/my_attendance_desktop'); ?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<style type="text/css">
  .unread_box_no_style_new {
    min-height: 4.6rem;
    border-radius: 8px;
    padding: 12px 20px 0px 12px !important;
    background-color: #f5f5f5;
    margin-bottom: 1.2rem;
  }

  .card-body {
    padding: 0 1.25rem;
  }

  .btn-success {
    border-radius: 8px;
  }

  .absent {
    color: #fa0000 !important;
    font-weight: bold;
    /*color: white !important;*/
  }

  .weekoff,
  .holiday {
    color: #ffb203 !important;
    font-weight: bold;
    /*color: white !important;*/
  }

  .halfday,
  .present {
    color: green !important;
    font-weight: bold;
    /*color: white !important;*/
  }

  .onleave {
    color: #ff8201 !important;
    font-weight: bold;
    /*color: white !important;*/
  }
</style>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js"
  integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous"></script>
<script>
  let reporting_manager;
  let reporting_manager_available;
  $(document).ready(function () {
    reporting_manager = '<?php echo $rep_manager_name; ?>';
    reporting_manager_available = '<?php echo $reporting_manager_available; ?>';

    change_month();
  });

  function isDateToday(checkIndate) {
    const checkDate = new Date(checkIndate);
    const today = new Date();

    const checkInDate = checkDate.getDate();
    const checkInMonth = checkDate.getMonth();
    const checkInFullYear = checkDate.getFullYear();

    const todayDate = today.getDate();
    const todaMonth = today.getMonth();
    const todayFullYear = today.getFullYear();

    return checkInDate == todayDate && checkInMonth == todaMonth && checkInFullYear == todayFullYear;
  }

  function change_month() {
    var year_month = $('#month_list').val();
    $(".card-body").html("<p style='text-align: center'>Loading...</p>");
    //document.write(year_month);
    $.ajax({
      url: '<?php echo site_url('staff/attendance/changeMonth') ?>',
      type: 'post',
      data: {
        'year_month': year_month
      },
      success: function (data) {
        var attendance = JSON.parse(data);
        var html = '';
        var total = attendance.length;
        var p = 0;
        var ab = 0;
        var hd = 0;
        var wo = 0;
        var ho = 0;
        var ol = 0;
        let extra_p_wo = 0;
        let extra_p_ho = 0;

        if (attendance.length == 0) {
          $(".att").html('<h4>No data found</h4>');
          return false;
        } else {
          for (var i = 0; i < attendance.length; i++) {
            let leave_filed_no_of_days_without_consider_present=0;

            if(isDateToday(attendance[i].date)){
              if(total){
                total=total-1;
              }
              continue;
            }
            
            let isOnLeave=0;
            if(attendance[i]?.leave_details){
              if(Object.values(attendance[i]?.leave_details)[0]?.total_leaves_taken_count>=0.5){
                const leaveDetails=Object.values(attendance[i]?.leave_details);
              
                if(leaveDetails[0]){
                  const leavesTaken=leaveDetails[0];
                  for(let leave in leavesTaken){
                    if(typeof leavesTaken[leave]==="object" && +leavesTaken[leave].status>=0 && +leavesTaken[leave].status<=2){
                      if(+leavesTaken[leave].consider_present==0){
                        leave_filed_no_of_days_without_consider_present+= +leavesTaken[leave].leave_no_of_days;
                      }
                    }
                  }
                }
              }
            }

            const isToday = new Date(attendance[i].date).toDateString() === new Date().toDateString();
            const shiftType=Number(attendance[i].type);
            const attendanceStatus=attendance[i].attendance_status;
            // we will take all the Attendance which are happend on week-off or holiday
            if(shiftType===2 || shiftType===3){
              // 2: week-off shift
              // 2: holiday shift
              if (shiftType===2) {
                wo += 1;
              } else if (shiftType===3) {
                ho += 1;
              }

              // Track if present or half-day on off-days
              if (attendanceStatus === "P" || attendanceStatus === "FD") {
                if (shiftType === 2) extra_p_wo += 1;
                if (shiftType === 3) extra_p_ho += 1;
              } else if (attendanceStatus === "HD") {
                if (shiftType === 2) extra_p_wo += 0.5;
                if (shiftType === 3) extra_p_ho += 0.5;
              }
            }else if(shiftType===1){
              // 1: week-day or working day shift
              if (attendanceStatus == "P" || attendanceStatus == "FD") {
                p += 1;
              } else if (attendanceStatus == "HD") {
                p += 0.5;
                if (leave_filed_no_of_days_without_consider_present == 0.5) {
                  ol += 0.5;
                } else {
                  ab += 0.5;
                }
              } else if (attendanceStatus == "AB") {
                if (leave_filed_no_of_days_without_consider_present == 0) {
                  ab += 1;
                } else if (leave_filed_no_of_days_without_consider_present == 0.5) {
                  ol += 0.5;
                  ab += 0.5;
                } else if (leave_filed_no_of_days_without_consider_present >= 1) {
                  ol += 1;
                }
              }
            }
          }
        }
        
        html += '<div class="unread_box_no_style_new" >';
        html += '<div class="d-flex justify-content-between">';
        html += ' <table class="table">';

        html += ' <tr>';
        html += ' <td>';
        html += '<div display: inline; style="font-weight: bold;">';
        html += 'Total days: ';
        html += total;
        html += '</div>';
        html += '</td>';
        html += '</tr>';

        html += ' <tr>';
        html += ' <td>';
        html += '<div style="color:green; font-weight: bold; display: inline;">';
        html += 'Present: ';
        html += p;
        html += '</div>';
        html += '</td>';
        html += '</tr>';

        html += ' <tr>';
        html += ' <td>';
        html += '<div style="color:red; font-weight: bold; display: inline;">';
        html += 'Absent: ';
        html += `${ab+ol} (Leaves filed:${ol}, Leaves not filed:${ab})`;
        html += '</div>';
        html += '</td>';
        html += '</tr>';

        html += ' <tr>';
        html += ' <td>';
        html += '<div style="color: #ffb203; font-weight: bold; display: inline;">';
        html += 'Week-Off/Holidays: ';
        html +=  wo+ho;
        html += '</div>';
        html += '</td>';
        html += '</tr>';

        html += ' <tr>';
        html += ' <td>';
        html += '<div style="color: #3e3e3e; font-weight: bold; display: inline;">';
        html += 'Extra Present on Week-Off/Holidays: ';
        html += `${extra_p_wo+extra_p_ho}`;
        html += '</div>';
        html += '</td>';
        html += '</tr>';

        html += '</table>';
        html += '</div>';
        html += '</div>';


        html += constructAttendance(attendance);

        $(".att").html(html);
      },
      error: function (error) {
        console.log(error);
      }
    });
  }

  const reasons = async function getLeaveRegularizeReasons() {
    let LeaveRegularizeReasons = [];
    await $.ajax({
      url: "<?php echo site_url('staff/attendance/getLeaveRegularizeReasons'); ?>",
      type: "POST",
      data: {},
      success: function (data) {
        let reasons = $.parseJSON(data);
        LeaveRegularizeReasons = reasons;
      }
    })
    return LeaveRegularizeReasons;
  }

  function regularizeOptions() {
    if ($("#regularize_reasons").val() == 0) {
      $("#regularize_textarea").show();
    } else {
      $("#regularize_textarea").hide();
    }
  }

  async function regularizeLeave(attendance_id, staff_id, date, btn_id, attendance_status, staff_shift_id) {
    var html = `
      <div class="">
        <label for="">Reporting Manager</label>
        ${reporting_manager}
      <div>
      <div>
          <div class="regularize_reason" data-regularize_type="options" id="regularize_options" onchange="regularizeOptions()">
            <select class="form-control" name="regularize_reasons" id="regularize_reasons">`;

    const leaveRegularizeReasons = await reasons();
    if (leaveRegularizeReasons?.length) {
      leaveRegularizeReasons.forEach(r => {
        html += `<option value="${r.name}">${r.name}</option>`;
      });
    } else {
      html += `<option value="NA">Options not available</option>`;
    }
    html += `<option value="0">Other</option>`

    html += `</select>
          </div>
      </div>

      <div class="regularize_reason" data-regularize_type="textarea" id="regularize_textarea" style="margin-top:10px;display:none;">
          <textarea placeholder="Enter reason here..." class="form-control" id="reason" rows="5"></textarea>
        </div>
      `;

    let staff_name = $("#staff_name").text();
    await Swal.fire({
      title: `Regularize Attendance <br> ${date}`,
      html: html,
      confirmButtonText: 'Confirm',
      showCancelButton: true,
      showLoaderOnConfirm: true,
      allowOutsideClick: false,
      preConfirm: function () {
        if ($("#regularize_reasons").val() == 0) {
          reason = $("#reason").val().trim();
        } else {
          reason = $("#regularize_reasons").val().trim();
        }

        if (!reason) {
          Swal.showValidationMessage('Enter the reason');
        }
      }
    }).then((result) => {
      if (result.isConfirmed) {
        var input = {
          attendance_id,
          staff_id,
          date,
          reason,
          attendance_status,
          staff_shift_id
        };

        $(`#${btn_id}`).text("Request Sent").removeClass('btn-success').removeClass('btn-danger').prop("disabled", true);

        $.ajax({
          url: "<?php echo site_url('staff/attendance/regularizeLeaveApplication') ?>",
          type: "POST",
          data: input,
          success: function (data) {
            Swal.fire({
              title: "Success",
              text: "Regularize Request Sent",
              icon: "success",
            });
          }
        })
      }
    });
  }

  const leaveStatusObject={
    0:"Pending",
    1:"Approved",
    2:"Auto Approved",
    3:"Rejected",
    4:"Cancelled",
  }

  function viewOverriddenReason(index,attendanceId,staffName,date){
    let overriddenReason = '<div class="mb-2"><h4>'+staffName+' ('+date+')</h4></div>';
    overriddenReason+=$(`#overridden-reason-${index}`).data("overridden-reason");

    Swal.fire({
      title: `<strong><u>Override Reason</u></strong>`,
      html: `${overriddenReason}`,
      showCloseButton: true,
      focusConfirm: false,
      confirmButtonText: `Go back!`,
    }).then(e=>{
      getAttendanceOverriddenInfo(attendanceId,staffName,date);
    });

  }

  function getAttendanceOverriddenInfo(attendanceId,staffName,date){
    const attendanceOverridenInfo=$(`#leave-overridden-info-${attendanceId}`).data("leave-over-info");
    var html = '<div class="mb-2"><h4>'+staffName+' ('+date+')</h4></div>';
    for(var i=0; i<attendanceOverridenInfo.length; i++) {
      // Format
      // <Action By name> has <Added Overridden status> on <Action on time> from <Source>
      html+=`
          <div style="border-bottom: 2px solid grey;padding: 3px;margin: 1px;text-align: left;">
            ${attendanceOverridenInfo[i].action_by} has ${attendanceOverridenInfo[i].action} on ${attendanceOverridenInfo[i].action_at}
            <br>
            <button class="btn btn-primary" id="overridden-reason-${i}" data-overridden-reason="${attendanceOverridenInfo[i].reason}" onclick="viewOverriddenReason(${i},${attendanceId},'${staffName}','${date}')">View Reason</button>
          </div>
      `;
    }

    Swal.fire({
      title: `<strong><u>Override information</u></strong>`,
      html: `${html}`,
      showCloseButton: true,
      focusConfirm: false,
      confirmButtonText: `Close!`,
    });
  }

  function constructAttendance(attendance) {
    var html = '';
    var bck = "";
    var status = "NA";

    const staff_attendance_algorithm = "<?php echo $this->settings->getSetting('staff_attendance_algorithm') ?>";

    for (let i in attendance) {
      // if (!attendance[i].attendance_id) continue;
      if(isDateToday(attendance[i].date)){
        continue;
      }

      const overriddenInfoBtn=`<br>
      <span id=leave-overridden-info-${attendance[i].attendance_id} data-leave-over-info='${JSON.stringify(attendance[i].check_in_out_info)}' style="cursor:pointer;" onclick="getAttendanceOverriddenInfo(${attendance[i].attendance_id},'${attendance[i].staff_name}','${attendance[i].date}')">(<u>Overridden</u>)<span>`;

      if (attendance[i].type == 2) {
        bck = "weekoff";
        status = `Week-Off ${attendance[i].is_manually_changed==1 ? overriddenInfoBtn : ""} ${(attendance[i].attendance_status=="P" || attendance[i].attendance_status=="HD") ? `<span style='color:green !important'>(Present ${attendance[i].attendance_status})</span>` : ""}`;
      } else if (attendance[i].type == 3) {
        status = `Holiday ${attendance[i].is_manually_changed==1 ? overriddenInfoBtn : ""} ${(attendance[i].attendance_status=="P" || attendance[i].attendance_status=="HD") ? `<span style='color:green !important'>(Present ${attendance[i].attendance_status})</span>` : ""}`;
        bck = "holiday";
      }else{
          if (attendance[i].attendance_status == "AB") {
            bck = "absent";
            status = `Absent ${attendance[i].is_manually_changed==1 ? overriddenInfoBtn : ""}`;
          } else if (attendance[i].attendance_status == "HD") {
            bck = "halfday";
            status = `Half-day ${attendance[i].is_manually_changed==1 ? overriddenInfoBtn : ""}`;
          } else if (attendance[i].attendance_status == "P" || attendance[i].attendance_status == "FD") {
            bck = "present";
            status = `Present ${attendance[i].is_manually_changed==1 ? overriddenInfoBtn : ""}`;
          }
      }
      
      var checkin = ' -';
      var checkout = ' - ';
      var is_checked_in_outside = '';
      var is_checked_out_outside = '';
      var checked_in_location_name = '';
      var checked_out_location_name = '';

      if (attendance[i].first_check_in_time != '') {
        checkin = attendance[i].first_check_in_time;
      }

      if (attendance[i].last_check_out_time != '') {
        checkout = `${attendance[i].last_check_out_time} ${attendance[i].is_auto_check_out}`;
      }

      if (attendance[i].check_in_out_info?.length) {
        if (attendance[i].first_check_in_time != '') {
          is_checked_in_outside = attendance[i].check_in_out_info[0].is_outside_campus == 1 ? "<br> Found Outside check-in" : "";

          if (attendance[i].check_in_out_info[0].location_name) {
            checked_in_location_name = `Loc: ${attendance[i].check_in_out_info[0].location_name}`;
          }
        }

        if (attendance[i].check_in_out_info?.length > 1 && attendance[i].last_check_out_time != '') {
          is_checked_out_outside = attendance[i].check_in_out_info.at(-1).is_outside == 1 ? "<br> Found Outside check-out" : "";

          if (attendance[i].check_in_out_info.at(-1).location_name) {
            checked_out_location_name = `Loc: ${attendance[i].check_in_out_info.at(-1).location_name}`;
          }
        }
      }

      html+=`<div id="attendance-data">`;

      html += `
        <table class="table">
          <tbody>
            <tr>
              <th colspan="2" style="text-align:center;background: #e6eff3;width: 44%;text-decoration:underline;">Details of ${attendance[i].date}</th>
            </tr>

            <tr>
              <th style="background: #e6eff3;width: 44%;">Shift start time</th>
              <td style="background: #f6f2d0;">${attendance[i].shift_start_time || "-"}</td>
            </tr>

            <tr>
              <th style="background: #e6eff3;width: 44%;">Shift end time</th>
              <td style="background: #f6f2d0;">${attendance[i].shift_end_time || "-"}</td>
            </tr>
            
            <tr>
              <th style="background: #e6eff3;width: 44%;">Attendance Status</th>`;
                // Display leave status if any here
              if(Object.entries(attendance[i]?.leave_details)?.length && +Object.values(attendance[i]?.leave_details)[0]?.total_leaves_taken_count!==0){
                const leaveDetails=Object.values(attendance[i]?.leave_details);
                  // If there are any leaves, the we need to display leave taken names in place of attendancee status
                  if(leaveDetails[0]){
                    const totalLeavesTakenNames={};
                    const leavesTaken=leaveDetails[0];
                    for(let leave in leavesTaken){
                      if(typeof leavesTaken[leave]==="object" && leavesTaken[leave].status>=0 && leavesTaken[leave].status<=2 ){
                        totalLeavesTakenNames[leavesTaken[leave]?.category_short_name]=leavesTaken[leave]?.category_short_name;
                      }
                    }

                    let leaveTakenNames="";
                    if(Object.keys(totalLeavesTakenNames)?.length){
                      leaveTakenNames=`On ${Object.keys(totalLeavesTakenNames).join(",")} ${(attendance[i].attendance_status=="P" || attendance[i].attendance_status=="HD") ? `<span style='font-weight: bold;color: green;'>(Present ${attendance[i].attendance_status}) ${attendance[i].is_manually_changed==1 ? overriddenInfoBtn : ""}</span>` : ""}`;
                    }

                    html += `<td>${leaveTakenNames}</td>`;
                  }
              }else{
                  html += `<td class="${bck}">${staff_attendance_algorithm=="consider_check_in_only" && status || isDateToday(attendance[i].date) && attendance[i].last_check_out_time && status || !isDateToday(attendance[i].date) && status || "NA"}</td>`;
              }
            html += `</tr>
            <tr>
              <th style="background: #e6eff3;width: 44%;">Leave Info</th>`;

      // Displaying leave information below
      if(Object.entries(attendance[i]?.leave_details)?.length){
        const leaveDetails=Object.values(attendance[i]?.leave_details)[0];

        html += `<td>
                      <a href="javascript:void(0)" class="link-primary" id="leave-info-${i}" data-leave-info='${JSON.stringify(leaveDetails)}' onclick="viewLeaveInfo(${i},'${attendance[i].staff_name}','${attendance[i].date}')">View info</a>
                </td>`;
      }else{
        html += '<td style="background: #f6f2d0;"> - </td>';
      }
      // Displaying leave info above

      html += `
            </tr>

            <tr>
              <th style="background: #e6eff3;width: 44%;">Check-in</th>
              <td style="background: #f6f2d0;">${checkin} ${is_checked_in_outside} <br> ${checked_in_location_name}</td>
            </tr>

            <tr>
              <th style="background: #e6eff3;width: 44%;">Check-out</th>
              <td style="background: #f6f2d0;">${checkout} ${is_checked_out_outside} <br> ${checked_out_location_name}</td>
            </tr>`;

      <?php if ($this->settings->getSetting('enable_staff_regularize_attendance')) { ?>
        html += `<tr>
                    <th style="background: #e6eff3;width: 44%;">Approved/Rejected Reason</th>`;

        if (attendance[i].leave_resolved != null) {
          html += `<td style="background: #f6f2d0;" id="${i}_approved_reason">${attendance[i]?.approved_reason || "-"}</td>`;
          if (attendance[i].leave_resolved == 1) {
            html+=`<tr>
              <th style="background: #e6eff3;width: 44%;">Action</th>`;
            html += `<td style="background: #f6f2d0;" id="${i}_approved_reason" disabled="true">Corrected</td>`;
          } else if (attendance[i].leave_resolved == 2) {
             html+=`<tr>
              <th style="background: #e6eff3;width: 44%;">Action</th>`;
            html += `<td style="background: #f6f2d0;" id="${i}_approved_reason" disabled="true">Rejected</td>`;
          } else {
             html+=`<tr>
              <th style="background: #e6eff3;width: 44%;">Action</th>`;
            html += `<td style="background: #f6f2d0;" id="${i}_approved_reason" disabled="true">Request Sent</td>`;
          }
          html += `</tr>`;
        } else {
          html += `<td style="background: #f6f2d0;" id="${i}_approved_reason">-</td> 
            </tr>

            <tr>
              <th style="background: #e6eff3;width: 44%;">Action</th>`;

          const isRegularizeAvail = isDateToday(attendance[i].date);

          if (!isRegularizeAvail) {
            if (attendance[i].on_leave==0 && (bck == "halfday" || bck == "absent")) {
              if (!+reporting_manager_available) {
                html += `<td style="background: #f6f2d0;">
                          Manager not assigned
                          </td>`;
              } else {
                html += `<td style="background: #f6f2d0;"><button id="${i}" style="font-size: 10px;" class="btn btn-info" onClick="regularizeLeave('${attendance[i].attendance_id}','${attendance[i].staff_id}','${attendance[i].date}','${i}','${attendance[i].attendance_status}',${attendance[i].shift_id})">Regularize Attendance</button></td>`;
              }
            } else {
              html += `<td style="background: #f6f2d0;">-</td>`;
            }
          } else {
            html += `<td style="background: #f6f2d0;">-</td>`;
          }

          html += `</tr>`;
        }
      <?php } ?>

      html += `</tbody>
        </table>
      </div>
      `;
    }
    return html;
  }

  function viewLeaveInfo(index,staffName,date){
    const leaveDetails=$(`#leave-info-${index}`).data("leave-info");
    let html='';
    
    if(Object.entries(leaveDetails)?.length){
      html += `<div>`;
      html += '<div class="mb-2"><h4>'+staffName+' ('+date+')</h4></div>';
      for(leave of Object.values(leaveDetails)){
        const leaveDurationType=+leave.leave_no_of_days>0.5 ? "Full" : "Half";
        const isLeaveConsideredAsPresent=`${+leave.consider_present==1 ? `<br> This ${leave.category_name} will be considered as Present` : ""}`;
        const leaveStaus=leaveStatusObject[leave.status];
        if(typeof leave=="object"){
          html += `
          <div style="border-bottom: 2px solid grey;padding: 3px;margin: 1px;text-align:left;">
            <span>${leave.leave_filed_by_name} have filed ${leaveDurationType} Day ${leave.category_name} (<span>${leaveStaus}</span>)
            <small>${isLeaveConsideredAsPresent}</small>`;

          let leaveRemarks="";
          if(leave.status==0){
            leaveRemarks=leave.leave_applied_remarks;
          }

          if(leave.status==1 || leave.status==2 || leave.status==3){
            leaveRemarks=leave.leave_approved_remarks;
          }

          if(leave.status==4){
            leaveRemarks=leave.leave_cancelled_remarks;
          }

          if(leaveRemarks?.length){
            html += `<br>
            <button style="cursor:pointer;" class="btn btn-primary" id="leave-remarks-${leave.leave_id}" data-leave-remarks="${leaveRemarks}" onClick="viewLeaveRemarks(${leave.leave_id},'${staffName}','${date}',${index})">View Remarks</button>`;
          }
          
          html+=`</div>`;
        }
      }
      html += `</div>`;
    }

    Swal.fire({
      title: `<strong><u>Information</u></strong>`,
      // icon: "info",
      html: `${html}`,
      showCloseButton: true,
      focusConfirm: false,
      confirmButtonText: `Close!`,
    });
  }

  function viewLeaveRemarks(leaveId,staffName,date,index){
    let leaveRemarks = '<div class="mb-2"><h4>'+staffName+' ('+date+')</h4></div>';
    leaveRemarks+=$(`#leave-remarks-${leaveId}`).data("leave-remarks");

    Swal.fire({
      title: `<strong><u>Leave remarks</u></strong>`,
      icon: "info",
      html: `${leaveRemarks}`,
      showCloseButton: true,
      focusConfirm: false,
      confirmButtonText: `Go back!`,
    }).then(e=>{
      viewLeaveInfo(index,staffName,date)
    })
  }
</script>