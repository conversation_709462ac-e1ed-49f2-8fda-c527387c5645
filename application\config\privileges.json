[{"name": "ACTIVITY", "sub_privilege": ["MODULE"]}, {"name": "ADDITIONAL_INCOME", "sub_privilege": ["MODULE", "ADD_CATEGORY", "ADD_INCOME", "VIEW_REPORTS"]}, {"name": "ASSETS", "sub_privilege": ["MODULE"]}, {"name": "BUILDING_MASTER", "sub_privilege": ["MODULE"]}, {"name": "OTHERLINKS", "sub_privilege": ["MODULE", "ADMIN"]}, {"name": "CIRCULARV2", "sub_privilege": ["MODULE", "CREATE", "VIEW_ALL", "REPORT", "APPROVER", "UNPUBLISH"]}, {"name": "COMMUNICATION", "sub_privilege": ["MODULE", "EMAIL_TEMPLATE", "SMS_TEMPLATE", "SMS_TEMPLATE_APPROVAL", "GROUP_CREATION", "REPORT", "RESEND_CIRCULAR"]}, {"name": "COMPETITION", "sub_privilege": ["MODULE", "ADD_EDIT_DELETE", "APPROVE", "VIEW", "DEFAULT_STAFF_INCHARGE"]}, {"name": "EMAILS", "sub_privilege": ["MODULE", "SEND", "VIEW_ALL"]}, {"name": "EXAMINATION", "sub_privilege": ["MODULE", "CREATE", "ACCESS_CONTROL", "PERFORMANCE_ANALYSIS", "CLASS_REPORT", "STUDENT_REPORT", "UNLOCK_MARKS_ENTRY", "ADD_REMARKS_CT", "CONSOLIDATION", "VERIFY_MARKS_CARDS", "ADD_REMARKS_ALL", "VERIFY_REMARKS", "EXAM_ADMIN", "REGENERATE_MARKSCARDS", "CREATE_ELECTIVES", "RANKING_REPORT", "STAFF_PERFORMANCE_INDEX", "SUBJECT_REMARKS", "VIEW_MARKS_STATUS", "VIEW_MARKS_CARD_ALL", "VIEW_MARKS_CARD_CT", "PUBLISH_MARKS_TO_PARENT", "RESULT_ANALYSIS_REPORT", "GENERATE_MARKS_AVERAGE"]}, {"name": "AFL", "sub_privilege": ["MODULE", "SUBJECTS", "ASSESSMENTS", "GRADING_SYSTEM", "RUBRICS", "MARKS", "ADMIN", "PERFORMANCE_POINTERS", "STUDENT_REPORT"]}, {"name": "ELIBRARY", "sub_privilege": ["MODULE", "ADMIN"]}, {"name": "EXPENSE", "sub_privilege": ["MODULE", "ADD_CATEGORY", "APPROVAL", "ADD_EXPENSE", "VIEW_REPORTS", "CANCELLED_VOUCHER", "APPROVAL_AMENDMENTS", "ADD_EXPENSE_AMENDMENTS_BUTTON", "APPROVE_REJECT_AMENDMENTS_BUTTON"]}, {"name": "FEESV2", "sub_privilege": ["MODULE", "COLLECT_FEES", "VIEW_DAILY_TX_REPORT", "VIEW_BALANCE_REPORT", "VIEW_ONLINE_TX_REPORT", "VIEW_ONLINE_CHALLAN_TX_REPORT", "ASSIGN_INVOICE", "VIEW_CONCESSIONS", "FEE_SUMMARY", "NON_RECONCILED", "CLASS_WISE_REPORT", "FAST_COLLECTION", "TRANSPORTATION_CRUD", "WIDGET", "CANCELED_REPORT", "CANCELED_REPORT", "SOFT_DELETE_RECEIPTS", "RECEIPT_CHANGE", "CONSOLIDATED_FEE_REPORT", "CLASS_WISE_DAILY_REPORT", "FINE_AMOUNT_ASSIGN", "FINE_AMOUNT_WAIVER", "VIEW_DAILY_TX_REPORT_PRARTHANA", "CONCESSIONS_ASSIGN", "DAY_BOOK_APS", "REFUND", "FEES_COLLECTION_STATUS", "DAY_BOOK_APS_COM", "RE_GENERATE_PDF_RECEIPTS", "REFUND_REPORTS", "MANAGEMENT_SUMMARY", "ADJUSTMENT_REPORT", "OVERVIEW_ONLINE_SETTLEMENT_REPORT", "ONLINE_SETTLEMENT_REPORT", "FEE_STUDENT_HISTORY", "FEE_AUDIT_LOG", "TRANSPORTATION_REPORT", "PREDEFINED_CONCESSION", "FEE_SUMMARY_REPORT", "EDIT_FEES_AMOUNT", "REFUND_TRANSACTION", "EXCESS_REPORT", "INVOICE_GENERATE", "EXCESS_AMOUNT", "FEE_COMPONENT_DETAIL_REPORT", "CLASSWISE_STUDENT_FEE_COUNT", "STATEMENT_GENERATE", "FEES_RECEIPT_SEND", "VIEW_ONLINE_CHALLAN_REPORT", "VIEW_DAILY_ONLINE_TX", "CLASS_WISE_DATE_WISE_REPORT", "FAST_COLLECTION_INSTALLMENT_WISE", "ONLINE_PAYMENT_REFUND", "FINE_WAIVER_REPORT", "VIEW_CONCESSIONS_DATE_WISE", "PREDEFINED_FILTERS", "MASS_FEE_ASSISGNED_STUDENT_WISE", "CONCESSIONS_ASSIGN_APPROVER", "CONCESSIONS_APPROVAL", "FEES_EDIT_HISTORY_REPORT", "MASS_INVOICE_STATEMENT", "TALLY_DAILY_REPORT", "SINGLE_WINDOW_APPROVAL_PROCESS", "CREATE_INVOICES", "ONLINE_REFUND_TX_REPORT", "FEE_COLLECTION_SUMMARY_DATE_WISE", "STUDENT_WISE_PREDEFINED_FILTERS", "VIEW_FEE_COLLECTION_PAGE_ADVANCED_DETAILS"]}, {"name": "FLASH_NEWS", "sub_privilege": ["MODULE"]}, {"name": "GALLERY", "sub_privilege": ["MODULE", "VIEW_EDIT_DELETE"]}, {"name": "HELIUM", "sub_privilege": ["MODULE", "ADMIN", "COURSE", "VIEW_SESSIONS", "MODIFY_COURSE", "BATCH", "MODIFY_BATCH", "MODIFY_SESSION", "VIEW_REGISTRATIONS", "VIEW_RESOURCES", "ADD_RESOURCES", "ADD_RECORDING", "REMOVE_RECORDING"]}, {"name": "HOMEWORK", "sub_privilege": ["MODULE", "HOMEWORK_ADMIN"]}, {"name": "STUDENT_TASKS", "sub_privilege": ["MODULE", "TASKS_ADMIN", "QUESTION_BANK"]}, {"name": "ACADEMICS", "sub_privilege": ["MODULE"]}, {"name": "DONATION", "sub_privilege": ["MODULE", "DONATION_ADMIN", "ADD", "VIEW_REPORTS"]}, {"name": "EVENT", "sub_privilege": ["MODULE", "EVENT_CRUD", "EVENT_ATTENDANCE", "CREATE_EVENT", "VIEW_EVENT_DETAILS", "RETURN_SAFETY_DEPOSIT"]}, {"name": "WALLET", "sub_privilege": ["MODULE", "LOAD_MONEY", "VIEW_WALLET_DETAILS", "ADD_TRANSACTION", "DELETE_TRANSACTION"]}, {"name": "INVENTORY", "sub_privilege": ["MODULE"]}, {"name": "INTERNAL_TICKETING", "sub_privilege": ["MODULE", "CREATE", "VIEW_REPORTS", "INTERNAL_TICKETING_ADMIN", "CREATE_TICKET_ON_BEHALF"]}, {"name": "INFIRMARY", "sub_privilege": ["MODULE", "CREATE", "VIEW_REPORTS", "INFIRMARY_ADMIN", "MEDICAL_EXPENCES", "MEDICAL_EXPENSE_MASS"]}, {"name": "LESSON_PLAN", "sub_privilege": ["MODULE", "ADMIN", "MANAGE_SYLLABUS", "PLAN_SYLLABUS_SCHEDULE", "DESIGN_SESSION", "CHECK_IN_OUT_SESSION", "LMS_ROLLBACK_PRIVILEGE", "ADD_SUBJECTS", "CLONE_OPTIONS", "DELETE_ADDED_SUBJECTS"]}, {"name": "LIBRARY", "sub_privilege": ["MODULE", "VIEW_EDIT_DELETE", "STAFF_VIEW", "BOOKS", "BORROW_RETURN", "FINE_COLLECTION", "BULK_CURCULATION", "DAMAGE_OR_LOST_BOOK", "STOCK_CHECK", "DAILY_TX_REPORT", "BOOK_BORROWED_REPORT", "FINE_DEFAULTER_REPORT", "STOCK_REPORT", "MEMBER_TX_REPORT", "GENERATE_QR_CODES", "ASSIGN_CARD", "BULK_ASSIGN_CARD", "CARD_MASTER", "BORROW_RETURN_RENEWAL"]}, {"name": "MANAGEMENT", "sub_privilege": ["MODULE"]}, {"name": "MSM", "sub_privilege": ["MODULE", "ACTIVITY_TRACKER", "BOARDING", "STUDENT_ANALYTICS", "FEE_COLLECTION", "FEE_SUMMARY", "STAFF_ATTENDANCE", "STUDENT_ATTENDANCE", "LEAD_MANAGEMENT", "PARENT_TICKETING", "INTERNAL_TICKETING", "ADMISSIONS", "INDUS_COGNITENSOR_DASHBOARD", "STAFF_REPORT", "INFIRMARY_ANALYTICS", "STAFF_LEAVE_APPROVE"]}, {"name": "ONLINE_CLASS", "sub_privilege": ["MODULE", "CREATE_SCHEDULE", "ADMIN"]}, {"name": "ONLINE_CLASS_V2", "sub_privilege": ["MODULE", "CREATE_SCHEDULE", "ADMIN", "REPORTS"]}, {"name": "PARENT_TICKETING", "sub_privilege": ["MODULE", "CRUD_CATEGORY", "ADD_OFFLINE_TICKET", "TICKETING_ADMIN", "TICKET_ANALYTICS", "STAFF_TICKET_REPORT", "STUDENT_TICKET_REPORT"]}, {"name": "PAYROLL", "sub_privilege": ["MODULE", "VIEW_MY_PAYSLIPS", "VIEW_TAX_DECLARATION", "MANAGE_TAX_DECLARATION", "PAYROLL_ADMIN", "VIEW_REPORTS", "DISBURSEMENT", "PF_REPORTS", "PT_REPORTS", "ESI_REPORTS", "ENTITLED_SALARY", "MASS_PAYSLIP_GENERATION", "STAFF_WISE_REPORT", "RE_GENERATE_PAYSLIP", "PAYROLL_HISTORY_REPORT", "MONTHWISE_TDS_DEDUCTEE_REPORT", "INCREMENTS_REPORT", "ADD_INCREMENTS_PREVIOUS_MONTH"]}, {"name": "PROCUREMENT", "sub_privilege": ["MODULE", "REQUISITION", "REPORTS", "REQUISITION_V2"]}, {"name": "INDENT", "sub_privilege": ["ADMIN", "VIEW_INDENT"]}, {"name": "PROCUREMENT_VENDORS", "sub_privilege": ["MODULE"]}, {"name": "PROCUREMENT_ASSETS", "sub_privilege": ["MODULE"]}, {"name": "PROCUREMENT_INVENTORY", "sub_privilege": ["MODULE"]}, {"name": "PROCUREMENT_SERVICE_CONTRACT", "sub_privilege": ["MODULE"]}, {"name": "PROCUREMENT_BUDGET", "sub_privilege": ["MODULE", "MANAGE_OVERALL_BUDGET", "MANAGE_DEPARTMENT_BUDGET", "CFO_CEO_APPROVER"]}, {"name": "PROCUREMENT_SALES", "sub_privilege": ["MODULE", "SALES", "SALES_RETURN", "REPORTS", "PRE_DEFINED_TEMPLATES", "DELETE_ITEMS_IN_TRANSACTION", "NON_RECONCILE_REPORT", "STUDENT_WISE_MISSING_ORDER_REPORT", "STUDENT_WISE_ORDER_HISTORY", "PARENT_ORDERS_SUMMARY_REPORT", "PARENT_ORDERS_REPORT"]}, {"name": "PROCUREMENT_VOUCHER", "sub_privilege": ["MODULE", "VIEW_DETAILS", "CREATE_OR_EDIT", "APPROVE"]}, {"name": "PROCUREMENT_DELIVERY_V2", "sub_privilege": ["MODULE", "VIEW_DETAILS", "CREATE_OR_EDIT"]}, {"name": "PROCUREMENT_INVOICE", "sub_privilege": ["MODULE", "VIEW_DETAILS", "CREATE_OR_EDIT", "APPROVE"]}, {"name": "PURCHASE", "sub_privilege": ["MODULE"]}, {"name": "QUESTION_BANK", "sub_privilege": ["MODULE"]}, {"name": "ROOM_BOOKING", "sub_privilege": ["ALLOW_ALL_ROOMS", "ALLOW_ROOM_BOOKING"]}, {"name": "SCHOOL", "sub_privilege": ["MODULE", "ACAD_YEAR_CHANGE", "STAFF_QR_CODES", "STUDENT_QR_CODES", "STUDENT_PHOTOS", "PARENT_QR_CODES", "CLASS_MASTER", "CLASS_MASTERV2", "PERMIT_ASSING_STOP", "CLASS_SECTION", "SEMESTER", "ORDER_OF_SECTION", "ORDER_OF_CLASS", "ORDER_OF_MASTER_CLASS", "CHANGE_CASE_OF_NAME"]}, {"name": "SCHOOL_CALENDAR", "sub_privilege": ["MODULE"]}, {"name": "STAFF", "sub_privilege": ["ADD_EDIT_DELETE", "VIEW_DETAILS", "VIEW", "MODULE", "VIEW_SMS_REPORT", "VIEW_REPORT", "VIEW_QUALIFICATION", "ATTENDANCE", "PROVISION_USER_LOGIN", "REPORTING_MANAGER_ADMIN", "PAYROLL_DETAILS", "MANAGE_DOCUMENTS", "STAFF_TRANING_WORKSHOP", "STAFF_PUBLICATIONS_CITAT", "STAFF_INTERESTS", "MANAGE_EXPERIENCE", "MANAGE_AWARDS", "MANAGE_QUALIFICATION", "STAFF_INITIATIVES", "STAFF_EXIT", "VIEW_AUDIT_REPORT", "STAFF_DOCUMENT_REPORT", "MANAGE_PROFILE_DISPLAY", "MANAGE_PROFILE_EDITS", "APPROVE_ATTRIBUTE_UPDATES", "PROVISION_STAFF_NAME_CHANGE", "MANAGE_DEPARTMENTS", "MANAGE_DESIGNATIONS", "STAFF_DATA_MISSING_REPORT", "STAFF_EDIT_HISTORY_REPORT", "PROVIDE_CONSENT", "MANAGE_STAFF_CONSENT", "CONFIGURE_STAFF_PROFILE_FIELDS", "CONFIGURE_LOCK_UNLOCK_STAFF_PROFILE", "DOCUMENTS_MASS_UPLOAD"]}, {"name": "STAFF_360_DEGREE", "sub_privilege": ["MODULE", "ATTENDANCE", "LEAVE_RECORDS", "ASSIGNED_TASKS", "QUALIFICATION", "AWARDS", "EXPERIENCE", "DOCUMENTS", "TRAININGS", "INTERESTS", "PUBLICATIONS", "CIRCULARS", "MESSAGES", "INITIATIVES", "INFIRMARY", "PAYROLL_DETAILS"]}, {"name": "STAFF_ATTENDANCE", "sub_privilege": ["MODULE", "CREATE_SHIFT", "ASSIGN_SHIFT", "MANAGE_ATTENDANCE", "ADMIN", "DAY_WISE_REPORT", "LATE_REPORT", "INDIVIDUAL_STAFF_REPORT", "OVERRIDE_REPORT", "REDRIVE_ATTENDANCE", "MANAGE_GEOFENCE", "RECTIFY_ATTENDANCE_TOOL", "DEDUPE_ATTENDANCE_TOOL", "CHECK_IN_REPORT", "STAFF_REGULARIZE_LEAVE", "EXPORT_PERMISSION", "EDIT_ATTENDANCE", "MODIFY_PREVIOUS_SHIFTS", "STAFF_ATTENDANCE_APPROVE_FACE_CHECKIN", "STAFF_ATTENDANCE_EXCEPTION_ATTENDANCE_APPROVAL", "STAFF_ATTENDANCE_USE_LOCATION_CHECKIN", "STAFF_ATTENDANCE_USE_FACE_CHECKIN"]}, {"name": "STAFF_MASSUPDATE", "sub_privilege": ["MODULE", "MASS_DOCUMENT_UPLOAD"]}, {"name": "STAFF_OBSERVATION", "sub_privilege": ["CREATE", "VIEW_SUMMARY", "MODULE", "VIEW_ALL", "VIEW"]}, {"name": "STAFF_PROFILE", "sub_privilege": ["VIEW"]}, {"name": "STAFF_TASKS_BASKET", "sub_privilege": ["MODULE", "MY_PROJECTS", "MY_TASKS", "REPORTS", "MANAGE_TEAMS", "MANAGE_STAFF_TASK_TYPES"]}, {"name": "STUDENT", "sub_privilege": ["ADDRESS_CRUD", "HEALTH_CRUD", "BASIC_VIEW", "DETAIL_VIEW", "MODULE", "COMMUNICATION_CRUD", "FULL_INFO_CRUD", "NEW_STUDENT_ADD", "MOTHER_TONGUE_CRUD", "MOTHER_TONGUE_CRUD_IF_CT", "HEALTH_CRUD_IF_CT", "ADDRESS_CRUD_IF_CT", "COMMUNICATION_CRUD_IF_CT", "DETAIL_VIEW_IF_CT", "ADD_SIBLINGS", "VIEW_SMS_REPORT", "VIEW_REPORT", "VIEW_CLASS_REPORT", "SCHOOL_DETAILS", "DOCUMENTS", "TEMP_PASSWORD_RESET", "VIEW_TOTAL_CLASS_WISE_STUDENT_REPORT", "GUARDIAN_INFO_CRUD", "VIEW_ALUMNI_LIST", "EXIT_STUDENT", "CATEGORY_REPORT", "TEMP_DEACTIVATION", "ADJUST_FEE_AMOUNT", "ASSIGN_FEES", "CONNECT_SIBLINGS", "PROVISION_PARENTS_CREDENTIALS", "MANAGE_ONLINE_CREDENTIALS", "VIEW_TRANSPORT_REPORT", "VIEW_VACCINATION_STATUS", "FEE_PAID_REPORT", "CATEGORY_WISE_STUDENT_REPORT", "MAP_RFID", "STUDENT_DOCUMENT_REPORT", "STUDENT_AADHAR_REPORT", "STUDENT_PAN_REPORT", "STUDENT_PREVIOUS_SCHOOL_REPORT", "STUDENT_DATA_MISSING_REPORT", "STUDENT_DOCUMENTS_DELETE", "STUDENT_EDIT_HISTORY_REPORT", "DOCUMENTS_MASS_UPLOAD", "MASS_ASSIGN_CLASS_SECTION", "SINGLE_WINDOW_REPORT", "CONFIGURE_PARENT_SIDE_FIELDS", "LOCK_UNLOCK_PROFILE", "SIBLING_REPORT", "DOCUMENTS_UPLOAD_REMINDER_EMAIL", "PHOTO_DOCS_MASS_UPLOAD"]}, {"name": "STUDENT_360", "sub_privilege": ["MODULE", "SCHOOL_DETAILS", "DOCUMENTS", "PREV_SCHOOL_DETAILS", "STUDENT_HEALTH", "STUDENT_MEDICAL_FORM", "STUDENT_CONSENT_FORMS", "FEES", "CIRCULARS", "SMS", "ATTENDANCE", "EXAMINATION", "OBSERVATION", "ACADEMICANALYSIS", "NON_COMPLIANCE", "TRANSPORTATION", "RFID_REPORT", "SINGLE_WINDOW_PROCESS", "PERSONAL_DETAILS_EXTENDED"]}, {"name": "STUDENT_ATTENDANCE", "sub_privilege": ["SHOW_ALL_SECTIONS", "TAKE", "EMERGENCY_EXIT", "HEALTH_CARE", "LATECOMER", "FRESH_ENTRY", "MODULE", "EDIT_ANY_DATE_ATTENDANCE", "VIEW_SUMMARY", "CLASS_ATTENDANCE_REPORT", "SPECIAL_CASE_REPORT", "ATTENDANCE_NOT_TAKEN_REPORT"]}, {"name": "STUDENT_ATTENDANCE_V2", "sub_privilege": ["EDIT_ATTENDANCE_ALLSUBJECTS", "TAKE_ATTENDANCE", "REPORTS", "EDIT_ATTENDANCE_SUBJECTWISE", "TAKE_PREVIOUS_DATE_ATTENDANCE"]}, {"name": "CALENDAR_EVENTS_V2", "sub_privilege": ["MODULE", "MANAGE_CALENDAR", "ASSIGN_CALENDAR", "REPORTS", "ENABLE_CONFIG"]}, {"name": "STUDENT_MASSUPDATE", "sub_privilege": ["MODULE", "STUDENT_ADDRESS_MASSUPDATE"]}, {"name": "STUDENT_OBSERVATION", "sub_privilege": ["MODULE", "ADD", "DEACTIVATE", "OBSERVATION_REPORT", "SECTION_SUMMARY_REPORT", "STUDENT_OBSERVATION_REPORT", "MANAGE_CATEGORIES", "MANAGE_CAPACITY", "PREDEFINED_REMARKS", "MASS_OBSERVATION"]}, {"name": "STUDENT_NONCOMPLIANCE", "sub_privilege": ["MODULE", "ADD", "DEACTIVATE", "MANAGE_CATEGORIES", "MANAGE_PENALTY", "NONCOMPLIANCE_REPORT", "STUDENT_NONCOMPLIANCE_REPORT", "SECTION_SUMMARY_REPORT", "ADMIN", "RESOLVE_ANY_NON_COMPLIANCE"]}, {"name": "STUDENT_PROFILE", "sub_privilege": ["VIEW", "VIEW_ACTIVITY_DETAILS", "VIEW_EMERGENCY_INFO", "VIEW_FEES_DETAILS", "VIEW_HEALTH_DETAILS", "VIEW_PERSONAL_DETAILS", "VIEW_SCHOOL_DETAILS", "VIEW_TRANSPORT_DETAILS"]}, {"name": "STUDENT_PROMOTION", "sub_privilege": ["MODULE"]}, {"name": "SUBJECT", "sub_privilege": ["MASTER", "ELECTIVE_MASTER", "STUDENT_ELECTIVE"]}, {"name": "SUBSTITUTION", "sub_privilege": ["MODULE"]}, {"name": "INDUS_SUY", "sub_privilege": ["MODULE"]}, {"name": "TIMETABLE", "sub_privilege": ["MY_TIMETABLE", "MODULE", "SUBJECTS", "SETTINGS", "CREATE_TIMETABLE", "CLONE_TIMETABLE", "STAFF_TIMETABLE_REPORT", "STAFF_WORKLOAD_REPORT", "SECTION_TIMETABLE_REPORT", "ROOM_TIMETABLE_REPORT", "FREEZE_SUBJECT_ALLOCATION", "AUTO_ALLOCATE_TIMETABLE", "MOVE_STAFF"]}, {"name": "TODO", "sub_privilege": ["MODULE"]}, {"name": "TRANSPORTATION", "sub_privilege": ["MODULE", "BUSES", "ROUTES", "STOPS", "ENTITIES", "JOURNEYS", "DRIVERS", "TAKE_ATTENDANCE", "TX_REPORT", "ADMIN_CONSOLE", "TX_MISMATCH_REPORT", "TX_JOURNEY_CHANGES", "TX_REPORT", "TX_DAILY_TRACK", "TX_DAILY_TRACK_SUMMARY", "TX_STUDENT_WISE_REPORT", "TX_AVG_JOURNEY_TIME", "NOTIFICATION_LOGS", "EXPORT_STUDENT_DATA", "EXPORT_STAFF_DATA"]}, {"name": "UPCOMING_BIRTHDAY", "sub_privilege": ["VIEW", "VIEW_STAFF"]}, {"name": "USER_MANAGEMENT", "sub_privilege": ["MODULE", "PROVISION_PARENTS", "PROVISION_STAFF", "CONNECT_SIBLINGS", "ASSIGN_ROLES_AND_PRIVILEGES", "ACTIVATE_PARENTS", "ACTIVATE_PARENTS_REPORTS", "ACCESS_CONTROL_HISTORY"]}, {"name": "VENDOR", "sub_privilege": ["MODULE", "CRUD"]}, {"name": "VISITOR", "sub_privilege": ["MODULE", "SECURITY_REGISTRATION", "STAFF_PREREGISTRATION", "STAFF_APPROVAL", "VIEW_REPORTS", "SWITCH_SCHOOL"]}, {"name": "LEAVE", "sub_privilege": ["MODULE", "STUDENT_LEAVE_APPLY", "APPLY_STUDENT_LEAVE_FOR_PREVIOUS_DATES", "STUDENT_LEAVE_APPROVE", "STAFF_LEAVE_APPROVE", "STAFF_LEAVE_APPLY", "LEAVE_REPORT", "APPLY_LEAVE_FOR_OTHER_STAFF", "STAFF_LEAVE_ADMIN", "STAFF_LEAVE_CATEGORY", "STAFF_LEAVE_QUOTA", "STUDENT_LEAVE_ADMIN", "COMPENSATION_STAFF_LEAVE_APPLY", "COMPENSATION_STAFF_LEAVE_APPROVE", "STAFF_LEAVE_YEARLY_REPORT", "ENABLE_HALF_DAY_STAFF_LEAVE_APPLY", "ENABLE_HALF_DAY_STAFF_LEAVE_APPLY_3_LEVEL", "ENABLE_STAFF_LEAVES_LOP_REPORT", "ENABLE_STAFF_LEAVE_CONVERSION"]}, {"name": "TEXTING", "sub_privilege": ["SEND", "REPORT"]}, {"name": "TRANSPORTATION_REQUEST", "sub_privilege": ["MODULE"]}, {"name": "SALES", "sub_privilege": ["MODULE", "SOFT_DELETE_RECEIPTS", "NEW_STUDENT_SALES_REPORT", "NON_RECONCILE_REPORT"]}, {"name": "ADMISSION", "sub_privilege": ["MODULE", "REPORTS", "FIELD_SELECTION", "SETTINGS", "VIEW_EDIT_APPLICATION", "UPDATE_DETAILS", "APPROVED_SEND_SMS", "ONLINE_SETTLEMENT_REPORT", "ONLINE_TRANSACTION_REPORT", "MOVE_TO_ERP", "CREATE_OFFLINE_APPLICATION", "FOLLOWUP", "PRINT_EMPTY_APPLICATION", "VIEW_DETAILS", "MOVE_TO_ERP_INDIVIDUAL", "CAN_ADMIT_REJECT", "COLLECT_OFFLINE_FEE", "SEAT_ALLOTMENT", "EDIT_APPLICATION_FORM", "CREATE_OFFERS", "JOINING_FORMS", "INDUS_APPLICATION_REPORT", "EDIT_HISTORY", "LINK_TO_ENQUIRY", "ASSIGN_COUNSELOR", "ADMISSION_ANALYSIS", "GENERATE_APPLICATION", "CHANGE_ACADEMIC_YEAR", "RECEIPT_CANCLED_REPORT", "MASS_EMAIL", "CLOSE_APPLICATION", "ENABLE_COMPLETED_INTERACTIONS", "ACTIVITY_REPORT", "COUNSELOR_ADMIN", "REVERT_SUBMISSION"]}, {"name": "ENQUIRY", "sub_privilege": ["MODULE", "ACAD_YEAR_CHANGE", "ENQUIRY_ADMIN", "SHOW_COUNSELOR_LIST", "MASS_EMAIL", "DEDUPE_ENQUIRIES"]}, {"name": "VIRTUAL_CLASSROOM", "sub_privilege": ["MODULE", "CREATE_SCHEDULE", "ADMIN"]}, {"name": "BIRTHDAY_SMS_SEND", "sub_privilege": ["MODULE"]}, {"name": "IDCARDS", "sub_privilege": ["MODULE"]}, {"name": "WIDGET", "sub_privilege": ["STAFF_DATA", "STUDENT_DATA", "TASK_ASSIGNED", "TASK_SUMMARY", "FEE_COLLECTION_TOTAL", "FEE_COLLECTION_TREND", "STUDENT_COUNT", "COMMUNICATION_TREND", "MY_TIMETABLE", "STUDENT_ATTENDANCE_V2_SUMMARY", "REPORTING_MANAGER", "OTHER_LINKS", "GALLERY", "TICKETING", "PARENT_TICKETING", "STUDENT_COUNT_GENDERWISE", "ENQUIRY_WIDGET", "INTERNAL_TICKETING", "STAFF_ON_LEAVE_WIDGET", "STUDENT_NON_COMPLIANCE", "STUDENT_NON_COMPLIANCE_STATISTICS", "STUDENT_OBSERVATION", "STUDENT_WIDGET_STATISTICS", "STUDENT_COUNCELLING_TREND", "GET_APPROVAL_WIDGET_DATA", "STAFF_LEAVES_DETAILS", "MONTH_WISE_STAFF_CALENDAR", "INFIRMARY_VISITOR_WIDGET_DATA", "ATTENDANCE_CHECKIN_WIDGET", "VISITOR", "INFIRMARY_STATISTICS_WIDGET", "STAFF_ANNIVERSARY_WIDGET", "ENQUIRY_STATISTICS_WIDGET", "LIBRARY_STATISTICS_WIDGET", "TRANSPORTATION_STATISTICS_WIDGET", "INVENTORY_STATISTICS_WIDGET", "BOOKS_TREND_WIDGET", "RFID_WIDGET", "STUDENT_COUNSELLING_STAT", "STUDENT_CHECKIN_TRACKING", "SUBSTITUTION_STATISTICS", "TASK_BASKET", "STAFF_ATTENDANCE_REPORTS_WISE", "ADMISSION_STATISTICS_WIDGET", "SINGLE_WINDOW", "CATEGORY_STOCK_DETAILS_WIDGET", "DAILY_PLANNER", "ID_CARDS", "STUDENT_DAY_ATTENDANCE_V2_SUMMARY"]}, {"name": "STUDENT_WALLET", "sub_privilege": ["MODULE"]}, {"name": "STUDENT_TRACKING", "sub_privilege": ["MODULE", "MANAGE_ESCORTS", "FACILITY_ENTRY", "VIEW_REPORTS", "TRACKING_ADMIN"]}, {"name": "ITARI", "sub_privilege": ["MODULE"]}, {"name": "STAFF_RECRUITMENT", "sub_privilege": ["MODULE", "BUDGETED_HEADCOUNT", "VACANCIES", "MASS_UPLOAD_RESUMES", "MANAGE_CANDIDATES", "MANAGE_INTERVIEW_PROCESS", "TAKE_INTERVIEW"]}, {"name": "STUDENT_COUNSELLING", "sub_privilege": ["MODULE", "STUDENT_WISE_REPORT", "COUNCELLING_REPORT", "GRADE_WISE_REPORT", "COUNSELLING_ANALYTICS", "ADMINISTRATION", "MY_WEEKLY_REPORT", "COMMENTS_REPORT"]}, {"name": "CLASSROOM_CHRONICLES", "sub_privilege": ["MODULE"]}, {"name": "STUDENT_EXIT_FLOW", "sub_privilege": ["MODULE", "ADMIN"]}, {"name": "STUDENT_EXIT_FLOW_STAFF", "sub_privilege": ["MODULE", "ADMIN"]}, {"name": "BOARDING", "sub_privilege": ["MODULE", "CAMPUS_MANAGEMENT", "ROOM_TYPE", "MANAGE_WARDEN", "MANAGE_OBSERVATION_CATEGORIES", "MANAGE_ATTENDANCE_SESSIONS", "BOARDING_CAMPUS_REPORT", "BLOCK_WISE_REPORT", "BED_ALLOTMENT_REPORT", "BED_VACANCY_REPORT", "OBSERVATION_REPORT", "ATTENDANCE_REPORT", "ALLOCATION", "MY_OBSERVATIONS", "DEACTIVATE_OBSERVATION", "TAKE_ATTENDENCE"]}, {"name": "STUDENT_DAY_ATTENDANCE_V2", "sub_privilege": ["MODULE", "TAKE_ATTENDANCE", "STUDENT_DAY_ATTENDANCE_V2_ADMIN", "EMERGENCY_EXIT", "ATTENDANCE_ABSENT_REASONS", "ATTENDANCE_REPORT", "LATE_COMER_REPORT", "EMERGENCY_EXIT_REPORT", "CALENDER_EVENTS", "ATTENDANCE_NOT_TAKEN_REPORT", "STUDENT_CONSECUTIVE_ABSENT_REPORT", "MONTH_WISE_ATTENDANCE_REPORT", "TAKE_ATTENDANCE_FOR_PREVIOUS_DATES", "TAKE_ALL_SECTION_ATTENDANCE", "SPECIAL_CASE_REPORT"]}, {"name": "DAILY_PLANNER", "sub_privilege": ["MODULE", "DAILY_PLANNER_CALENDAR", "MANAGE_STAFF_TASK_TYPES", "ENABLE_STYLUS_PEN"]}, {"name": "STUDENT_CERTIFICATE", "sub_privilege": ["MODULE", "ADD_NEW_CERTIFICATE_TEMPLATE", "CLONE_CERTIFICATE_TEMPLATE", "EDIT_CERTIFICATE_TEMPLATE", "ISSUE_CERTIFICATES", "CERTIFICATES_REPORT", "MANAGE_CERTIFICATES_TEMPLATE"]}, {"name": "ID_CARDS", "sub_privilege": ["MODULE", "MANAGE_ID_CARD_TEMPLATE", "MANAGE_ID_CARD_ORDERS", "APPROVE_ID_CARDS"]}]