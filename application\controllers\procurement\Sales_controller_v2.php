<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON><PERSON>
 *          <EMAIL>
 *
 * Created:  July 3, 2023
 *
 * Description:  .
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

class Sales_controller_v2 extends CI_Controller {

	public function __construct() {
    parent::__construct();
		if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('PROCUREMENT_SALES')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('procurement/Sales_model_v2');
    $this->load->model('student/Student_Model');
    $this->load->library('filemanager');
    $this->config->load('form_elements');
  } 

  public function index(){
    $site_url = site_url();
    $data['tiles'] = array(
        [
          'title' => 'Sales',
          'sub_title' => 'Collect sales for students',
          'icon' => 'svg_icons/sales.svg',
          'url' => $site_url.'procurement/sales_controller_v2/sales_collect',
          'permission' => $this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.SALES')
        ],
        [
          'title' => 'Sales Return',
          'sub_title' => 'Sales return and refund',
          'icon' => 'svg_icons/sales.svg',
          'url' => $site_url.'procurement/sales_controller_v2/sales_return_view',
          'permission' => $this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.SALES_RETURN')
        ],
        [
          'title' => 'Mass Item Issue (Super Admin)',
          'sub_title' => 'View & Print receipts (Only for existing students)',
          'icon' => 'svg_icons/daybook.svg',
          'url' => $site_url.'procurement/sales_controller_v2/mass_item_issue',
          'permission' => $this->authorization->isSuperAdmin()
        ]
        // ,
        // [
        //   'title' => 'Tests With Queries (Super Admin)',
        //   'sub_title' => 'View & Print receipts (Only for existing students)',
        //   'icon' => 'svg_icons/daybook.svg',
        //   'url' => $site_url.'procurement/sales_controller_v2/test_with_queries',
        //   'permission' => $this->authorization->isSuperAdmin()
        // ]
    );
    $data['tiles'] = checkTilePermissions($data['tiles']);

    $data['report_tiles'] = array(
        [
          'title' => 'Daily Transaction Report',
          'sub_title' => 'Daily Transaction',
          'icon' => 'svg_icons/dailytransaction.svg',
          'url' => $site_url.'procurement/sales_controller_v2/daily_transcation_sales',
          'permission' => $this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.REPORTS')
        ],
        [
          'title' => 'Sales History Report',
          'sub_title' => 'View & Print receipts (Only for existing students)',
          'icon' => 'svg_icons/daybook.svg',
          'url' => $site_url.'procurement/sales_controller_v2/sales_history',
          'permission' => $this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.SALES')
        ],
        [
          'title' => 'Cancellation Report',
          'sub_title' => 'View cancelled receipt.',
          'icon' => 'svg_icons/cancelationreport.svg',
          'url' => $site_url.'procurement/sales_controller_v2/receipt_canceled_report',
          'permission' => $this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.REPORTS')
        ],
        [
          'title' => 'Sales Return Report',
          'sub_title' => 'Sales Return Report',
          'icon' => 'svg_icons/cancelationreport.svg',
          'url' => $site_url.'procurement/sales_controller_v2/sales_return_report',
          'permission' => $this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.REPORTS')
        ],
        [
          'title' => 'Student Wise Sales Report',
          'sub_title' => 'Student Wise Sales/Return Report',
          'icon' => 'svg_icons/cancelationreport.svg',
          'url' => $site_url.'procurement/sales_controller_v2/student_wise_sales_report',
          'permission' => $this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.REPORTS')
        ],
        [
          'title' => "Item Wise Student's Sales Report",
          'sub_title' => 'Category Wise Students Sales Report',
          'icon' => 'svg_icons/cancelationreport.svg',
          'url' => $site_url.'procurement/sales_controller_v2/category_wise_student_sales_report',
          'permission' => $this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.REPORTS')
        ],
        [
          'title' => "Student-wise Missing Order Report",
          'sub_title' => 'Student-wise Missing Order Report',
          'icon' => 'svg_icons/cancelationreport.svg',
          'url' => $site_url.'procurement/sales_controller_v2/student_wise_missing_order_report',
          'permission' => $this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.STUDENT_WISE_MISSING_ORDER_REPORT')
        ],
        [
          'title' => "Student-wise Orders History",
          'sub_title' => 'Student-wise Orders History',
          'icon' => 'svg_icons/cancelationreport.svg',
          'url' => $site_url.'procurement/sales_controller_v2/student_wise_order_history',
          'permission' => $this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.STUDENT_WISE_ORDER_HISTORY')
        ],
        [
          'title' => "Parent Orders Summary Report",
          'sub_title' => 'Parent Orders Summary Report',
          'icon' => 'svg_icons/cancelationreport.svg',
          'url' => $site_url.'procurement/sales_controller_v2/parent_orders_summary_report',
          'permission' => $this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.PARENT_ORDERS_SUMMARY_REPORT')
        ],
        [
          'title' => "Parent Orders Report",
          'sub_title' => 'Parent Orders Report',
          'icon' => 'svg_icons/cancelationreport.svg',
          'url' => $site_url.'procurement/sales_controller_v2/parent_orders_report',
          'permission' => $this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.PARENT_ORDERS_REPORT')
        ]
    );

// check if new std allow
    $allow_new = $this->settings->getSetting('allow_sales_for_new_students');
    if($allow_new) {
      $new= [
        'title' => 'New Student sales',
        'sub_title' => 'New Student sales list',
        'icon' => 'svg_icons/dailytransaction.svg',
        'url' => $site_url.'procurement/sales_controller_v2/new_student_sales_list',
        'permission' => $this->authorization->isAuthorized('PROCUREMENT_SALES.MODULE') && $this->authorization->isAuthorized('PROCUREMENT_SALES.REPORTS')
      ];
      array_push($data['report_tiles'], $new);

    }
// 
    $data['report_tiles'] = checkTilePermissions($data['report_tiles']);

    $data['acc_tiles'] = array(
        [
          'title' => 'Sales Accounts',
          'sub_title' => 'Sales Accounts',
          'icon' => 'svg_icons/account.svg',
          'url' => $site_url.'procurement/sales_controller_v2/sales_accounts',
          'permission' => $this->authorization->isSuperAdmin()
        ]
    );
    $data['acc_tiles'] = checkTilePermissions($data['acc_tiles']);

    $data['admin_tiles'] = array(
      [
        'title' => 'Re-generate PDF Receipt',
        'sub_title' => 'Re-generate PDF Receipt',
        'icon' => 'svg_icons/regeneratepdfreceipt.svg',
        'url' => $site_url.'procurement/sales_controller_v2/re_generate_pdf',
        'permission' => $this->authorization->isSuperAdmin()
      ],
      [
        'title' => 'Pre Defined Templates',
        'sub_title' => 'Template',
        'icon' => 'svg_icons/regeneratepdfreceipt.svg',
        'url' => $site_url.'procurement/sales_controller_v2/pre_defined_templates',
        'permission' => $this->authorization->isAuthorized('PROCUREMENT_SALES.PRE_DEFINED_TEMPLATES')
      ]
    );
    $data['admin_tiles'] = checkTilePermissions($data['admin_tiles']);

    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'procurement/sales_view_v2/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'procurement/sales_view_v2/index_mobile';
    }else{
      $data['main_content'] = 'procurement/sales_view_v2/index';    	
    }

    
    $this->load->view('inc/template', $data);
  }

  public function updateVariant($id, $item_id) {
    $updateVariant = $this->Sales_model_v2->update_variant($id);
    if ($updateVariant) {
        $this->session->set_flashdata('flashSuccess', 'Successfully Updated.');
        redirect('items/variant/index/' . $item_id);
    } else {
        $this->session->set_flashdata('flashError', 'Something Wrong..');
        redirect('procurement/sales_controller_v2/sales_variant/' . $item_id);
    }
  }

  public function search_class_student(){
    $mode = $_POST['mode'];
    switch ($mode) {
      case 'admission_no':
        $admission_no = $_POST['admission_no'];
        $stdData = $this->Sales_model_v2->get_sales_student_detailbyId($admission_no, 0);
        break;
      case 'class':
        $classId = $_POST['classId'];
        $stdData = $this->Sales_model_v2->get_student_data_by_class_section($classId);
        break;
      case 'student':
        $student_id = $_POST['student_id'];
        $stdData = $this->Sales_model_v2->get_sales_student_detailbyId(0,$student_id);
        break;
      case 'name':
        $std_id = $_POST['std_id'];
        $stdData = $this->Sales_model_v2->get_sales_student_detailbyId(0,$std_id);
        break;



        case 'enrollment_no':
          $enrollment_no = $_POST['enrollment_no'];
          $stdData = $this->Sales_model_v2->get_sales_student_details_by__($enrollment_no, 'enrollment_no');
          break;
        case 'roll_no':
          $roll_no = $_POST['roll_no'];
          $stdData = $this->Sales_model_v2->get_sales_student_details_by__($roll_no, 'roll_no');
          break;
        case 'alpha_roll_no':
          $alpha_roll_no = $_POST['alpha_roll_no'];
          $stdData = $this->Sales_model_v2->get_sales_student_details_by__($alpha_roll_no, 'alpha_roll_no');
          break;
    }
    // echo '<pre>777'; print_r($stdData); die();
    echo json_encode($stdData);
  }
  
  public function sales_collect(){
   
    $data['classList'] = $this->Sales_model_v2->getClassSectionNames();
    $data['salesYear'] = $this->Sales_model_v2->get_sales_year();
    $data['acadYears'] = $this->Sales_model_v2->get_acad_years();
    $data['preDefinedTemplates'] = $this->Sales_model_v2->get_pre_defined_templates();
    $data['discountRemarks'] = array(
      'Complementary',
      'Bulk order'
    );
    
    $data['products'] = $this->Sales_model_v2->get_prodcuts_all('only_category_admin');
    $data['studentNames'] = $this->Sales_model_v2->getstudentallNames();
    $allow_new = $this->settings->getSetting('allow_sales_for_new_students');
    $data['manual_receipt'] = $this->settings->getSetting('sales_manual_receipt_number');
    $data['allow_new'] = 0;
    if($allow_new) {
      $data['allow_new'] = 1;
    }
    $data['main_content'] = 'procurement/sales_view_v2/collect/sell';
    $this->load->view('inc/template_fee', $data);
  }

  public function collect_sales() {
    $data['classList'] = $this->Student_Model->getClassNames();
    $data['products'] = $this->Sales_model_v2->get_prodcuts_all();
    $data['studentNames'] = $this->Sales_model_v2->getstudentallNames();
    $data['main_content'] = 'procurement/sales_view_v2/collect/sell';
    $this->load->view('inc/template', $data);
  }

  public function collect_sales_fee($std_id){
    $data['main_content'] = 'procurement/sales_view_v2/collect/transaction/index';
    $this->load->view('inc/template', $data);
  }

  public function fetch_jsonformatVariants(){
    $result = $this->Sales_model_v2->get_prodcuts_all();
    echo json_encode($result);
  }

  public function submit_sales_transaction(){

    $input = $this->input->post();
    $total_after_discount= isset($input['total_after_discount']) ? $input['total_after_discount'] : 0;
    if($total_after_discount < 0) {
      $this->session->set_flashdata('flashError', 'Total amount after discount cannot be negative.');
      redirect('procurement/sales_controller_v2/sales_collect');
    }

    if($this->settings->getSetting('enable_indus_single_window_approval_process') && isset($_POST['categories_subcategories'])){
      $cat_sub_id = explode('_',$_POST['categories_subcategories'][0]);
      $cat_id = $cat_sub_id[0];
      $catergory_name = $this->db->select('category_name')->from('procurement_itemmaster_category')->where('id',$cat_id)->get()->row()->category_name;

      $category_name_trimmed = strtolower(trim($catergory_name));
      if (
          $category_name_trimmed == 'uniforms' ||
          $category_name_trimmed == 'uniform' ||
          $category_name_trimmed == 'books' ||
          $category_name_trimmed == 'book'
      ) 
      {
          $this->Sales_model_v2->single_window_details($_POST['student'], $catergory_name);
      }
    }
    
    // exit();
    // return 1;
    

    if ($input['sale_type'] == 'new') {
      $input['student'] = '';
    }
    $catIds = [];
    $products = [];
    foreach ($input['prodcuts'] as $key => $product) {
      list($catId, $productId) = explode('_', $product);
      array_push($catIds,$catId);
      array_push($products,$productId);
    }
    $temp = [];
    foreach ($catIds as $key => $catId) {
      $temp[$catId][] = array(
        'prodcuts'=>$products[$key],
        'variants'=>$input['variants'][$key],
        'quantity'=>$input['quantity'][$key],
        'amount'=>$input['amount'][$key],
        'current_quantity'=>$input['current_quantity'][$key],
      );
    }

    // echo '<pre>'; print_r($temp); die();
    // Making it true to AVOID some cases
    if (true || $input['final_amount'] != 0) {
      $stransIds = [];
      // echo '<pre>PRE bro: '; print_r($stransIds); die();
      foreach ($temp as $catId => $prodcuts) {
        $this->db->trans_begin();

        $sTransId = $this->Sales_model_v2->insert_sales_transaction($input['student'], $input, $catId, $prodcuts);
        // echo '<pre>PRE bro: '; print_r($sTransId); die();
        if (empty($sTransId)) {
          $this->db->trans_rollback();
        }else{
          $manual_receipt = $this->settings->getSetting('sales_manual_receipt_number');
          if ($manual_receipt) {
            $result =  $this->Sales_model_v2->update_receipt_sale_transcation_manual($sTransId, $catId, $input);
          }else{
            $result =  $this->Sales_model_v2->update_receipt_sale_transcation($sTransId, $catId, $input);
          }
        if (empty($result)) {
          $this->db->trans_rollback();
          $this->session->set_flashdata('flashError', 'Receipt book not found');
          redirect('procurement/sales_controller_v2/sales_collect');
        }
          array_push($stransIds, $sTransId);
        }
        $this->db->trans_commit();
      }
      $enable_sales_notification_emails = $this->settings->getSetting('enable_procurement_send_notification_email_to_parent');
      // echo "<pre>Before If Condition<br>";print_r($enable_sales_notification_emails);die();
      if($enable_sales_notification_emails){
        // echo "<pre>after If Condition<br>";print_r($enable_sales_notification_emails);die();
        $student_and_parent_details= $this->Sales_model_v2->student_and_parent_details($input['student']);
        $alpha_rollnum= $student_and_parent_details['alpha_rollnum'];
        $enrollment_number= $student_and_parent_details['enrollment_number'];
        $student_name= $student_and_parent_details['student_name'];
        $ClassSection= $student_and_parent_details['class_name'].''.$student_and_parent_details['section_name'];
        $sales_details = $this->Sales_model_v2->get_sales_details($sTransId);
        $html = '';
        if ($sales_details != null) {

         


            $html .= '<h4>Student Name : '.$student_name.' </h4>';
            $html .= '<h4>Grade : '.$ClassSection.' </h4>';
            $html .= '<h4>Alpha Roll No. : '.$alpha_rollnum.' </h4>';
            $html .= '<h4>Enrollment No. : '.$enrollment_number.' </h4>';
            $html .= '<h4>Receipt No : '.htmlspecialchars($sales_details['sales_details']->receipt_no).' </h4>';
            $html .= '<h4>Receipt Date : '.htmlspecialchars($sales_details['sales_details']->receipt_date).'</h4>';
            $html .= '<h4>Total Amount : '.number_format($sales_details['sales_details']->total_amount, 2).'</h4>';

            if($this->settings->getSetting('procurement_sales_enable_discount_feature')) {
              $totalAmount= number_format($input['final_amount'], 2);
              $discountAmount= number_format($input['discounted_amount'], 2);
              $totalAmountAfterDiscount= number_format($input['total_after_discount'], 2);

              $html .= '<h4>Discount Amount : '.$discountAmount.'</h4>';
              $html .= '<h4>Amount After Discount : '.$totalAmountAfterDiscount.'</h4>';
            }

            $html .= '<h4>Receipt Details</h4>';
            $html .= '<table style="border: 1px solid #dee2e6; border-collapse: collapse; width: 100%;">';
            $html .= '<thead><tr>';
            $html .= '<th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">Item Name</th>';
            $html .= '<th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">Quantity</th>';
            $html .= '<th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">Amount</th>';
            $html .= '</tr></thead>';
            $html .= '<tbody>';

            foreach ($sales_details['items_details'] as $item) {
                $html .= '<tr>';
                $html .= '<td style="border: 1px solid #dee2e6; padding: 8px;">' . htmlspecialchars($item->item_name) . '</td>';
                $html .= '<td style="border: 1px solid #dee2e6; padding: 8px;">' . htmlspecialchars($item->quantity) . '</td>';
                $html .= '<td style="border: 1px solid #dee2e6; padding: 8px;">' . number_format($item->amount, 2) . '</td>';
                $html .= '</tr>';
            }

            $html .= '</tbody>';
            $html .= '</table>';
        } else {
            $html .= '<p>No sales details found.</p>';
        }
        // // Send notification to the parent
        //   $student_and_parent_details= $this->Sales_model_v2->student_and_parent_details($input['student']);
        //   $parent_id_arr= [];
        //   foreach($student_and_parent_details['parent_ids'] as $key => $val) {
        //     array_push($parent_id_arr, $val->id);
        //   }
        //   $parent_id_str= implode(',', $parent_id_arr);
        //   $student_name= $student_and_parent_details['student_name'];
        //   // echo '<pre>'; print_r($student_and_parent_details); die();
        //           $this->load->helper('texting_helper');
        //           $input_arr = array();
        //           $input_arr['staff_ids'] = [$parent_id_str];
        //           $input_arr['mode'] = 'notification';
        //           $input_arr['source'] = 'Purchase';
        //           $input_arr['message'] = "Dear parent, your kid - $student_name has bought some items from the school. All the details send to your email. Thank you!";
        //           $input_arr['title'] = 'Purchase Items';
        //           $status= sendText($input_arr);
        // // end of send to notification

        // Email and Notification
        $parent_id_arr= [];
        $parent_email_arr= [];
        foreach($student_and_parent_details['parent_details'] as $key => $val) {
          array_push($parent_id_arr, $val->parent_id);
          array_push($parent_email_arr, $val->parent_email);
        }
        $circular = $this->Sales_model_v2->get_circular_data();
        $this->load->model('communication/emails_model');
        $this->load->model('communication/circular_model', 'circular');
        $this->load->model('communication/texting_model', 'texting_model');
        if(!empty($circular)){
          $from_email = $circular->registered_email;
          $email_ids = $parent_email_arr;
          $subject = $circular->email_subject;
          $body = $circular->content;
          if($circular->members_email != ''){
            $emails = explode(',', $circular->members_email);
            foreach ($emails as $email) {
              $email = trim($email);
              if ($email != '') {
                  array_push($email_ids, $email); 
              }
            }
          }
          $email_master_data = array(
            'subject' => $subject,
            'body' => $html,
            'source' => 'Email Template',
            'sent_by' => $this->authorization->getAvatarId(),
            'recievers' => implode(',', $parent_email_arr),
            'from_email' => $from_email,
            'files' => NULL,
            'acad_year_id' => $this->acad_year->getAcadYearId(),
            'visible' => 1,
            'sender_list' =>NULL,
            'sending_status' => 'Completed'
          );
          $email_master_id = $this->emails_model->saveEmail($email_master_data);
          $sent_data = [];
          if(!empty($email_master_id)){
            foreach ($student_and_parent_details['parent_details'] as $parent) {
              $obj = new stdClass();
              $obj->email_master_id = $email_master_id;
              $obj->id = $parent->parent_id;
              $obj->email = $parent->parent_email;
              $obj->avatar_type = 2;
              $obj->status = ($parent->parent_email) ? 'Awaited' : 'No Email';
              $sent_data[] = $obj;
            }
          }
          $this->load->model('communication/emails_model');
          $this->emails_model->save_sending_data($sent_data, $email_master_id);
          $this->load->helper('email_helper');
          sendEmail($html, $subject, $email_master_id, $email_ids, $from_email, json_decode(''));
        }

        
        $parent_id_str= implode(',', $parent_id_arr);
        $school_name = $this->settings->getSetting('school_name');
        $notification = "Dear parent, your kid - $student_name has bought some items from the school store. Please check your school app for more details. Thank you!";
        $credits = $this->texting_model->_calculateCredits($notification, 0);
        $text_master = array(
          'title' => $school_name,
          'message' => $notification,
          'sent_by' => $this->authorization->getAvatarId(),
          'reciever' => $parent_id_str,
          'acad_year_id' => $this->acad_year->getAcadYearId(),
          'source' => 'Sales',
          'text_count' => 0,
          'visible' => 1,
          'mode' => 'notification',
          'sms_credits' => $credits,
          'is_unicode' => 0,
          'sender_list' => NULL,
          'sending_status' => 'Initiated'
        );
        $texting_master_id = $this->texting_model->save_texts($text_master);

        $url = site_url('parent_controller/student_inventory');
        $textingData = [];
        foreach($student_and_parent_details['parent_details'] as $parent){
          $textingData[] = array(
            'texting_master_id' => $texting_master_id,
            'stakeholder_id' => $parent->parent_id,
            'mobile_no' => $parent->parent_mobile_no,
            'mode' => 1,
            'status' => ($parent->tokenState == 0 )? 'No Token' : 'Sent',
            'avatar_type' => 2,
            'is_read' => 0,
            'user_id' => $parent->user_id,
            'token' => $parent->user_token,
          );
        }
        $token_data = $this->texting_model->save_notifications($textingData);
        $this->load->helper('notification_helper');
        $notification_status = commonNotifications($token_data, $school_name, $notification, $url);
      }

      $this->session->set_userdata('sTransIds', $stransIds);
      $this->session->set_userdata('std_id', $input['student']);
      $this->session->set_userdata('sale_type', $input['sale_type']);
      $this->session->set_userdata('new_student_name', $input['new_std_name']);
      // if(empty($sTransId)) {
        
      // }
      redirect('procurement/sales_controller_v2/receipts_sales');
    }
  }

  public function receipts_sales( $canceled = ''){
    $data['cancel'] = $canceled;
    $sTransIds = $this->session->userdata('sTransIds');
    $std_id = $this->session->userdata('std_id');
    $sale_type = $this->session->userdata('sale_type');
    $new_student_name = $this->session->userdata('new_student_name');
    if (empty($sTransIds)) {
      $this->session->set_flashdata('flashError', 'Stock of some product is out of stock. Please try again!');
      redirect('procurement/sales_controller_v2/sales_collect');
    }
    $sales_data = [];
    foreach ($sTransIds as $key => $stransId) {
      $receipt_data = $this->Sales_model_v2->get_sales_receipt_data($stransId, $std_id, $sale_type);
      $result = $this->_create_template_sales_pdf_format($receipt_data, $receipt_data->receipt_template);
      
      $update =  $this->Sales_model_v2->update_html_receipt_sales($result, $stransId);
      // echo '<pre>'; print_r($update); die();
      if($update) {
        $this->__generateSales_pdf_receipt($result, $stransId);
      }
      array_push($sales_data, $receipt_data);
    }

    $this->session->unset_userdata('sTransIds');
    $data['sales_data'] = $sales_data;
    $data['sales_type'] = $sale_type;
    $data['new_student_name'] = $new_student_name;
    $this->session->unset_userdata('new_student_name');
    $this->session->unset_userdata('sale_type');
    $receiptpath = $this->settings->getSetting('school_short_name');
    // 
    $fileCheck = FCPATH."application/views/procurement/sales_view_v2/receipts/".$receiptpath.'.php';
    $data['main_content'] = 'procurement/sales_view_v2/receipts/'.$receiptpath;
    if (!file_exists($fileCheck)) {
      
      $data['main_content'] = 'procurement/sales_view_v2/receipts/sales_receipts';
    }
    // echo '<pre>'; print_r($fileCheck); die();
    $this->load->view('inc/template_fee', $data);
  }

  private function _create_template_sales_pdf_format($sales_data, $template){
    // echo '<pre>A: '; print_r(empty($template)); die();
    $sale_type = $this->session->userdata('sale_type');
    if($sale_type == 'existing') {
      $template = str_replace('%%student_name%%', $sales_data->std_data->std_name, $template);
      $class = $sales_data->std_data->class_name.''.$sales_data->std_data->section_name;
      $template = str_replace('%%class%%',$class, $template);
      $template = str_replace('%%class_name%%',$sales_data->std_data->class_name, $template);
    } else if($sale_type == 'new') {
      $new_student_name = $this->session->userdata('new_student_name');
      $template = str_replace('%%student_name%%', $new_student_name, $template);
      $template = str_replace('%%class%%',$sales_data->class_name, $template);
      $template = str_replace('%%class_name%%','NA', $template);
    }else{
      $template = str_replace('%%student_name%%', $sales_data->std_data->std_name, $template);
      $class = $sales_data->std_data->class_name.''.$sales_data->std_data->section_name;
      $template = str_replace('%%class%%',$class, $template);
      $template = str_replace('%%class_name%%',$sales_data->std_data->class_name, $template);
    }

    $template = str_replace('%%receipt_no%%',$sales_data->receipt_no, $template);
    $template = str_replace('%%transaction_date%%', $sales_data->receipt_date, $template);
    $template = str_replace('%%remarks%%', $sales_data->remarks, $template);


// Add this line to set the renrollment_number and alpha_rollnum by default if it is there in the  template
    $template = str_replace('%%enrollment_number%%',$sales_data->std_data->enrollment_number, $template);
    $template = str_replace('%%alpha_rollnum%%', $sales_data->std_data->alpha_rollnum, $template);





    $i=1;
    $totalAmount = 0;
    $sales_part = '<tr>';
    $sales_part .= '<td>S. No.</td>';
    $sales_part .= '<td colspan="2">Particulars</td>';
    $sales_part .= '<td>Amount</td>';
    $sales_part .= '</tr>';
     foreach ($sales_data->trans as $key => $val) {  
      $totalAmount += $val->amount;
      $sales_part.='<tr>';
      $sales_part.='<td style="vertical-align: middle;">'.$i++.'</td>';
      $sales_part.='<td colspan="2">'.$val->product_name .' - '.$val->variant_name.'</td>';
      $sales_part.='<td>'.$val->amount.'</td>';
      $sales_part.='</tr>';
    }

    $sales_part.= '<tr>';
    $sales_part.='<td colspan="3" style="text-align: right;">Total Amount</td>';
    $sales_part.='<td>'.$totalAmount.'</td>'; 
    $sales_part.='</tr>';

    if($this->settings->getSetting('procurement_sales_enable_discount_feature')) {
      $sales_part.= '<tr>';
      $sales_part.='<td colspan="3" style="text-align: right;">Discount Amount</td>';
      $sales_part.='<td>'.$sales_data->discount_amount.'</td>'; 
      $sales_part.='</tr>';

      $amountAfterDiscount= 1*$totalAmount - 1*$sales_data->discount_amount;

      $sales_part.= '<tr>';
      $sales_part.='<td colspan="3" style="text-align: right;">Total Payble Amount</td>';
      $sales_part.='<td>'.$amountAfterDiscount.'</td>'; 
      $sales_part.='</tr>';

      $amountInWords = $this->getIndianCurrency($amountAfterDiscount + $sales_data->card_charge_amount);
      // echo '<pre>A: '; print_r($amountInWords); die();
    } else {
      $amountInWords = $this->getIndianCurrency($sales_data->total_amount + $sales_data->card_charge_amount);
    }
    $sales_part .= '</table>';
   
    // echo '<pre>A: '.$amountInWords; die();

    $payment_mode = '<table style="margin:0">';
      if ($sales_data->payment_type == '7') {
        $payment_mode .='<tr>';
        $payment_mode .='<td><b>Card : </b>'.$sales_data->cheque_dd_number.'</td>';
        $payment_mode .='</tr>';
      }else if($sales_data->payment_type == '9'){
        $payment_mode .='<tr>';
        $payment_mode .='<td>Cash</td>';
        $payment_mode .='</tr>';
      }else if($sales_data->payment_type == '4'){
        $payment_mode .='<tr>';
        $payment_mode .='<td><b>Cheque No : </b>'.$sales_data->cheque_dd_number.'<br>'.'<b>Cheque Bank : </b>' .$sales_data->bank_name.'<br> <b>Cheque Branch : </b> '.$sales_data->bank_branch.'<br> <b>Cheque Date : </b> '.date('d-m-Y',strtotime($sales_data->cheque_dd_date)).'</td>';
        $payment_mode .='</tr>';
      }else if($sales_data->payment_type == '1'){
        $payment_mode .='<tr>';
        $payment_mode .='<td><b>DD No : </b>'.$sales_data->cheque_dd_number.'<br>'.'<b>DD Bank : </b>' .$sales_data->bank_name.'<br> <b>DD Branch : </b> '.$sales_data->bank_branch.'<br> <b>DD Date : </b> '.date('d-m-Y',strtotime($sales_data->cheque_dd_date)).'</td>';         
        $payment_mode .='</tr>';
      }else if($sales_data->payment_type == '8'){
        $payment_mode .='<tr>';
        $payment_mode .='<td><b>Net Banking : </b>'.$sales_data->cheque_dd_number.'</td>';
        $payment_mode .='</tr>';
      } else if($sales_data->payment_type == '15'){
        $payment_mode .='<tr>';
        $payment_mode .='<td><b>Pocket Money</b></td>';
        $payment_mode .='</tr>';
      } else{
        $payment_mode .='<tr>';
        $payment_mode .='<td>Cash</td>';
        $payment_mode .='</tr>';
      }
    $payment_mode .= '</table>';
    $template = str_replace('%%rupees_in_words%%',ucwords($amountInWords), $template);
    $template = str_replace('%%payment_modes%%',$payment_mode, $template);
    $template = str_replace('%%sales%%',$sales_part, $template);

   
// echo '<pre>Template: '; print_r($template); die();
    // echo '<pre>Template: '; print_r($template); die();
    return $template;
  }

  private function __generateSales_pdf_receipt($html, $stransId) {
    $school = CONFIG_ENV['main_folder'];
    $path = $school.'/sales_reciepts/'.uniqid().'-'.time().".pdf";

    $bucket = $this->config->item('s3_bucket');

    $status = $this->Sales_model_v2->update_sales_path($stransId, $path);
    $page = 'landscape';
    $receipt_for = $this->settings->getSetting('school_short_name');
    if ($receipt_for === 'nhis') {
      $page = 'portrait';
    }
    if ($receipt_for === 'apstrust') {
      $page = 'portrait';
    }
    $page_size = 'a4';
    $curl = curl_init();
    $postData = urlencode($html);
    $username = CONFIG_ENV['job_server_username'];
    $password = CONFIG_ENV['job_server_password'];
    $return_url = site_url().'Callback_Controller/updateSalesPdfLink_v2';

    curl_setopt_array($curl, array(
        CURLOPT_URL => CONFIG_ENV['job_server_pdfgen_uri'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERPWD => $username . ":" . $password,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => "path=".$path."&bucket=".$bucket."&page=".$page."&page_size=".$page_size."&data=".$postData."&return_url=".$return_url,
        CURLOPT_HTTPHEADER => array(
            "Accept: application/json",
            "Cache-Control: no-cache",
            "Content-Type: application/x-www-form-urlencoded",
            "Postman-Token: 090abdb9-b680-4492-b8b7-db81867b114e"
        ),
    ));
    
    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);
  }

  public function sales_receipt_history($stransId, $std_id, $canceled = ''){
    $data['cancel'] = $canceled;
    $data['traverse_to'] = '1';
    $data['std_id'] = $std_id;
    if($std_id == 0) {
      $data['sales_type'] = 'new';
    } else {
      $data['sales_type'] = 'existing';
    }
    $sales_data[] = $this->Sales_model_v2->get_sales_receipt_data($stransId, $std_id, $data['sales_type']);
    $data['sales_data'] = $sales_data;
    if($std_id == 0) {
      $data['new_student_name'] = $sales_data[0]->student_name;
    }
    $receiptpath = $this->settings->getSetting('school_short_name');
    $fileCheck = FCPATH."application/views/sales/receipts/".$receiptpath.'.php';
    $data['main_content'] = 'procurement/sales_view_v2/receipts/'.$receiptpath;
    if (!file_exists($fileCheck)) {   
      $data['main_content'] = 'procurement/sales_view_v2/receipts/sales_receipts';
    }
    $this->load->view('inc/template_fee', $data);
  }

  public function getIndianCurrency(float $number)
  {
      $decimal = round($number - ($no = floor($number)), 2) * 100;
      $hundred = null;
      $digits_length = strlen($no);
      $i = 0;
      $str = array();
      $words = array(0 => '', 1 => 'one', 2 => 'two',
          3 => 'three', 4 => 'four', 5 => 'five', 6 => 'six',
          7 => 'seven', 8 => 'eight', 9 => 'nine',
          10 => 'ten', 11 => 'eleven', 12 => 'twelve',
          13 => 'thirteen', 14 => 'fourteen', 15 => 'fifteen',
          16 => 'sixteen', 17 => 'seventeen', 18 => 'eighteen',
          19 => 'nineteen', 20 => 'twenty', 30 => 'thirty',
          40 => 'forty', 50 => 'fifty', 60 => 'sixty',
          70 => 'seventy', 80 => 'eighty', 90 => 'ninety');
      $digits = array('', 'hundred','thousand','lakh', 'crore');
      while( $i < $digits_length ) {
          $divider = ($i == 2) ? 10 : 100;
          $number = floor($no % $divider);
          $no = floor($no / $divider);
          $i += $divider == 10 ? 1 : 2;
          if ($number) {
              $plural = (($counter = count($str)) && $number > 9) ? '' : null;
              $hundred = ($counter == 1 && $str[0]) ? ' and ' : null;
              $str [] = ($number < 21) ? $words[$number].' '. $digits[$counter]. $plural.' '.$hundred:$words[floor($number / 10) * 10].' '.$words[$number % 10]. ' '.$digits[$counter].$plural.' '.$hundred;
          } else $str[] = null;
      }
      $Rupees = implode('', array_reverse($str));
      $paise = ($decimal) ? "." . ($words[$decimal / 10] . " " . $words[$decimal % 10]) . ' Paise' : '';
      return ($Rupees ? $Rupees . 'Rupees ' : '') . $paise ;
  }

  public function create_receipt_book(){
    $receipt_book = $this->Sales_model_v2->get_sales_receipt_books();
    $data['recept_format'] = $this->fee_library->receipt_format_creation_for_sales($receipt_book);
    $data['main_content'] = 'procurement/sales_view_v2/receipt_book';
    $this->load->view('inc/template', $data);
  }

  public function serach_prodcut_wise_varints(){
    $productsId = $_POST['productsId'];
    $cat_id = $_POST['cat_id'];
    $sales_year_id = $_POST['sales_year_id'];
    $result = $this->Sales_model_v2->get_prodcut_varints($productsId, $cat_id, $sales_year_id);
    
    echo json_encode($result);
  }

  public function get_quantity_amount_of_variantId(){
    $vId = $_POST['vId'];
    $result = $this->Sales_model_v2->get_varints_price_quantity($vId);
    echo json_encode($result);
  }

  public function daily_transcation_sales(){
    $data['category'] = $this->Sales_model_v2->get_category_list_report();
    $data['sub_category'] = $this->Sales_model_v2->get_sub_category_list_report('cats_dummy');
    $data['items'] = $this->Sales_model_v2->get_item_list_report('sub_cates_dummy');
    
    $data['class_sections'] = $this->Sales_model_v2->get_class_sections();

    $data['main_content'] = 'procurement/sales_view_v2/reports/daily_transaction';
    $this->load->view('inc/template_fee', $data);
  }

  public function get_daily_transaction(){
    $fromDate = $_POST['fromDate'];
    $toDate = $_POST['toDate'];
    $product_variants = $_POST['product_variants'];
    $im_subcategory = $_POST['im_subcategory'];
    $im_category = $_POST['im_category'];
    $payment_modes = $_POST['payment_modes'];
    $class_section = $_POST['class_section'];
    $result = $this->Sales_model_v2->get_daily_transaction_report($fromDate, $toDate, $product_variants, $im_subcategory, $im_category, $payment_modes, $class_section);
    echo json_encode($result);
  }

  public function sales_history(){
    if (!empty($this->input->post())) {
      $data['sales_history'] = $this->Sales_model_v2->get_sales_history_student_wise($this->input->post('student'));
      $data['student_details'] = $this->Sales_model_v2->get_sales_history_student_details($this->input->post('student'));
    }
    $data['IS_AUTHORIZED_DELETE_ITEMS_IN_TRANSACTION']= $this->authorization->isAuthorized('PROCUREMENT_SALES.DELETE_ITEMS_IN_TRANSACTION');
    $data['studentNames'] = $this->Sales_model_v2->getstudentallNames();
    $data['classList'] = $this->Sales_model_v2->getClassSectionNames();
    $data['main_content'] = 'procurement/sales_view_v2/reports/history';
    $this->load->view('inc/template', $data);
  }

  public function get_student_id_sales(){
    $admission_no = $_POST['admission_no'];
    $stdData = $this->Sales_model_v2->get_sales_student_historyby_id($admission_no, 0);
    echo json_encode($stdData);
  }

  public function get_student_id_sales_studentiId(){
    $student_id = $_POST['student_id'];
    $stdData = $this->Sales_model_v2->get_sales_student_historyby_id(0, $student_id);
    echo json_encode($stdData);
  }

   public function sales_receipt_pdf_download($id){ 
    $link = $this->Sales_model_v2->download_sales_receipt_path($id);
    $url = $this->filemanager->getFilePath($link);

    $data = file_get_contents($url);
    $this->load->helper('download');
    force_download('sales.pdf', $data, TRUE);
  }

  public function re_generate_pdf(){
    $data['main_content'] = 'procurement/sales_view_v2/reports/re_geneate';
    $this->load->view('inc/template', $data);  
  }

  public function sales_accounts(){
    $data['sales_varints'] = $this->Sales_model_v2->get_varints_name_for_accounts();
    $data['main_content'] = 'procurement/sales_view_v2/reports/accounts';
    $this->load->view('inc/template', $data);  
  }

  public function get_transaction_sales(){
    $fromDate = $_POST['fromDate'];
    $toDate = $_POST['toDate'];
    $result = $this->Sales_model_v2->get_daily_transaction_report($fromDate, $toDate);
    echo json_encode($result);
  }

  public function generate_pdf_fee_receipt(){
    $checked_transids = $_POST['checked_transids'];
    foreach ($checked_transids as $key => $transId) {
      $receipt_data = $this->Sales_model_v2->get_sales_receipt_data_for_pdf($transId);
      $result = $this->_create_template_sales_pdf_format($receipt_data, $receipt_data->receipt_template);
      $update =  $this->Sales_model_v2->update_html_receipt_sales($result, $transId);
      $return= 0;
      if($update) {
        $return= 1;
        $this->__generateSales_pdf_receipt($result, $transId, 'apsps', 'landscape');
      }
    }
    echo $return;
  }

  public function sales_receits_delete(){
    $salesId = $_POST['salesId'];
    $remarks = $_POST['remarks'];
    echo $this->Sales_model_v2->soft_delete_sales_receipt($salesId, $remarks);
  }

  public function update_sale_vendor_code(){
    $result = $this->Sales_model_v2->update_sale_vendor_code_by_varints();
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully Added.');
    }else{
      $this->session->set_flashdata('flashError', 'Something went wrong...');
    }
    redirect('procurement/sales_controller_v2/sales_accounts');
  }
  public function delete_vendor_account($id){
    $result = $this->Sales_model_v2->delete_vendor_account_by_id($id);
    if ($result) {
      $this->session->set_flashdata('flashSuccess', 'Successfully deleted.');
    }else{
       $this->session->set_flashdata('flashError', 'Something went wrong...');
    }
    redirect('procurement/sales_controller_v2/sales_accounts');
  }

  public function receipt_canceled_report(){
    $data['main_content'] = 'procurement/sales_view_v2/reports/receipt_cancellation';
    $this->load->view('inc/template', $data);
  }

  public function get_cancelations_transaction(){
    $from_date = $_POST['from_date'];
    $to_date = $_POST['to_date'];
    $result = $this->Sales_model_v2->get_cancelation_transaction_report($from_date, $to_date);
    echo json_encode($result);
  }

  public function new_student_sales_list(){
    $data['new_student_list'] = $this->Sales_model_v2->get_new_student_sales_list_report();
    $data['main_content'] = 'procurement/sales_view_v2/reports/new_student_sales';
    $this->load->view('inc/template_fee', $data);
  }

  function sales_return_view() {
    $data['all_class']= $this->Sales_model_v2->get_class_sections();
    $data['salesYear']= $this->Sales_model_v2->get_sales_year();
    $data['is_prev_sales_year_closed']= $this->Sales_model_v2->is_prev_sales_year_closed();
    $data['acadYears']= $this->Sales_model_v2->get_acad_years();
    // echo '<pre>'; print_r($data['is_prev_sales_year_closed']); die();
    $data['main_content'] = 'procurement/sales_view_v2/collect/sales_return_view';
    $this->load->view('inc/template_fee', $data);
  }

  public function getItemSales() {
    echo json_encode( $this->Sales_model_v2->getItemSales() );
  }

  public function submit_return() { 
    // $last_insert_ids = $this->Sales_model_v2->submit_return();
    $last_insert_ids = $this->Sales_model_v2->submit_return_new();
    // $input= $this->input->post();
    // $proc_sales_master_id= explode(',', $input['proc_sales_master_ids']);
    // if(!empty($last_insert_ids)){
    //   $receipt_data = $this->Sales_model_v2->get_sales_receipt_template_data();
    //   foreach($last_insert_ids as $key => $val) {
    //     $sales_return_data = $this->Sales_model_v2->get_sales_return_data($val);

    //     $result = $this->_create_template_sales_pdf_format_version_2($sales_return_data, $receipt_data->receipt_template);

    //     $update =  $this->Sales_model_v2->store_receipt_html_to_return_table($result, $val);
    //     if ($update) {
    //       $receipt= $this->Sales_model_v2->genearte_pdf_for_return_receipt($result, $val, '', '');
    //     }
    //   }

      
    //   // echo $proc_sales_master_id[0]; 
    //   // exit();
    //   // $this->session->set_userdata('html_template', $result);

    //   // echo '<pre>'; print_r($receipt); die();

    //   // redirect('procurement/sales_controller_v2/sales_return_receipt_page/' .$proc_sales_master_id);

    //   // echo $result;

    //   // echo '<pre>'; print_r($result); die(); $this->filemanager->getFilePath($link);

    // }


    $input= $this->input->post();
    
    // 

    // Starts emailing
    $enable_sales_notification_emails = $this->settings->getSetting('enable_procurement_send_notification_email_to_parent');
    // echo "<pre>Before If Condition<br>";print_r($enable_sales_notification_emails);die();
    if($enable_sales_notification_emails){
      $student_and_parent_details= $this->Sales_model_v2->student_and_parent_details($input['stuent_id']);
      $student_name= $student_and_parent_details['student_name'];
      $ClassSection= $student_and_parent_details['class_name'].' - '.$student_and_parent_details['section_name'];
      $sales_details = $this->Sales_model_v2->get_return_details($last_insert_ids);
      $html = '';
      if (!empty($sales_details) && $sales_details != null) {
          $html .= '<h4>Student Name : '.$student_name.' </h4>';
          $html .= '<h4>Grade : '.$ClassSection.' </h4>';
          $html .= '<h4>Was Received at : '.htmlspecialchars($sales_details[0]->created_on).' </h4>';
          $html .= '<h4>Return On : '.htmlspecialchars($sales_details[0]->return_on).'</h4>';
          // $html .= '<h4>Total Amount : '.number_format($sales_details['sales_details']->total_amount, 2).'</h4>';

          $html .= '<h4>Receipt Details</h4>';
          $html .= '<table style="border: 1px solid #dee2e6; border-collapse: collapse; width: 100%;">';
          $html .= '<thead><tr>';
          $html .= '<th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">Item Name</th>';
          $html .= '<th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">Unit Price</th>';
          $html .= '<th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">Quantity</th>';
          $html .= '<th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">Refund Amount</th>';
          $html .= '</tr></thead>';
          $html .= '<tbody>';

          $tot_qty= 0;
          $tot_amt= 0;
          foreach ($sales_details as $item) {
            $tot_qty += $item->return_quantity;
            $tot_amt += $item->refund_amount;
              $html .= '<tr>';
              $html .= '<td style="border: 1px solid #dee2e6; padding: 8px;">' . htmlspecialchars($item->item_name) . '</td>';
              $html .= '<td style="border: 1px solid #dee2e6; padding: 8px;">' . number_format($item->item_unit_price, 2) . '</td>';
              $html .= '<td style="border: 1px solid #dee2e6; padding: 8px;">' . htmlspecialchars($item->return_quantity) . '</td>';
              $html .= '<td style="border: 1px solid #dee2e6; padding: 8px;">' . number_format($item->refund_amount, 2) . '</td>';
              $html .= '</tr>';
          }

          $html .= '<tr>';
          $html .= '<th colspan="2" style="border: 1px solid #dee2e6; padding: 8px;">Total</th>';
          $html .= '<th style="border: 1px solid #dee2e6; padding: 8px;">' . htmlspecialchars($tot_qty) . '</th>';
          $html .= '<th style="border: 1px solid #dee2e6; padding: 8px;">' . number_format($tot_amt, 2) . '</th>';
          $html .= '</tr>';

          $html .= '</tbody>';
          $html .= '</table>';
      } else {
          $html .= '<p>No sales details found.</p>';
      }

      // echo '<pre>'; print_r($html); die();

      // Email and Notification
      $parent_id_arr= [];
      $parent_email_arr= [];
      foreach($student_and_parent_details['parent_details'] as $key => $val) {
        array_push($parent_id_arr, $val->parent_id);
        array_push($parent_email_arr, $val->parent_email);
      }
      $circular = $this->Sales_model_v2->get_circular_data_new('Item Return Email Template'); // 
      $this->load->model('communication/emails_model');
      $this->load->model('communication/circular_model', 'circular');
      $this->load->model('communication/texting_model', 'texting_model');
      if(!empty($circular)){
        $from_email = $circular->registered_email;
        $email_ids = $parent_email_arr;
        $subject = $circular->email_subject;
        $body = $circular->content;
        if($circular->members_email != ''){
          $emails = explode(',', $circular->members_email);
          foreach ($emails as $email) {
            $email = trim($email);
            if ($email != '') {
                array_push($email_ids, $email); 
            }
          }
        }
        $email_master_data = array(
          'subject' => $subject,
          'body' => $html,
          'source' => 'Email Template',
          'sent_by' => $this->authorization->getAvatarId(),
          'recievers' => implode(',', $parent_email_arr),
          'from_email' => $from_email,
          'files' => NULL,
          'acad_year_id' => $this->acad_year->getAcadYearId(),
          'visible' => 1,
          'sender_list' =>NULL,
          'sending_status' => 'Completed'
        );
        $email_master_id = $this->emails_model->saveEmail($email_master_data);
        $sent_data = [];
        if(!empty($email_master_id)){
          foreach ($student_and_parent_details['parent_details'] as $parent) {
            $obj = new stdClass();
            $obj->email_master_id = $email_master_id;
            $obj->id = $parent->parent_id;
            $obj->email = $parent->parent_email;
            $obj->avatar_type = 2;
            $obj->status = ($parent->parent_email) ? 'Awaited' : 'No Email';
            $sent_data[] = $obj;
          }
        }
        $this->load->model('communication/emails_model');
        $this->emails_model->save_sending_data($sent_data, $email_master_id);
        $this->load->helper('email_helper');
        $satetas= sendEmail($html, $subject, $email_master_id, $email_ids, $from_email, json_decode(''));

        // echo '<pre>'; print_r($satetas); die();
      }


      $parent_id_str= implode(',', $parent_id_arr);
      $school_name = $this->settings->getSetting('school_name');
      $notification = "Dear parent, your kid - $student_name has return some items to the school store. Please check your school app for more details. Thank you!";
      $credits = $this->texting_model->_calculateCredits($notification, 0);
      $text_master = array(
        'title' => $school_name,
        'message' => $notification,
        'sent_by' => $this->authorization->getAvatarId(),
        'reciever' => $parent_id_str,
        'acad_year_id' => $this->acad_year->getAcadYearId(),
        'source' => 'Sales',
        'text_count' => 0,
        'visible' => 1,
        'mode' => 'notification',
        'sms_credits' => $credits,
        'is_unicode' => 0,
        'sender_list' => NULL,
        'sending_status' => 'Initiated'
      );
      $texting_master_id = $this->texting_model->save_texts($text_master);

      $url = site_url('Parent_controller/student_inventory');
      $textingData = [];
      foreach($student_and_parent_details['parent_details'] as $parent){
        $textingData[] = array(
          'texting_master_id' => $texting_master_id,
          'stakeholder_id' => $parent->parent_id,
          'mobile_no' => $parent->parent_mobile_no,
          'mode' => 1,
          'status' => ($parent->tokenState == 0 )? 'No Token' : 'Sent',
          'avatar_type' => 2,
          'is_read' => 0,
          'user_id' => $parent->user_id,
          'token' => $parent->user_token,
        );
      }
      $token_data = $this->texting_model->save_notifications($textingData);
      $this->load->helper('notification_helper');
      $notification_status = commonNotifications($token_data, $school_name, $notification, $url);
    }
    // end emailing

    echo json_encode($last_insert_ids);
    // exit();
  }

  public function sales_return_receipt_page($proc_sales_return_id) {
    // echo '<pre>'; print_r($proc_sales_master_id); die();
    $receipts_datas = $this->Sales_model_v2->get_receipts_from_sales_master_id($proc_sales_return_id);
    if(!empty($receipts_datas)) {
      foreach($receipts_datas as $key => $val) {
        if($val->pdf_status == 1 || $val->receipt_pdf) {
          $val->receipt_pdf= $this->filemanager->getFilePath($val->receipt_pdf);
        }
      }
    }
    $data['receipts_data']= $receipts_datas;
   

    $data['main_content'] = 'procurement/sales_view_v2/reports/sales_return_receipt_page';
    $this->load->view('inc/template_fee', $data);
  }

  public function _create_template_sales_pdf_format_version_2($sales_data, $template) {
    $sale_type = $this->session->userdata('sale_type');
    if($sale_type == 'existing') {
      $template = str_replace('%%student_name%%', $sales_data[0]->std_name, $template);
      $class = $sales_data['cls_sec']->class_name.''.$sales_data['cls_sec']->section_name;
      $template = str_replace('%%class%%',$class, $template);
      $template = str_replace('%%class_name%%',$sales_data['cls_sec']->class_name, $template);
    } else if($sale_type == 'new') {
      $new_student_name = $this->session->userdata('new_student_name');
      $template = str_replace('%%student_name%%', $sales_data[0]->std_name, $template);
      $template = str_replace('%%class%%',$sales_data['cls_sec']->class_name.''.$sales_data['cls_sec']->section_name, $template);
      $template = str_replace('%%class_name%%','NA', $template);
    }else{
      $template = str_replace('%%student_name%%', $sales_data[0]->std_name, $template);
      $class = $sales_data['cls_sec']->class_name.''.$sales_data['cls_sec']->section_name;
      $template = str_replace('%%class%%',$class, $template);
      $template = str_replace('%%class_name%%',$sales_data['cls_sec']->class_name, $template);
    }

    $template = str_replace('%%receipt_no%%',"0019958786", $template); // manual, need to change
    $template = str_replace('%%transaction_date%%', "10-10-2023", $template);
    $template = str_replace('%%remarks%%', $sales_data[0]->return_reason, $template);
    $i=1;
    $totalAmount = 0;
    $sales_part = '<tr>';
    $sales_part .= '<td>S. No.</td>';
    $sales_part .= '<td colspan="2">Particulars</td>';
    $sales_part .= '<td>Amount</td>';
    $sales_part .= '</tr>';
     foreach ($sales_data as $key => $val) {  
      if($key != 'cls_sec') {
        $totalAmount += $val->refund_amount;
        $sales_part.='<tr>';
        $sales_part.='<td style="vertical-align: middle;">'.$i++.'</td>';
        $sales_part.='<td colspan="2">'.$val->item_name.'</td>';
        $sales_part.='<td>'.$val->refund_amount.'</td>';
        $sales_part.='</tr>';
      }
    }

    $sales_part.= '<tr>';
    $sales_part.='<td colspan="3" style="text-align: right;">Total Amount</td>';
    $sales_part.='<td>'.$totalAmount.'</td>'; 
    $sales_part.='</tr>';
   
    $amountInWords = $this->getIndianCurrency($totalAmount);


    $payment_mode = '<table style="margin:0">';
      if ($sales_data[0]->mode_of_payment == '7') {
        $payment_mode .='<tr>';
        $payment_mode .='<td><b>Card : </b>'.$sales_data[0]->cheque_dd_number.'</td>';
        $payment_mode .='</tr>';
      }else if($sales_data[0]->mode_of_payment == '9'){
        $payment_mode .='<tr>';
        $payment_mode .='<td>Cash</td>';
        $payment_mode .='</tr>';
      }else if($sales_data[0]->mode_of_payment == '4'){
        $payment_mode .='<tr>';
        $payment_mode .='<td><b>Cheque No : </b>'.$sales_data[0]->cheque_dd_number.'<br>'.'<b>Cheque Bank : </b>' .$sales_data[0]->bank_name.'<br> <b>Cheque Branch : </b> '.$sales_data[0]->bank_branch.'<br> <b>Cheque Date : </b> '.date('d-m-Y',strtotime($sales_data[0]->cheque_dd_date)).'</td>';
        $payment_mode .='</tr>';
      }else if($sales_data[0]->mode_of_payment == '1'){
        $payment_mode .='<tr>';
        $payment_mode .='<td><b>DD No : </b>'.$sales_data[0]->cheque_dd_number.'<br>'.'<b>DD Bank : </b>' .$sales_data[0]->bank_name.'<br> <b>DD Branch : </b> '.$sales_data[0]->bank_branch.'<br> <b>DD Date : </b> '.date('d-m-Y',strtotime($sales_data[0]->cheque_dd_date)).'</td>';         
        $payment_mode .='</tr>';
      }else if($sales_data[0]->mode_of_payment == '8'){
        $payment_mode .='<tr>';
        $payment_mode .='<td><b>Net Banking : </b>'.$sales_data[0]->cheque_dd_number.'</td>';
        $payment_mode .='</tr>';
      } else if($sales_data[0]->mode_of_payment == '15'){
        $payment_mode .='<tr>';
        $payment_mode .='<td><b>Pocket Money</b></td>';
        $payment_mode .='</tr>';
      } else{
        $payment_mode .='<tr>';
        $payment_mode .='<td>Cash</td>';
        $payment_mode .='</tr>';
      }
    $payment_mode .= '</table>';
    $template = str_replace('%%rupees_in_words%%',ucwords($amountInWords), $template);
    $template = str_replace('%%payment_modes%%',$payment_mode, $template);
    $template = str_replace('%%sales%%',$sales_part, $template);

    // $this->load->library('setting');
    // $school= $this->settings->getSetting('school_name');
    // $school_address= $this->settings->getSetting('school_address');
    // $school_logo= $this->settings->getSetting('school_logo');

    // $template = str_replace('%%school%%',"$school", $template); // manual, need to change
    // $template = str_replace('%%school_address%%', "$school_address", $template);
    // $template = str_replace('%%school_logo%%', "$school_logo", $template);

    // echo '<pre>'; print_r($template); die();

    return $template;
  }

  function sales_return_report() {
    $data['salesYear']= $this->Sales_model_v2->get_sales_year();
    // echo '<pre>'; print_r($data); die();
    $data['main_content'] = 'procurement/sales_view_v2/reports/sales_return_report';
    $this->load->view('inc/template_fee', $data);
  }

  public function getAllReturnReports() {
    echo json_encode( $this->Sales_model_v2->getAllReturnReports() );
  }

  public function get_class_sections() {
    echo json_encode( $this->Sales_model_v2->get_class_sections() );
  }

  public function get_students() {
    echo json_encode( $this->Sales_model_v2->get_students() );
  }

  public function check_if_rfid_mapped(){
    $res = $this->Sales_model_v2->check_if_rfid_mapped();
    echo json_encode($res);
  }

  public function student_wise_sales_report() {
    $data['studentNames'] = $this->Sales_model_v2->getstudentallNames();
    $data['classList'] = $this->Sales_model_v2->getClassSectionNames();
    $data['category'] = $this->Sales_model_v2->get_category_list_report();
    $data['salesYear'] = $this->Sales_model_v2->get_sales_year();

    // echo '<pre>'; print_r($data['classList']); die();

    if($this->mobile_detect->isTablet()) {
      $data['main_content']= 'procurement/sales_view_v2/reports/student_wise_sales_report_mobile_tablet';
    }else if($this->mobile_detect->isMobile()){
        $data['main_content']= 'procurement/sales_view_v2/reports/student_wise_sales_report_mobile_tablet';
    }else { 
      $data['main_content'] = 'procurement/sales_view_v2/reports/student_wise_sales_report';
    }
    $this->load->view('inc/template_fee', $data);

  }

  public function get_all_the_students_class_section_wise() {
    $res = $this->Sales_model_v2->get_all_the_students_class_section_wise();
    echo json_encode($res);
  }

  public function get_student_id_by_adm_no(){
    $admission_no = $_POST['admission_no'];
    $stdData = $this->Sales_model_v2->get_student_id_by_adm_no($admission_no);
    echo json_encode($stdData);
  }

  public function get_report_of_a_student(){
    $student_id = $_POST['student_id'];
    $selected_category = $_POST['selected_category'];
    $selected_subcategories = $_POST['selected_subcategories'];
    $sales_year_id = $_POST['sales_year_id'];
    $stdData = $this->Sales_model_v2->get_report_of_a_student_latest($student_id, $selected_category, $selected_subcategories, $sales_year_id);
    // echo '<pre>'; print_r($stdData); die();
    // $stdData = $this->Sales_model_v2->get_report_of_a_student($student_id, $selected_category, $selected_subcategories);
    echo json_encode($stdData);
  }

  public function get_item_cat_and_subcategory(){
    $array_of_items = $_POST['array_of_items'];
    $array_of_prices = $_POST['array_of_prices'];
    $stdData = $this->Sales_model_v2->get_item_details_from_item_name($array_of_items, $array_of_prices);
    echo json_encode($stdData);
  }

  public function get_all_sales_details_student_wise() {
    echo json_encode( $this->Sales_model_v2->get_all_sales_details_student_wise() );
  }
  
  public function mass_item_issue() {
    // $data['classList'] = $this->Student_Model->getClassNames();
    // $data['products'] = $this->Sales_model_v2->get_prodcuts_all();
    // $data['studentNames'] = $this->Sales_model_v2->getstudentallNames();
    $data['main_content'] = 'procurement/sales_view_v2/collect/mass_item_issue';
    $this->load->view('inc/template', $data);
  }

  // public function mass_item_transaction_csv_submit(){
  //   $this->load->library('csvimport'); 
  //   $creDir = 'uploads/';
  //   if ($_FILES['csv_file']['name'] != "") {
  //     $ret_val = $this->upload('csv_file', $creDir);
  //     if ($ret_val['status'] == 'success') {
  //       $file_data = $ret_val['data'];
  //     } else {
  //       $file_data = $ret_val['data'];
  //     }
  //   }else{
  //     $file_data="";
  //   }
  //   $file_path = 'uploads/'.$file_data['file_name'];

  //   if ($this->csvimport->get_array($file_path)) {
  //     $inventory_arr = $this->csvimport->get_array($file_path);
  //     echo "<pre>"; print_r($inventory_arr); die();
  //     $data['inventory_data'] = $this->Sales_model_v2->match_inventory_data($inventory_arr);
  //   }
  //   $data['main_content'] = 'feesv2/import';
  //   $this->load->view('inc/template', $data);
  // }

  // public function upload($filename = '', $upload_path) {
  //   $config['upload_path'] = $upload_path;
  //   $config['allowed_types'] = 'csv|CSV';
  //   $config['remove_spaces'] = true;
  //   $config['overwrite'] = false;
    
  //   $this->load->library('upload', $config);
  //   $this->upload->initialize($config);
  //   if (!$this->upload->do_upload($filename)) {
  //     $error = array('status' => 'error', 'data' => $this->upload->display_errors());
  //     $this ->session ->set_flashdata('flashError', 'Failed to upload - ' . $filename);
  //     return $error;
  //   } else {
  //     $image = $this->upload->data();
  //     $success = array('status' => 'success', 'data' => $image);
  //     return $success;
  //   }
  // }

  public function mass_item_issue_insert(){
    $student_id = $this->input->post('student_id');
    $proc_qt = $this->input->post('proc_qt');
    $proc_unit_price = $this->input->post('proc_unit_price');
    $proc_price = $this->input->post('proc_price');
    $proc_items_id = $this->input->post('proc_items_id');
    echo $this->Sales_model_v2->insert_items_details_by_student_wise($student_id, $proc_qt, $proc_unit_price, $proc_price, $proc_items_id);
  }

  public function category_wise_student_sales_report() {
    $data['category'] = $this->Sales_model_v2->get_category_list_report();
    $data['salesYear'] = $this->Sales_model_v2->get_sales_year();
    if($this->mobile_detect->isTablet()) {
      $data['main_content']= 'procurement/sales_view_v2/reports/category_wise_student_sales_report_mobile_tablet';
    }else if($this->mobile_detect->isMobile()){
        $data['main_content']= 'procurement/sales_view_v2/reports/category_wise_student_sales_report_mobile_tablet';
    }else { 
      $data['main_content'] = 'procurement/sales_view_v2/reports/category_wise_student_sales_report_view';
    }
    $this->load->view('inc/template', $data);

  }

  public function get_category_wise_student_sales_report() {
    $stdData= $this->Sales_model_v2->get_category_wise_student_sales_report();
    echo json_encode($stdData);
  }

  public function get_items_and_students_list() {
    $stdData= $this->Sales_model_v2->get_items_and_students_ids();
    // $stdData= $this->Sales_model_v2->get_items_and_students_list();
    echo json_encode($stdData);
  }

  public function get_item_wise_sales_report() {
    $stdData= $this->Sales_model_v2->get_item_wise_sales_report();
    // echo '<pre>'; print_r($stdData); die();
    echo json_encode($stdData);
  }

  public function test_with_queries() {
    $data['queries']= $this->Sales_model_v2->test_with_queries();
    $data['main_content'] = 'procurement/sales_view_v2/collect/test_with_queries';
    $this->load->view('inc/template', $data);
  }

  public function onchange_acad_year_get_classes() {
    echo json_encode($this->Sales_model_v2->onchange_acad_year_get_classes());
  }

  public function remove_item_from_transaction() {
    echo json_encode($this->Sales_model_v2->remove_item_from_transaction());
  }

  function pre_defined_templates() {
    $data['categories']= $this->Sales_model_v2->get_all_categories();
    $data['preDefTemplates']= $this->Sales_model_v2->get_predefined_templates();
    $data['main_content'] = 'procurement/sales_view_v2/collect/pre_defined_templates';
    $this->load->view('inc/template', $data);
  }

  public function onchange_category() {
    echo json_encode($this->Sales_model_v2->onchange_category());
  }

  // public function onchange_subcategory() {
  //   echo json_encode($this->Sales_model_v2->onchange_subcategory());
  // }

  public function add_templates() {
    echo json_encode($this->Sales_model_v2->add_templates());
  }

  public function remove_item_from_template() {
    echo json_encode($this->Sales_model_v2->remove_item_from_template());
  }

  public function edit_temp_name() {
    echo json_encode($this->Sales_model_v2->edit_temp_name());
  }

  public function get_defined_templates_item() {
    // $x= $this->Sales_model_v2->get_defined_templates_item();
    // echo '<pre>'; print_r($x); die();
    echo json_encode($this->Sales_model_v2->get_defined_templates_item());
  }

  // public function add_id_cards_for_2023_24() {
  //   echo json_encode($this->Sales_model_v2->add_id_cards_for_2023_24());
  // }

  function delete_temp_name() {
    echo json_encode($this->Sales_model_v2->delete_temp_name());
  }

  public function student_wise_missing_order_report() {
    $data['classList'] = $this->Sales_model_v2->getClassSectionNames();
    if($this->mobile_detect->isTablet()) {
      $data['main_content']= 'procurement/sales_view_v2/reports/student_wise_missing_order_report_mobile_tablet';
    }else if($this->mobile_detect->isMobile()){
        $data['main_content']= 'procurement/sales_view_v2/reports/student_wise_missing_order_report_mobile_tablet';
    }else { 
      $data['main_content'] = 'procurement/sales_view_v2/reports/student_wise_missing_order_report_view';
    }
    $this->load->view('inc/template', $data);

  }

  public function get_student_wise_missing_report(){
    $stdData= $this->Sales_model_v2->get_student_wise_missing_report($_POST);
    echo json_encode($stdData);
  }

  public function student_wise_order_history() {
    $data['studentNames'] = $this->Sales_model_v2->getstudentallNames();
    if($this->mobile_detect->isTablet()) {
      $data['main_content']= 'procurement/sales_view_v2/reports/student_wise_order_history_mobile_tablet';
    }else if($this->mobile_detect->isMobile()){
        $data['main_content']= 'procurement/sales_view_v2/reports/student_wise_order_history_mobile_tablet';
    }else { 
      $data['main_content'] = 'procurement/sales_view_v2/reports/student_wise_order_history_view';
    }
    $this->load->view('inc/template', $data);

  }

  public function get_student_wise_order_history(){
    $stdData= $this->Sales_model_v2->get_student_wise_order_history($_POST['std_id']);
    echo json_encode($stdData);
  }

  public function get_individual_item_list(){
    $stdData= $this->Sales_model_v2->get_individual_item_list($_POST['ppso_id']);
    echo json_encode($stdData);
  }

  public function parent_orders_summary_report(){
    $data['acad_year'] = $this->Sales_model_v2->get_acad_year_list();
    $data['item_category'] = $this->Sales_model_v2->get_category_items();
    $data['main_content'] = 'product/parent_orders_summary_report_view';

    if($this->mobile_detect->isTablet()) {
      $data['main_content']= 'product/parent_orders_summary_report_view_mobile';
    }else if($this->mobile_detect->isMobile()){
        $data['main_content']= 'product/parent_orders_summary_report_view_mobile';
    }else { 
      $data['main_content'] = 'product/parent_orders_summary_report_view';
    }

    $this->load->view('inc/template', $data);
  }

  public function get_parent_ordered_items(){
    $data = $this->Sales_model_v2->get_parent_ordered_items($_POST);
    echo json_encode($data);
  }
  
  public function parent_orders_report(){
    if($this->mobile_detect->isTablet()) {
      $data['main_content']= 'procurement/sales_view_v2/reports/parent_orders_report_view_mobile';
    }else if($this->mobile_detect->isMobile()){
        $data['main_content']= 'procurement/sales_view_v2/reports/parent_orders_report_view_mobile';
    }else { 
      $data['main_content'] = 'procurement/sales_view_v2/reports/parent_orders_report_view';
    }
    $this->load->view('inc/template', $data);
  }

  public function get_parent_orders_report_order_wise(){
    $data = $this->Sales_model_v2->get_parent_orders_report_order_wise($_POST);
    echo json_encode($data);
  }

  public function get_parent_orders_report_item_wise(){
    $data = $this->Sales_model_v2->get_parent_orders_report_item_wise($_POST);
    echo json_encode($data);
  }

  function getPreReservedItemsCategoriesWise() {
    $data = $this->Sales_model_v2->getPreReservedItemsCategoriesWise();
    echo json_encode($data);
  }

  function get_reserved_items_with_quantity() {
    $data = $this->Sales_model_v2->get_reserved_items_with_quantity();
    echo json_encode($data);
  }

  function submit_multi_cat_sales_form() {
    
    $input = $this->input->post();

    // echo '<pre>'; print_r($input); die();
   
    $data= [];
    $data['prodcuts']= $input['prodcuts'];
    $data['variants']= $input['variants'];
    $data['current_quantity']= $input['current_quantity'];
    $data['inv_master_id']= $input['inv_master_id'];
    $data['inv_item_id']= $input['inv_item_id'];
    $data['quantity']= $input['quantity'];
    $data['amount']= $input['amount'];

    $categories = [];
    foreach ($data['prodcuts'] as $product) {
        $parts = explode('_', $product);
        $categories[] = $parts[0]; // Get the category part
    }

    $uniqueCategories = array_unique($categories);

    $result = [];

    // For each category, create a chunk of all arrays
    foreach ($uniqueCategories as $category) {
        $categoryData = [];
        
        // Find all indices for this category
        $indices = array_keys(array_filter($categories, function($cat) use ($category) {
            return $cat == $category;
        }));
        
        // For each array in the original data, extract values for these indices
        foreach ($data as $key => $array) {
            $categoryData[$key] = array_values(array_intersect_key($array, array_flip($indices)));
        }
        
        $result[] = $categoryData;
    }

    

    $total_different_categories = count($result);
    $discounted_amount= 1*$input['discounted_amount'];
    $discount_per_category= 1*$input['discounted_amount'] / intval(($total_different_categories));
    $xxxx= 1;
    $transIds= [];
    foreach ($result as $key => $value) {
      $input_final= [];
      $input_final= $result[$key];

      $input_final['sale_type']= $input['sale_type'];
      $input_final['input_rfid']= $input['input_rfid'];
      $input_final['admission_no']= $input['admission_no'];
      $input_final['enrollment_no']= $input['enrollment_no'];
      $input_final['student_name']= $input['student_name'];
      $input_final['classes']= $input['classes'];
      $input_final['student']= $input['student'];
      $input_final['new_std_name']= $input['new_std_name'];
      $input_final['class_name']= $input['class_name'];
      $input_final['parent_name']= $input['parent_name'];
      $input_final['contact_number']= $input['contact_number'];
      $input_final['receipt_date']= $input['receipt_date'];
      $input_final['categories_subcategories']= $result[$key]['prodcuts'];
      $input_final['pre_defined_template']= $input['pre_defined_template'];

      $total= 0;
      $final= 0;
      foreach ($value['amount'] as $key1 => $val1) {
        $total += 1*$val1;
        $final += 1*$val1;
      }

      if($final - $discounted_amount < 0 ) {
        $disc= $final;
      } else {
        $disc = 1*$discounted_amount;
      }

      $discounted_amount= $discounted_amount - $disc;

      $input_final['total_amount']= $total;
      $input_final['final_amount']= $final;
      $input_final['discounted_amount']= $disc;
      $input_final['total_after_discount']= 1*$final - 1*$disc;
      $input_final['discount_mode_type']= $input['discount_mode_type'];
      $input_final['discount_amount_or_percentage_value']= $input['discount_amount_or_percentage_value'];
      $input_final['discounted_remarks_hidden']= $input['discounted_remarks_hidden']; 
      $input_final['payment_type']= $input['payment_type'];
      $input_final['bank_name']= $input['bank_name'];
      $input_final['branch_name']= $input['branch_name'];
      $input_final['cheque_dd_nb_cc_dd_number']= $input['cheque_dd_nb_cc_dd_number']; 
      $input_final['dd_number']= $input['dd_number']; 
      $input_final['bank_date']= $input['bank_date'];
      $input_final['cc_number']= $input['cc_number']; 
      $input_final['nb_number']= $input['nb_number']; 
      $input_final['remarks']= $input['remarks']; 

        $transIds[]= $this->submit_sales_transaction_category_wise($input_final, $xxxx);

        // sleep(1);
        $xxxx ++;
// break; return 1;
    }

     $this->session->set_userdata('sTransIds', $transIds);
      $this->session->set_userdata('std_id', $input['student']);
      $this->session->set_userdata('sale_type', $input['sale_type']);
      $this->session->set_userdata('new_student_name', $input['new_std_name']);
      // if(empty($sTransId)) {
        // echo '<pre>'; print_r($transIds); die();
      // }
      redirect('procurement/sales_controller_v2/receipts_sales');


    // echo json_encode(  array('status' => 'success', 'message' => 'Sales transaction submitted successfully. The receipts is generated successfully for each category. Go to the daily transactions to see all the receipts.', 'sTransIds' => $transIds, 'std_id' => $input['student'], 'sale_type' => $input['sale_type'], 'new_student_name' => $input['new_std_name'])  );
  }

  public function submit_sales_transaction_category_wise($input, $xxxx){

    // echo '<pre>'; print_r($input); die();

    $total_after_discount= isset($input['total_after_discount']) ? $input['total_after_discount'] : 0;
    if($total_after_discount < 0) {
      $this->session->set_flashdata('flashError', 'Total amount after discount cannot be negative.');
      redirect('procurement/sales_controller_v2/sales_collect');
    }

    if($this->settings->getSetting('enable_indus_single_window_approval_process') && isset($_POST['categories_subcategories'])){
      $cat_sub_id = explode('_',$_POST['categories_subcategories'][0]);
      $cat_id = $cat_sub_id[0];
      $catergory_name = $this->db->select('category_name')->from('procurement_itemmaster_category')->where('id',$cat_id)->get()->row()->category_name;

      $category_name_trimmed = strtolower(trim($catergory_name));
      if (
          $category_name_trimmed == 'uniforms' ||
          $category_name_trimmed == 'uniform' ||
          $category_name_trimmed == 'books' ||
          $category_name_trimmed == 'book'
      ) 
      {
          $this->Sales_model_v2->single_window_details($_POST['student'], $catergory_name);
      }
    }

    if ($input['sale_type'] == 'new') {
      $input['student'] = '';
    }
    $catIds = [];
    $products = [];
    foreach ($input['prodcuts'] as $key => $product) {
      list($catId, $productId) = explode('_', $product);
      array_push($catIds,$catId);
      array_push($products,$productId);
    }
    $temp = [];
    foreach ($catIds as $key => $catId) {
      $temp[$catId][] = array(
        'prodcuts'=>$products[$key],
        'variants'=>$input['variants'][$key],
        'quantity'=>$input['quantity'][$key],
        'amount'=>$input['amount'][$key],
        'current_quantity'=>$input['current_quantity'][$key],
      );
    }
    // Making it true to AVOID some cases
    if (true || $input['final_amount'] != 0) {
      $stransIds = [];
      
      foreach ($temp as $catId => $prodcuts) {
        $this->db->trans_begin();
// echo '<pre>PRE bro: '.$xxxx; print_r($temp); 
        $sTransId = $this->Sales_model_v2->insert_sales_transaction_unique_category($input['student'], $input, $catId, $prodcuts);
        // echo '<pre>PRE bro: '; print_r($sTransId); 
        if (empty($sTransId)) {
          $this->db->trans_rollback();
        }else{
          $manual_receipt = $this->settings->getSetting('sales_manual_receipt_number');
          if ($manual_receipt) {
            $result =  $this->Sales_model_v2->update_receipt_sale_transcation_manual($sTransId, $catId, $input);
          }else{
            $result =  $this->Sales_model_v2->update_receipt_sale_transcation($sTransId, $catId, $input);
          }
        if (empty($result)) {
          $this->db->trans_rollback();
          $this->session->set_flashdata('flashError', 'Receipt book not found');
          redirect('procurement/sales_controller_v2/sales_collect');
        }
          array_push($stransIds, $sTransId);
        }
        $this->db->trans_commit();
      }
// return 1;
      $enable_sales_notification_emails = $this->settings->getSetting('enable_procurement_send_notification_email_to_parent');
      // echo "<pre>Before If Condition<br>";print_r($enable_sales_notification_emails);die();
      if($enable_sales_notification_emails){
        // echo "<pre>after If Condition<br>";print_r($enable_sales_notification_emails);die();
        $student_and_parent_details= $this->Sales_model_v2->student_and_parent_details($input['student']);
        $alpha_rollnum= $student_and_parent_details['alpha_rollnum'];
        $enrollment_number= $student_and_parent_details['enrollment_number'];
        $student_name= $student_and_parent_details['student_name'];
        $ClassSection= $student_and_parent_details['class_name'].''.$student_and_parent_details['section_name'];
        $sales_details = $this->Sales_model_v2->get_sales_details($sTransId);
        $html = '';
        if ($sales_details != null) {

            $html .= '<h4>Student Name : '.$student_name.' </h4>';
            $html .= '<h4>Grade : '.$ClassSection.' </h4>';
            $html .= '<h4>Alpha Roll No. : '.$alpha_rollnum.' </h4>';
            $html .= '<h4>Enrollment No. : '.$enrollment_number.' </h4>';
            $html .= '<h4>Receipt No : '.htmlspecialchars($sales_details['sales_details']->receipt_no).' </h4>';
            $html .= '<h4>Receipt Date : '.htmlspecialchars($sales_details['sales_details']->receipt_date).'</h4>';
            $html .= '<h4>Total Amount : '.number_format($sales_details['sales_details']->total_amount, 2).'</h4>';

            if($this->settings->getSetting('procurement_sales_enable_discount_feature')) {
              $totalAmount= number_format($input['final_amount'], 2);
              $discountAmount= number_format($input['discounted_amount'], 2);
              $totalAmountAfterDiscount= number_format($input['total_after_discount'], 2);

              $html .= '<h4>Discount Amount : '.$discountAmount.'</h4>';
              $html .= '<h4>Amount After Discount : '.$totalAmountAfterDiscount.'</h4>';
            }

            $html .= '<h4>Receipt Details</h4>';
            $html .= '<table style="border: 1px solid #dee2e6; border-collapse: collapse; width: 100%;">';
            $html .= '<thead><tr>';
            $html .= '<th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">Item Name</th>';
            $html .= '<th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">Quantity</th>';
            $html .= '<th style="border: 1px solid #dee2e6; padding: 8px; text-align: left;">Amount</th>';
            $html .= '</tr></thead>';
            $html .= '<tbody>';

            foreach ($sales_details['items_details'] as $item) {
                $html .= '<tr>';
                $html .= '<td style="border: 1px solid #dee2e6; padding: 8px;">' . htmlspecialchars($item->item_name) . '</td>';
                $html .= '<td style="border: 1px solid #dee2e6; padding: 8px;">' . htmlspecialchars($item->quantity) . '</td>';
                $html .= '<td style="border: 1px solid #dee2e6; padding: 8px;">' . number_format($item->amount, 2) . '</td>';
                $html .= '</tr>';
            }

            $html .= '</tbody>';
            $html .= '</table>';
        } else {
            $html .= '<p>No sales details found.</p>';
        }

        // Email and Notification
        $parent_id_arr= [];
        $parent_email_arr= [];
        foreach($student_and_parent_details['parent_details'] as $key => $val) {
          array_push($parent_id_arr, $val->parent_id);
          array_push($parent_email_arr, $val->parent_email);
        }
        $circular = $this->Sales_model_v2->get_circular_data();
        $this->load->model('communication/emails_model');
        $this->load->model('communication/circular_model', 'circular');
        $this->load->model('communication/texting_model', 'texting_model');
        if(!empty($circular)){
          $from_email = $circular->registered_email;
          $email_ids = $parent_email_arr;
          $subject = $circular->email_subject;
          $body = $circular->content;
          if($circular->members_email != ''){
            $emails = explode(',', $circular->members_email);
            foreach ($emails as $email) {
              $email = trim($email);
              if ($email != '') {
                  array_push($email_ids, $email); 
              }
            }
          }
          $email_master_data = array(
            'subject' => $subject,
            'body' => $html,
            'source' => 'Email Template',
            'sent_by' => $this->authorization->getAvatarId(),
            'recievers' => implode(',', $parent_email_arr),
            'from_email' => $from_email,
            'files' => NULL,
            'acad_year_id' => $this->acad_year->getAcadYearId(),
            'visible' => 1,
            'sender_list' =>NULL,
            'sending_status' => 'Completed'
          );
          $email_master_id = $this->emails_model->saveEmail($email_master_data);
          $sent_data = [];
          if(!empty($email_master_id)){
            foreach ($student_and_parent_details['parent_details'] as $parent) {
              $obj = new stdClass();
              $obj->email_master_id = $email_master_id;
              $obj->id = $parent->parent_id;
              $obj->email = $parent->parent_email;
              $obj->avatar_type = 2;
              $obj->status = ($parent->parent_email) ? 'Awaited' : 'No Email';
              $sent_data[] = $obj;
            }
          }
          $this->load->model('communication/emails_model');
          $this->emails_model->save_sending_data($sent_data, $email_master_id);
          $this->load->helper('email_helper');
          sendEmail($html, $subject, $email_master_id, $email_ids, $from_email, json_decode(''));
        }

        
        $parent_id_str= implode(',', $parent_id_arr);
        $school_name = $this->settings->getSetting('school_name');
        $notification = "Dear parent, your kid - $student_name has bought some items from the school store. Please check your school app for more details. Thank you!";
        $credits = $this->texting_model->_calculateCredits($notification, 0);
        $text_master = array(
          'title' => $school_name,
          'message' => $notification,
          'sent_by' => $this->authorization->getAvatarId(),
          'reciever' => $parent_id_str,
          'acad_year_id' => $this->acad_year->getAcadYearId(),
          'source' => 'Sales',
          'text_count' => 0,
          'visible' => 1,
          'mode' => 'notification',
          'sms_credits' => $credits,
          'is_unicode' => 0,
          'sender_list' => NULL,
          'sending_status' => 'Initiated'
        );
        $texting_master_id = $this->texting_model->save_texts($text_master);

        $url = site_url('parent_controller/student_inventory');
        $textingData = [];
        foreach($student_and_parent_details['parent_details'] as $parent){
          $textingData[] = array(
            'texting_master_id' => $texting_master_id,
            'stakeholder_id' => $parent->parent_id,
            'mobile_no' => $parent->parent_mobile_no,
            'mode' => 1,
            'status' => ($parent->tokenState == 0 )? 'No Token' : 'Sent',
            'avatar_type' => 2,
            'is_read' => 0,
            'user_id' => $parent->user_id,
            'token' => $parent->user_token,
          );
        }
        $token_data = $this->texting_model->save_notifications($textingData);
        $this->load->helper('notification_helper');
        $notification_status = commonNotifications($token_data, $school_name, $notification, $url);
      }

    }
    // $obj= new stdClass();
    return $stransIds[0];
    // return 1;
  }

  function submit_sales_transactionV2() {
    // sleep(4);
    $this->session->set_flashdata('flashSuccess', 'Successfully Updated.');
    redirect('procurement/sales_controller_v2/sales_collect');
  }

}

?>