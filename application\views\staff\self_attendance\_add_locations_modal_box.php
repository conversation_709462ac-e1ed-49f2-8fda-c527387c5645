<div id="_add_locations_modal_box" class="modal fade" tabindex="-1" role="dialog" data-backdrop="static">
    <div class="modal-dialog modal-dialog-scrollable modal-x1" style="">
        <div class="modal-content" style="border-radius: 8px;">
            <div class="modal-header" style="border-bottom: 2px solid #ccc;">
                <h4 class="modal-title" id="modalHeader">Add Geo-fence</h4>
                <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;"
                    type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <form enctype="multipart/form-data" data-parsley-validate="" method="post" class="form-horizontal"
                id="add_locations_form">
                <div class="modal-body" style="">
                    <section class="first_half" style="">
                        <div class="form-group">
                            <div class="">
                                <label class="control-label" for="geo_fence_name">Enter Geo-fence Name <span
                                        style="color:red;">*</span></label>
                                <input id="geo_fence_name" name="geo_fence_name" type="text" class="form-control"
                                    required="" maxlength="50" placeholder="Enter Geo-fence Name">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="">
                                <label class="control-label" for="latitude">Enter Latitude  <span
                                        style="color:red;">*</span></label>
                                <input id="latitude" name="latitude" type="number" step="0.000001" class="form-control"
                                    required="" placeholder="Enter Latitude" min="-90" max="90">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="">
                                <label class="control-label" for="longitude">Enter Longitude  <span
                                        style="color:red;">*</span></label>
                                <input id="longitude" name="longitude" type="number" step="0.000001"
                                    class="form-control" required="" placeholder="Enter Longitude" min="-180" max="180">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="">
                                <label class="control-label" for="radius">Enter Radius(In meter)  <span
                                        style="color:red;">*</span></label>
                                <input id="radius" name="radius" type="number" step="0.01" class="form-control"
                                    required="" placeholder="Enter Radius" min="1" max="10000">
                            </div>
                        </div>
                    </section>
                </div>
                <div class="bg-light" style="text-align:center;">
                    <button type="button" class="btn btn-primary m-2" id="create_btn"
                        onclick="createLocation()">Create</button>
                    <button type="button" class="btn btn-secondary" id="close_btn" data-dismiss="modal">Close</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script type="text/javascript">
    async function createLocation() {
        let forms = $('#add_locations_form');
        // Manual validation in addition to parsley
        let name = $("#geo_fence_name").val().trim();
        let lat = parseFloat($("#latitude").val());
        let lng = parseFloat($("#longitude").val());
        let radius = parseFloat($("#radius").val());
        let errors = [];

        if (name === "") {
            errors.push("Geo-fence Name is required.");
        } else if (name.length > 50) {
            errors.push("Geo-fence Name must be at most 50 characters.");
        }
        if (isNaN(lat) || lat=="" || lat < -90 || lat > 90) {
            errors.push("Latitude must be between -90 and 90.");
        }
        if (isNaN(lng) || lng=="" || lng < -180 || lng > 180) {
            errors.push("Longitude must be between -180 and 180.");
        }
        if (isNaN(radius) || radius=="" || radius < 1 || radius > 10000) {
            errors.push("Radius must be between 1 and 10000 meters.");
        }

        if (errors.length > 0) {
            // Add SweetAlert for alert
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                html: errors.join('<br>')
            });
            return;
        }

        if (forms.parsley().validate()) {
            let form = $('#add_locations_form')[0];
            let form_data = new FormData(form);
            await $.ajax({
                url: '<?php echo site_url('staff/attendance/add_new_staff_locations'); ?>',
                type: 'post',
                data: form_data,
                processData: false,
                contentType: false,
                success: function (data) {
                    $("#close_btn").click()
                    $("#geo_fence_name").val("")
                    $("#latitude").val("");
                    $("#longitude").val("");
                    $("#radius").val("");

                    // Add SweetAlert for success
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        html: `Added <b>${name}</b> Geo-fence successfully`
                    });
                }
            });
            get_staff_attendance_locations()
        }
    }
</script>
<style type="text/css">
    @media only screen and (min-width:668px) {
        .modal-content {
            width: 40%;
            margin: auto;
        }
    }

    @media only screen and (max-width:667px) {
        .modal-content {
            width: 100%;
            margin: auto;
        }
    }
</style>