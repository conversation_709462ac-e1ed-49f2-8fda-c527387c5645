<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/invoice_controller_v2/index_v2'); ?>">Goods Delivery Challan
            Dashboard</a></li>
    <li>Item Wise Delivery Challan</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <h3 class="">
                    <a href="<?php echo site_url('procurement/invoice_controller_v2/index_v2'); ?>"
                        class="back_anchor control-primary"><span class="fa fa-arrow-left"></span></a>
                    Item Wise Delivery Challan
                </h3>
            </div>
        </div>
        <div class="panel-body">
            <div class="col-md-12" id="filter_div">
                <div class="col-md-3">
                    <label for="salesYear">Sales Year</label>
                    <select name="salesYear" id="salesYear" class="form-control select2">
                        <option value="">Select Sales Year</option>
                        <?php
                        if (!empty($salesYear)) {
                            foreach ($salesYear as $key => $val) {

                                if ($val->is_active == 1) {
                                    $selected = 'selected';
                                } else {
                                    $selected = '';
                                }
                                echo "<option $selected value='$val->id'>$val->year_name</option>";
                            }
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="item_id">Select Item</label>
                    <select name="item_id" id="item_id" class="form-control select2" onchange="onchange_item()">
                        <option value="">Select Item</option>
                        <?php
                        if (!empty($itemList)) {
                            foreach ($itemList as $key => $val) {
                                echo "<option value='$val->id'>$val->item_name</option>";
                            }
                        }
                        ?>
                    </select>
                </div>
            </div>
            <div class="col-md-12" style="height: 20px;"> </div>
            <div class="col-md-12" id="itemWiseDeliveryNote"> </div>
        </div>
    </div>
</div>

<!-- Modal to update selling rice -->
<div class="modal fade" id="update-selling-price-modal" tabindex="-1" role="dialog" style="width:70%;margin:auto;top:0%"
    data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-content modal-dialog" style="border-radius: 8px;">
        <div class="modal-header" style="border-bottom: 2px solid #ccc;">
            <h4 class="modal-title" id="modalHeader_vrm"></h4>
            <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;"
                type="button" class="close" data-dismiss="modal">&times;</button>
        </div>
        <form id="myform_update_selling_price">
            <input type="hidden" value="" name="item_name" id="item-name-id" />
            <div class="modal-body">
                <div class="col-md-12" id="item-wise-delivery-notes">

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="update_selling_price(this)">Submit</button>
            </div>
        </form>
    </div>
</div>




<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

<script>
    $(document).ready(function () {
        $('.select2').select2();
    });

    function onchange_item() {
        var item_id = $('#item_id').val();
        var salesYear = $('#salesYear').val();
        if (item_id != '' && salesYear != '') {
            $.ajax({
                url: '<?php echo site_url('procurement/invoice_controller_v2/getItemWiseSalesYearWiseVendors'); ?>',
                type: 'POST',
                data: { item_id: item_id, salesYear: salesYear },
                success: function (response) {
                    response = JSON.parse(response);
                    __construct_vendor_return_table(response);
                }
            });
        } else {
            $('#itemWiseDeliveryNote').html('');
            alert('Please select Sales Year and Item');
        }
    }

    function __construct_vendor_return_table(response) {
        if (!response || !response.length) {
            $('#itemWiseDeliveryNote').html('<p>No data available</p>');
            return;
        }

        let itemName = $('#item_id option:selected').text();
        let itemId = $('#item_id').val();

        var html = '<table class="table table-bordered">';
        html += '<thead>';
        html += '<tr>';
        html += '<th>#</th>';
        html += '<th>Vendor</th>';
        html += '<th>Date</th>';
        html += '<th>Vendor Code</th>';
        html += '<th>Bill No</th>';
        html += '<th>HSN SAC</th>';
        html += '<th>Challan Type</th>';
        html += '<th>Initial Quantity</th>';
        html += '<th>Total Amount</th>';
        html += '<th>Current Quantity</th>';
        html += '<th>Cost Price</th>';
        html += '<th>sGST%</th>';
        html += '<th>cGST%</th>';
        html += '<th>Unit Price</th>';
        html += '<th>Selling Price</th>';
        html += `<th>
                    <button  disabled onclick="onclick_vendor_return_button()"  class="btn btn-warning" id="return_btn">Update Selling Price</button>
                </th>`;
        html += '</tr>';
        html += '</thead>';
        html += '<tbody>';
        let TotalInitialQuantity = 0;
        let TotalCurrentQuantity = 0;
        $.each(response, function (key, val) {
            TotalInitialQuantity += parseInt(val.initial_quantity);
            TotalCurrentQuantity += parseInt(val.current_quantity);
            html += '<tr>';
            html += '<td>' + (key + 1) + '</td>';
            html += '<td>' + val.vendor_name + '</td>';
            html += '<td>' + val.delivery_date + '</td>';
            html += '<td>' + val.vendor_code + '</td>';
            html += '<td>' + val.bill_no + '</td>';
            html += '<td>' + val.hsn_sac_no + '</td>';
            html += '<td>' + val.dc_type + '</td>';
            html += '<td>' + Number(val.initial_quantity).toLocaleString() + '</td>';
            html += '<td>' + Number(val.total_amount).toLocaleString() + '</td>';
            html += '<td>' + Number(val.current_quantity).toLocaleString() + '</td>';
            html += '<td>' + Number(val.price).toLocaleString() + '</td>';
            html += '<td>' + val.sgst + '</td>';
            html += '<td>' + val.cgst + '</td>';
            html += '<td>' + parseFloat(val.calculated_unit_price).toFixed(2).toLocaleString() + '</td>';
            html += '<td>' + parseFloat(val.selling_price).toFixed(2).toLocaleString() + '</td>';
            html += `<td>
                        <input data-delivery_date="${val.delivery_date}" data-current_selling_price="${val.selling_price}" ${Number(val.current_quantity) <= 0 ? 'disabled' : ''} onclick="onclick_return_checkbox()" value="${(val.vendor_name).replaceAll("'", "")}___${val.invoice_items_id}" data-price="${val.price}" data-cgst="${val.cgst}" data-sgst="${val.sgst}" data-invoice_master_id="${val.invoice_master_id}" data-invoiceItemId="${val.invoice_items_id}" data-initial_quantity="${val.initial_quantity}" data-new_unit_price="${val.calculated_unit_price}" data-current_quantity="${val.current_quantity}" data-total_amount="${val.total_amount}" type="checkbox" class="checkbox_for_return_class">
                    </td>`;
            html += '</tr>';
        });
        html += '</tbody>';
        html += '<tfoot>';
        html += '<tr>';
        html += '<th colspan="7" style="text-align: center;">Total</th>';
        html += '<td style="font-weight: bold;">';
        html += Number(TotalInitialQuantity).toLocaleString();
        html += '</td>';
        html += '<td></td>';
        html += '<td style="font-weight: bold;">';
        html += Number(TotalCurrentQuantity).toLocaleString();
        html += '</td>';
        html += '</tr>';
        html += '</tfoot>';
        html += '</table>';
        $('#itemWiseDeliveryNote').html(html);
    }

    function onclick_return_checkbox() {
        let is_checked = false;
        $("input.checkbox_for_return_class").each(function () {
            if ($(this).is(':checked')) {
                is_checked = true;
                return false;
            }
        });
        if (is_checked) {
            $("#return_btn").prop('disabled', false);
        } else {
            $("#return_btn").prop('disabled', true);
        }
    }

    function onclick_vendor_return_button() {
        let current_selling_price = [];
        let delivery_date = [];
        let invoiceMasterId = [];
        let invoiceItemId = [];
        let initial_quantity = [];
        let new_unit_price = [];
        let variantName = [];
        let current_quantity = [];
        let total_amount = [];
        let price = [];
        let cgst = [];
        let sgst = [];
        $("#modalHeader_vrm").html(`Update Selling Price for Item- <b>${$("#item_id option:selected").text()}</b>`);
        $("#item-name-id").val($("#item_id option:selected").text());

        $("input.checkbox_for_return_class").each(function () {
            if ($(this).is(':checked')) {
                var current_selling_price1 = $(this).data('current_selling_price');
                var delivery_date1 = $(this).data('delivery_date');
                var invoiceMasterId1 = $(this).data('invoice_master_id');
                var initial_quantity1 = $(this).data('initial_quantity');
                var new_unit_price1 = $(this).data('new_unit_price');
                var variantName1___invoiceItemId1 = $(this).val();
                var both = variantName1___invoiceItemId1.split('___');
                var variantName1 = both[0];
                var invoiceItemId1 = both[1];
                var current_quantity1 = $(this).data('current_quantity');
                var total_amount1 = $(this).data('total_amount');
                var price1 = $(this).data('price');
                var cgst1 = $(this).data('cgst');
                var sgst1 = $(this).data('sgst');

                current_selling_price.push(current_selling_price1);
                delivery_date.push(delivery_date1);
                invoiceItemId.push(invoiceItemId1);
                invoiceMasterId.push(invoiceMasterId1);
                initial_quantity.push(initial_quantity1);
                new_unit_price.push(new_unit_price1);
                variantName.push(variantName1);
                current_quantity.push(current_quantity1);
                total_amount.push(total_amount1);
                price.push(price1);
                cgst.push(cgst1);
                sgst.push(sgst1);
            }
        });

        if (variantName.length) {
            let html = `
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Vendor</th>
                                    <th>Date</th>
                                    <th>Total</th>
                                    <th>Utilized</th>
                                    <th>Remaining</th>
                                    <th>Price</th>
                                    <th>cGST%</th>
                                    <th>sGST%</th>
                                    <th>Unit Price</th>
                                    <th>Current Selling Price</th>
                                    <th>Update Selling Price To</th>
                                </tr>
                            </thead>
                            <tbody>`;
            for (var i in variantName) {
                html += `<tr>
                            <td>${variantName[i]}</td>
                            <td>${delivery_date[i]}</td>
                            <td>${Number(initial_quantity[i]).toLocaleString()}</td>
                            <td>${(Number(initial_quantity[i]) - Number(current_quantity[i])).toLocaleString()}</td>
                            <td>${Number(current_quantity[i]).toLocaleString()}</td>

                            <td>${Number(price[i]).toLocaleString()}</td>
                            <td>${Number(cgst[i]).toLocaleString()}</td>
                            <td>${Number(sgst[i]).toLocaleString()}</td>

                            <td>${Number(new_unit_price[i]).toLocaleString()}</td>
                            <td>
                                
                                <input type="hidden" value="${variantName[i]}" name="vendor_names[]" />
                                <input type="hidden" value="${invoiceMasterId[i]}" name="invoice_master_id[]" />
                                <input type="hidden" value="${invoiceItemId[i]}" name="invoice_items_id[]" />
                                <input readonly class="form-control" type="number" step="0.1" value="${Number(current_selling_price[i])}" name="current_selling_price[]" />
                                
                            </td>
                            <td>
                                <input id="updated_selling_price${Number(i)}" class="form-control" value="${Number(current_selling_price[i])}" type="number" step="0.1" name="updated_selling_price[]" />
                            </td>
                        </tr>`
            }
            html += `</tbody>
                    </table>
                    <div class="col-md-12" style="height: 10px;"></div>
                    <div class="form-group">
                        <label for="selling_price_update_remarks">Remarks</label>
                        <textarea name="selling_price_update_remarks" id="selling_price_update_remarks" class="form-control"></textarea>
                    </div>
                    <div class="col-md-12" style="height: 20px;"></div>`;
            $("#item-wise-delivery-notes").html(html);
            $("#update-selling-price-modal").modal('show');
        }
    }

    // function change_total_return_price(current, new_unit_price, index, current_quantity) {
    //     let input_quantity= $(current).val();
    //     $("#updated_selling_price" + index).val( Number(input_quantity)*Number(new_unit_price) );
    //     if(Number(current_quantity) < Number(input_quantity)) {
    //         alert(`Quantity cannot be exceed to ${Number(current_quantity)}`);
    //     }
    // }

    function update_selling_price(current) {
        Swal.fire({
            title: 'Update Selling Price?',
            text: "Are you sure you want to update this selling price?",
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, update it!',
            cancelButtonText: 'No, cancel',
            background: '#fff'
        }).then((result) => {
            if (result.isConfirmed) {
                update_selling_price_after_confirm(current);
            } else if (result.dismiss === Swal.DismissReason.cancel) {
                // User clicked "No, cancel"
                Swal.fire(
                    'Cancelled',
                    'The selling price remains unchanged :)',
                    'error'
                );
            }
        });
    }

    function update_selling_price_after_confirm(current) {
        var $form = $('#myform_update_selling_price');
        if ($form.parsley().validate()) {
            $(current).prop('disabled', true).html('Please Wait ...');
            var form = $('#myform_update_selling_price')[0];
            var formData = new FormData(form);
            $.ajax({
                url: '<?php echo site_url('procurement/invoice_controller_v2/update_selling_price_deliovery_price'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                cache: false,
                success: function (data) {
                    $(current).prop('disabled', false).html('Submit');
                    if (data) {
                        Swal.fire({
                            icon: 'success',
                            text: `Successfully selling price updated!`,
                            timer: 3000
                        }).then((result) => {
                            window.location.reload();
                        });
                    } else {
                        Swal.fire({
                            icon: 'Error',
                            text: `Successfully selling price update process failed!`
                        }).then((result) => {
                            window.location.reload();
                        });
                    }
                }
            });
        }
    }
</script>