<?php

class Vendor_model_v2 extends CI_Model{
    public function __construct(){
        parent::__construct();
    }
    
    //get the vendors
    public function get_vendor_details($vendor_id = 0){
        $this->db->select('*');
        if($vendor_id){
            $this->db->where('id', $vendor_id);
            return $this->db->get('procurement_vendor_master')->row();
        }
        return $this->db->where('status', 1)->get('procurement_vendor_master')->result();
    }

    public function get_vendor_basic_info($vendor_id) {
        $this->db->select('id, vendor_name, DATE_FORMAT(on_board, "%d-%m-%Y") as onBoard, status');
        $this->db->where('id', $vendor_id);
        return $this->db->get('procurement_vendor_master')->row();
    }

    public function getVendors() {
        return $this->db->select("id, vendor_code, vendor_name, gst_no, vendor_email, vendor_website, contact_number, CONCAT(ifnull(contact_first_name, ''), ' ', ifnull(contact_last_name, '')) as contact_name, email as contact_email, customer_service_number, DATE_FORMAT(on_board, '%d-%m-%Y') as onBoard, status")
        ->order_by('id', 'DESC')
        ->get('procurement_vendor_master')->result();
    }


    public function get_vendor_code(){
        $vendor = $this->db->select('id')->order_by('id', 'DESC')->limit(1)->get('procurement_vendor_master')->row();
        if(empty($vendor))
            return 1;
        $id = $vendor->id + 1;
        return $id;
    }

    //insert new vendor
    public function addNewVendor(){
        $number = $this->get_vendor_code();
        $vendor_code = 'V'.sprintf("%'.05d",$number);
        $input = $this->input->post();
        $onBoard = date('Y-m-d', strtotime($input['on_board']));
        $nm= $input['vendor_name'];
        $is_exist= $this->db->where("vendor_name", $nm)->get('procurement_vendor_master')->result();
        if(!empty($is_exist)) {
            return -1;
        }
        $data = array(
            'vendor_code' => $vendor_code,
            'vendor_name' => $input['vendor_name'],
            'gst_no' => $input['gst_no'],
            'vendor_email' => $input['vendor_email'],
            'vendor_website' => $input['vendor_website'],
            'contact_first_name' => $input['contact_first_name'],
            'contact_last_name' => $input['contact_last_name'],
            'email' => $input['email'],
            'contact_number' => $input['contact_no'],
            'customer_service_number' => $input['customer_service_no'],
            'last_modified_by' => $this->authorization->getAvatarId(),
            'on_board' => $onBoard,
            'vendor_pan_number' => $input['pan_number']
        );

        return $this->db->insert('procurement_vendor_master', $data);
    }

    //check vendor address and fetch vendor_id, vendor_type and id
    public function checkVendorAddress($vendor_id){
        $this->db->select('*');
        $this->db->where('vendor_id', $vendor_id);
        return $this->db->get('procurement_vendor_address_info')->result();
    }

    //update vendor info
    public function updateVendorInfo($vendor_id){
        $data = array(
            'vendor_name' => $this->input->post('vendor_name'),
            'gst_no' => $this->input->post('gst_no'),
            'vendor_email' => $this->input->post('vendor_email'),
            'vendor_website' => $this->input->post('vendor_website'),
            'contact_first_name' => $this->input->post('contact_first_name'),
            'contact_last_name' => $this->input->post('contact_last_name'),
            'email' => $this->input->post('email'),
            'contact_number' => $this->input->post('contact_no'),  
            'customer_service_number' => $this->input->post('customer_service_no'),
            'vendor_pan_number' => $this->input->post('pan_number'),
            'last_modified_by' => $this->authorization->getAvatarId()
        );

        $this->db->where('id', $vendor_id);
        return $this->db->update('procurement_vendor_master', $data);
    }

    //Delete Vendor Address
    public function deleteVendor($vendor_id){
        //delete vendor address first
        $this->db->where('vendor_id', $vendor_id);
        $status = $this->db->delete('procurement_vendor_address_info');
        if($status) {
            //if address deleted
            $this->db->where('id', $vendor_id);
            return $this->db->delete('procurement_vendor_master');
        } else {
            return 0;
        }
    }

    public function generateAddressData($vendor_id){
        
        $data['office'] = array(
            'vendor_id' => $vendor_id,
            'address_type' => 0,
            'address_line1' => $this->input->post('a_present_add'),
            'address_line2' => $this->input->post('a_present_add2'),
            'area' => $this->input->post('a_present_area'),
            'district' => $this->input->post('a_present_district'),
            'state' => $this->input->post('a_present_state'),
            'country' => $this->input->post('a_present_country'),
            'pin_code' => $this->input->post('a_present_pin_code'),
            'last_modified_by' => $this->authorization->getAvatarId()
        );

        $data['warehouse'] = array(
            'vendor_id' => $vendor_id,
            'address_type' => 1,
            'address_line1' => $this->input->post('a_permanent_add'),
            'address_line2' => $this->input->post('a_permanent_add2'),
            'area' => $this->input->post('a_permanent_area'),
            'district' => $this->input->post('a_permanent_district'),
            'state' => $this->input->post('a_permanent_state'),
            'country' => $this->input->post('a_permanent_country'),
            'pin_code' => $this->input->post('a_permanent_pin_code'),
            'last_modified_by' => $this->authorization->getAvatarId()
        );
        return $data;
    }

    //insert new address for vendor
    public function submitVendorAddressInfo($address){
        return $this->db->insert_batch('procurement_vendor_address_info', $address);
    }

    //update vendor address
    public function updateVendorAddressInfo($vendor_id,$address_type,$address){
        $this->db->where(array('vendor_id' => $vendor_id, 'address_type' => $address_type));
        return $this->db->update('procurement_vendor_address_info', $address);
    }

    public function addPaymentInstrument($vendor_id){
        $input = $this->input->post();
        $name = trim($input['name']);
        $check = $this->db->select('id')->where('vendor_id', $vendor_id)->where('name', $name)->get('inventory_payment_instruments')->result();

        if(!empty($check)) {
            return -1;
        }
        $data = array(
            'name' => $name,
            'vendor_id' => $vendor_id,
            'payment_type' => $input['payment_type'],
            'bank_name' => ($input['bank_name'] != '')?$input['bank_name']:null,
            'account_number' => ($input['account_no'] != '')?$input['account_no']:null,
            'ifsc_code' => ($input['ifsc_code'] != '')?$input['ifsc_code']:null,
            'branch' => ($input['branch_name'] != '')?$input['branch_name']:null,
            'cheque_in_favor_of' => ($input['favor_of'] != '')?$input['favor_of']:null,
            'last_modified_by' => $this->authorization->getAvatarId()
        );
        return $this->db->insert('procurement_vendor_payment_instruments', $data);
    }

    public function getPaymentInsData($vendor_id){
        return $this->db->select('*')->where('vendor_id', $vendor_id)->get('procurement_vendor_payment_instruments')->result();
    }

    // public function getProducts(){
    //     return $this->db->select('id, product_code, subcategory_name')->get('procurement_itemmaster_subcategory')->result();
    // }

    public function addObservationRating($observation, $rating, $vendor_id) {
        $data = array(
            'vendor_id' => $vendor_id,
            'observation' => $observation,
            'rating' => $rating,
            'created_by' => $this->authorization->getAvatarId()
        );
        return $this->db->insert('procurement_vendor_observations', $data);
    }

    public function getObservations($vendor_id) {
        $res= $this->db_readonly->select("ob.id,ob.observation, ob.rating,DATE_FORMAT(ob.created_on, '%d-%m-%Y') as createdOn, CONCAT(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as observer")
            ->from('procurement_vendor_observations ob')
            ->join('avatar a', 'a.id=ob.created_by', 'left')
            ->join('staff_master sm', 'sm.id=a.stakeholder_id', 'left')
            ->where('ob.vendor_id', $vendor_id)
            ->order_by('ob.created_on', 'DESC')
            ->get()->result();

        return $res;
    }

    public function getCategories() {
        return $this->db->select('*')
                ->from('procurement_itemmaster_category pic')
                ->get()->result();
    }

    public function getUnassignedCategories($vendor_id) {
        return $this->db->select('*')
                ->from('procurement_itemmaster_category pic')
                ->where("id not in (select proc_im_category_id from procurement_vendor_category where vendor_id=$vendor_id)")
                ->where('status', 1) // Only active categories
                ->get()->result();
    }

    public function getVendorCategoriesData($vendor_id) {
        return $this->db->select('pic.category_name')
                ->from('procurement_vendor_category pvc')
                ->join('procurement_itemmaster_category pic', 'pic.id=pvc.proc_im_category_id')
                ->where('pvc.vendor_id', $vendor_id)
                ->get()->result();
    }

    public function getVendorCategories($vendor_id) {
        $result = $this->db_readonly->select('pic.id, pic.category_name, ifnull(pvc.proc_im_category_id, "no") as is_assigned')
                ->from('procurement_itemmaster_category pic')
                ->join('procurement_vendor_category pvc', "pic.id=pvc.proc_im_category_id and pvc.vendor_id= $vendor_id", 'left')
                ->order_by('pic.id', 'asc')
                ->get()->result();

        return $result;
    }

    public function changeVendorStatus($status, $vendor_id) {
        $this->db->where('id', $vendor_id);
        return $this->db->update('procurement_vendor_master', array('status' => $status));
    }

    public function removePaymentInstrument($id) {
        return $this->db->where('id', $id)->delete('procurement_vendor_payment_instruments');
    }

    public function assign_unAssign_category($vendor_id, $proc_im_category_id, $is_call_for_delete) {
        if( $is_call_for_delete == 'yes') { //deletion
            $a= $this->db->where('vendor_id', $vendor_id)
            ->where('proc_im_category_id', $proc_im_category_id)
            ->delete('procurement_vendor_category');

            if( $a ) {
                return 1;
            }

        } else { // assigning
            $data = array(
                'vendor_id' => $vendor_id,
                'proc_im_category_id' => $proc_im_category_id
            );
            $b= $this->db->insert('procurement_vendor_category', $data);

            if( $b ) {
                return 2;
            }
        }

        return 0;
    }

    public function getVariants(){
        $product = $_POST['products'];
        return $this->db->select('id, item_name as name')->where('proc_im_subcategory_id', $product)->where('status', 1)->get('procurement_itemmaster_items')->result();
    }
}