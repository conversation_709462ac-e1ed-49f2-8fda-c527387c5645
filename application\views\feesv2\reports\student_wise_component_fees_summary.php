<ul class="breadcrumb">
  <li><a href="<?php echo site_url('avatars');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('feesv2/fees_dashboard');?>">Fee Dashboard</a></li>
  <li>Fee Detail Component wise Report</li>
</ul>
<?php
  function requireFilter ($filterArray, $filter) {
    foreach ($filterArray as $f) {
      if ($f == $filter)
        return 1;
    }
    return 0;
  }
?>
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px">
        <div class="col-md-8">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('feesv2/fees_dashboard');?>">
              <span class="fa fa-arrow-left"></span>
            </a> 
           Fee Detail Component wise Report
          </h3>
        </div>
      </div>
    </div>
<style type="text/css">
  p{
    margin-bottom: .5rem;
  }
  input[type=checkbox]{
    margin: 0px 4px;
  }
</style>
  <div class="card-body">
    <div class="col-md-12">
      <div class="row" style="margin: 0px">

       <div class="col-md-3 form-group" id="multiBlueprintSelect">
        <p>Select Fee Type</p>
        <select class="form-control multiselect select" multiple title='All' id="fee_type" name="fee_type">
          <?php foreach ($fee_blueprints as $key => $val) { ?>
            <option value="<?= $val->id ?>"><?php echo $val->name ?></option>
          <?php } ?>
        </select>
      </div>

        <div class="col-md-3 form-group">
          <p>Class</p>
          <?php
            $array = array();
            foreach ($classes as $key => $class) {
              $array[$class->classId] = $class->className;
            }
            echo form_dropdown("class_name[]", $array, set_value("class_name"), "id='classId' multiple title='Select Classes' class='form-control classId select '");
          ?>
        </div>

        <div class="col-md-3 form-group">
          <p>Class/Section</p>
          <?php
            $array = array();
            $array[0] = 'Select Section';
            foreach ($classSectionList as $key => $cl) {
              $array[$cl->id] = $cl->class_name . $cl->section_name;
            }
            echo form_dropdown("classSectionId", $array, '', "id='classSectionId' multiple title='Select Class/Section' class='form-control select'");
          ?>
        </div>

        <div class="col-md-3 form-group">
          <p for="sectionId">Admission Status</p>
          <select id="admission_status" name="admission_status" class="form-control input-md">
            <option value=""><?php echo "All" ?></option>
            <?php foreach ($admission_status as $value => $type) { ?>
              <option value="<?php echo $value ?>"><?php echo $type ?></option>
            <?php } ?>
          </select>
        </div>

        <div class="col-md-3 form-group">
          <p>Admission Type</p>
          <?php
            $array = array();
            $array[0] = 'Select Admission Type';
            foreach ($admission_type as $key => $admission) {
              $array[$key] = ucfirst($admission);
            }
            echo form_dropdown("admission_type", $array, set_value("admission_type"), "id='admission_type' class='form-control'");
          ?>
        </div>

        <?php if ($category) { ?>
        <div class="col-md-3 form-group">
          <p>Category</p>
          <?php
            $array = array();
            $array[0] = 'Select Category';
            foreach ($category as $key => $cat) {
              $array[$key] = ucfirst($cat);
            }
            echo form_dropdown("category", $array, set_value("category"), "id='category' class='form-control'");
          ?>
        </div>
        <?php } ?>

        <?php if ($rteType) { ?>
        <div class="col-md-3 form-group">
          <p>RTE</p>
          <?php
            $array = array();
            $array[0] = 'Select RTE';
            foreach ($rteType as $key => $rte) {
              $array[$key] = ucfirst($rte);
            }
            echo form_dropdown("rte_nrteId", $array, set_value("rte_nrteId"), "id='rte_nrteId' class='form-control'");
          ?>
        </div>
        <?php } ?>

        <?php if ($donors) { ?>
        <div class="col-md-3 form-group">
          <p>Donors</p>
          <?php
            $array = array();
            $array[0] = 'Select Donors';
            foreach ($donors as $key => $donor) {
              if(!empty($donor->id)){
                $array[$donor->id] = $donor->name;
              }
            }
            echo form_dropdown("donorsId", $array, set_value("donorsId"), "id='donorsId' class='form-control'");
          ?>
        </div>
        <?php } ?>

        <?php if ($combination) { ?>
        <div class="col-md-3 form-group">
          <p>Combination</p>
          <?php
            $array = array();
            $array[0] = 'Select Combination';
            foreach ($combination as $key => $comb) {
              $array[$comb->id] = $comb->name;
            }
            echo form_dropdown("combination", $array, set_value("combination"), "id='combination' class='form-control'");
          ?>
        </div>
        <?php } ?>

        <div class="col-md-3 form-group">
          <p>Staff Kid</p>
          <select id="staff_kid" name="staff_kid" class="form-control input-md">
            <option value="">All</option>
            <option value="1">Yes</option>
            <option value="0">No</option>
          </select>
        </div>

        <?php if($this->authorization->isAuthorized('FEESV2.STUDENT_WISE_PREDEFINED_FILTERS')) { ?>
          <div class="col-md-4 report-controls-wrapper">
          <div class="form-group">
            <label class="control-label" style="margin-bottom: 6px;">Saved Reports</label>
            <div class="report-row">
              <select name="" id="filter_types" class="form-control grow-select" onchange="selectFilters()">
                <option value="">Select Report</option>
              </select>

              <?php if($this->authorization->isAuthorized('FEESV2.STUDENT_WISE_PREDEFINED_FILTERS')) { ?>
                <div class="dc-buttons">
                  <input type="button" name="reload" id="reload_filter" class="btn btn-info" value="Reload" onclick="selectFilters()">
                  <input type="button" name="save" id="save_filter" class="btn btn-info" value="Save">
                  <input type="button" name="update" id="update_filter" class="btn btn-info" value="Update">
                </div>
              <?php } ?>
            </div>
          </div>
        </div>
        <?php } ?>

      </div>
    </div>

    <style>
      .report-row {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .grow-select {
        flex: 1.3;
        min-width: 200px;
      }

      .dc-buttons {
        display: flex;
        gap: 10px;
        flex-shrink: 0;
      }

      @media (max-width: 1400px) {
        .report-row {
          flex-direction: column;
          align-items: flex-start;
        }

        .grow-select {
          width: 100%;
          margin-bottom: 5px;
        }

        .dt-buttons {
          width: 100%;
          justify-content: flex-start;
          margin-top: 5px;
        }

        .dt-buttons input.btn {
          flex: 1;
          text-align: center;
        }
      }
    </style>

    <div class="row">
      <div class="col-sm-12 col-md-12 text-center">
        <input type="button" onclick="get_component_wise_report()" name="search" id="search" class="btn btn-primary" value="Get Report">
      </div>
    </div>

    <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>

    <div class="col-md-12">
      <div class="loading-icon" style="display: none; text-align: center;">
        <img src="<?php echo base_url('assets/img/ajax-loader.gif');?>">
      </div>
      <div class="total_student_summary" style="display: none;">
        <div class="fee_summary"></div>
      </div>
      <div class="table-responsive">
        <div id="report_data"></div>
      </div>
    </div>

  </div>
</div>

<style>
  .dataTables_filter{
    border-bottom : none;
  }

  .buttons-print {
    padding: 2px !important;
  }

  .buttons-colvis {
    padding: 2px !important;
  }

  .buttons-excel {
    border: none !important;
    background: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .dt-button {
    border: none !important;
    background: none !important;
  }

  .btn-info {
    border-radius: 8px !important;
  }

  .dt-button .btn {
    line-height: 20px;
  }

  .dt-buttons {
    text-align: right;
    float: right;
  }

  .dt-buttons {
    width: 100%;
    justify-content: flex-start;
    margin-top: 5px;
  }

  .dt-buttons input.btn {
    flex: 1;
    text-align: center;
  }

  /* Column visibility dropdown styling */
  .dt-button-collection {
    background: white !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
    padding: 5px 0 !important;
    min-width: 200px !important;
  }

  .dt-button-collection .dt-button {
    display: block !important;
    width: 100% !important;
    text-align: left !important;
    padding: 8px 15px !important;
    border: none !important;
    background: none !important;
    color: #333 !important;
    font-size: 14px !important;
  }

  .dt-button-collection .dt-button:hover {
    background-color: #f5f5f5 !important;
    color: #000 !important;
  }

  .dt-button-collection .dt-button.active {
    background-color: #007bff !important;
    color: white !important;
  }

  .dt-button-collection .dt-button input[type="checkbox"] {
    margin-right: 8px !important;
    transform: scale(1.1) !important;
  }

  .buttons-columnVisibility {
    position: relative !important;
  }

  .dt-button-collection {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    z-index: 1050 !important;
    margin-top: 2px !important;
  }

  .custom-colvis-dropdown {
    max-height: 300px !important;
    overflow-y: auto !important;
  }

  .custom-colvis-dropdown .dt-button {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 250px !important;
  }

  .dt-button-collection .dt-button input[type="checkbox"] {
    appearance: checkbox !important;
    -webkit-appearance: checkbox !important;
    -moz-appearance: checkbox !important;
    width: 16px !important;
    height: 16px !important;
    margin-right: 8px !important;
    vertical-align: middle !important;
    cursor: pointer !important;
  }

  .dt-button-collection .dt-button:hover input[type="checkbox"] {
    background-color: transparent !important;
  }

  .buttons-colvis .dt-button-collection {
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
  }

  .dt-button-collection .dt-button.active input[type="checkbox"] {
    accent-color: white !important;
  }

  .dataTables_scrollHeadInner table {
    margin: 0px;
  }

  .search-box {
    display: inline-block;
    margin-right: 10px;
  }

  .table-responsive {
    overflow-x: auto;
  }

  .component-header {
    background-color: #f8f9fa;
    font-weight: bold;
    text-align: center;
  }

  .component-subheader {
    background-color: #e9ecef;
    font-weight: 600;
    text-align: center;
    font-size: 12px;
  }

  .table-bordered th, .table-bordered td {
    border: 1px solid #dee2e6;
    padding: 8px;
    vertical-align: middle;
  }

  .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.05);
  }

  .text-right {
    text-align: right;
  }

  .text-center {
    text-align: center;
  }

  #component_wise_table thead th {
    position: sticky !important;
    top: 0;
    background-color: #f1f5f9;
    color: #111827;
    font-weight: 600;
    border: 1px solid #d1d5db;
    padding: 12px 8px;
    text-align: center;
    vertical-align: middle;
    z-index: 10;
  }

  #component_wise_table tbody td {
    border: 1px solid #d1d5db;
    padding: 8px;
    vertical-align: middle;
  }



  .fee_summary {
    margin-top: 20px;
  }

  @media print {
    .dt-buttons, .dataTables_filter, .dataTables_length, .dataTables_info, .dataTables_paginate {
      display: none !important;
    }

    .table-responsive {
      overflow: visible !important;
    }

    table {
      font-size: 10px !important;
    }
  }
</style>

<script>
$(document).ready(function(){
  $('.select').multiselect({
    includeSelectAllOption: true,
    enableFiltering: true,
    buttonWidth: '100%',
    maxHeight: 200
  });

  get_predefined_filters();

  $('#save_filter').on('click', function() {
    saveFilter();
  });

  $('#update_filter').on('click', function() {
    updateFilter();
  });

  $('.select').on('keydown', function(e) {
    if (e.key === 'Escape') {
      $(this).closest('.dropdown').find('.dropdown-toggle').dropdown('hide');
    }

    if (e.key === 'Enter') {
      $(this).closest('.dropdown').find('.dropdown-toggle').dropdown('hide');
    }

    if (e.key === ' ' && !$(this).is(':focus')) {
      e.preventDefault();
    }
  });

  $(document).on('click', function(event) {
    var $target = $(event.target);
    if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
      $('.bootstrap-select').removeClass('open show');
      $('.dropdown-menu').removeClass('show');
    }
  });
});

function get_component_wise_report(){
  var fee_type = $('#fee_type').val();
  var classId = $('#classId').val();
  var classSectionId = $('#classSectionId').val();
  var admission_status = $('#admission_status').val();
  var admission_type = $('#admission_type').val();
  var category = $('#category').val();
  var rte_nrteId = $('#rte_nrteId').val();
  var donorsId = $('#donorsId').val();
  var combination = $('#combination').val();
  var staff_kid = $('#staff_kid').val();
  
  $(".loading-icon").show();
  $('.table-responsive #report_data').html('');
  $('.fee_summary').html('');
  $('.total_student_summary').hide();
  $('#search').prop('disabled', true).val('Please wait...');

  $.ajax({
    url: '<?php echo site_url('feesv2/reports_v2/student_wise_component_getStudentsForSummary'); ?>',
    type: 'post',
    data: {
      'fee_type': fee_type,
      'clsId': classId,
      'classSectionId': classSectionId,
      'admission_status': admission_status,
      'admission_type': admission_type,
      'category': category,
      'rte_nrteId': rte_nrteId,
      'donorsId': donorsId,
      'combination': combination,
      'staff_kid': staff_kid
    },
    success: function(data) {
      var cohort_student_ids = JSON.parse(data);
      if(cohort_student_ids.length > 0){
        process_student_cohorts(cohort_student_ids, 0, []);
      } else {
        $('.table-responsive').html('<div class="alert alert-warning">No students found with the selected criteria.</div>');
        $(".loading-icon").hide();
        $('#search').prop('disabled', false).val('Get Report');
      }
    },
    error: function() {
      $('.table-responsive').html('<div class="alert alert-danger">Error occurred while fetching data.</div>');
      $(".loading-icon").hide();
      $('#search').prop('disabled', false).val('Get Report');
    }
  });
}

function process_student_cohorts(cohort_student_ids, index, all_data){
  if(index >= cohort_student_ids.length){
    construct_component_wise_table(all_data);
    $(".loading-icon").hide();
    $('.total_student_summary').show();
    $('#search').prop('disabled', false).val('Get Report');
    return;
  }

  var fee_type = $('#fee_type').val();
  var classId = $('#classId').val();
  var classSectionId = $('#classSectionId').val();
  var admission_status = $('#admission_status').val();
  var admission_type = $('#admission_type').val();
  var category = $('#category').val();
  var rte_nrteId = $('#rte_nrteId').val();
  var donorsId = $('#donorsId').val();
  var combination = $('#combination').val();
  var staff_kid = $('#staff_kid').val();

  $.ajax({
    url: '<?php echo site_url('feesv2/reports_v2/get_student_wise_component_getStudentsForSummary'); ?>',
    type: 'post',
    data: {
      'cohortstudentids': cohort_student_ids[index],
      'fee_type': fee_type,
      'clsId': classId,
      'classSectionId': classSectionId,
      'admission_status': admission_status,
      'admission_type': admission_type,
      'category': category,
      'rte_nrteId': rte_nrteId,
      'donorsId': donorsId,
      'combination': combination,
      'staff_kid': staff_kid
    },
    success: function(data) {
      var result = JSON.parse(data);
      all_data.push(result);
      process_student_cohorts(cohort_student_ids, index + 1, all_data);
    },
    error: function() {
      $('.table-responsive').html('<div class="alert alert-danger">Error occurred while processing data.</div>');
      $(".loading-icon").hide();
      $('#search').prop('disabled', false).val('Get Report');
    }
  });
}

function construct_component_wise_table(all_data){
  var merged_data = {
    student_data: {},
    component_headers: {},
    feeArray: {}
  };

  // Merge all cohort data
  for(var i = 0; i < all_data.length; i++){
    var cohort_data = all_data[i];

    // Merge student data
    for(var student_id in cohort_data.student_data){
      merged_data.student_data[student_id] = cohort_data.student_data[student_id];
    }

    // Merge component headers
    for(var header in cohort_data.component_headers){
      merged_data.component_headers[header] = cohort_data.component_headers[header];
    }

    // Merge fee array
    for(var student_id in cohort_data.feeArray){
      if(!merged_data.feeArray[student_id]){
        merged_data.feeArray[student_id] = {};
      }
      for(var component in cohort_data.feeArray[student_id]){
        merged_data.feeArray[student_id][component] = cohort_data.feeArray[student_id][component];
      }
    }
  }

  // Generate summary data
  generateComponentSummary(merged_data);

  var html = '<table class="table table-bordered table-striped" id="component_wise_table">';
  html += '<thead>';
  html += '<tr>';
  html += '<th rowspan="2" class="component-header">S.No</th>';
  html += '<th rowspan="2" class="component-header">Student Name</th>';
  html += '<th rowspan="2" class="component-header">Admission No</th>';
  html += '<th rowspan="2" class="component-header">Class</th>';
  html += '<th rowspan="2" class="component-header">Section</th>';

  // Component headers
  var component_headers_array = Object.keys(merged_data.component_headers);
  for(var i = 0; i < component_headers_array.length; i++){
    html += '<th colspan="5" class="component-header">' + component_headers_array[i] + '</th>';
  }
  html += '</tr>';

  html += '<tr>';
  for(var i = 0; i < component_headers_array.length; i++){
    html += '<th class="component-subheader">Fee Amount</th>';
    html += '<th class="component-subheader">Collected</th>';
    html += '<th class="component-subheader">Concession</th>';
    html += '<th class="component-subheader">Fine</th>';
    html += '<th class="component-subheader">Balance</th>';
  }
  html += '</tr>';
  html += '</thead>';
  html += '<tbody>';

  var sno = 1;
  for(var student_id in merged_data.student_data){
    var student = merged_data.student_data[student_id];
    html += '<tr>';
    html += '<td>' + sno + '</td>';
    html += '<td>' + student.first_name + ' ' + (student.last_name || '') + '</td>';
    html += '<td>' + student.admission_no + '</td>';
    html += '<td>' + student.class + '</td>';
    html += '<td>' + student.section + '</td>';

    // Component data
    for(var i = 0; i < component_headers_array.length; i++){
      var component_key = component_headers_array[i];
      var fee_data = merged_data.feeArray[student_id] && merged_data.feeArray[student_id][component_key] ? merged_data.feeArray[student_id][component_key] : null;

      if(fee_data){
        html += '<td>' + parseFloat(fee_data.component_amount || 0).toFixed(2) + '</td>';
        html += '<td>' + parseFloat(fee_data.component_amount_paid || 0).toFixed(2) + '</td>';
        html += '<td>' + parseFloat(fee_data.concession_amount || 0).toFixed(2) + '</td>';
        html += '<td>' + parseFloat(fee_data.fine_amount || 0).toFixed(2) + '</td>';
        html += '<td>' + parseFloat(fee_data.balance_amount || 0).toFixed(2) + '</td>';
      } else {
        html += '<td>0.00</td><td>0.00</td><td>0.00</td><td>0.00</td><td>0.00</td>';
      }
    }

    html += '</tr>';
    sno++;
  }

  html += '</tbody>';
  html += '</table>';

  $('#report_data').html(html);

  // Initialize DataTable with proper buttons
  if ($.fn.DataTable.isDataTable('#component_wise_table')) {
    $('#component_wise_table').DataTable().destroy();
    $('#component_wise_table').empty();
  }

  var component_headers_array = Object.keys(merged_data.component_headers);
  var colLen = component_headers_array.length * 5 + 5; // 5 columns per component + 5 basic columns

  let table = $('#component_wise_table').DataTable({
    ordering: false,
    paging: false,
    dom: 'Bfrtip',
    info: false,
    scrollX: true,
    scrollY: "500px",
    scrollCollapse: true,
    'columnDefs': [
      { orderable: false, targets: '_all' },
      { className: 'text-center', targets: [0] },
      { className: 'text-right', targets: '_all' }
    ],
    buttons: [
      {
        extend: 'print',
        text: '<button class="btn btn-info"><span class="fa fa-print" aria-hidden="true"></span> Print</button>',
        title: 'Component wise Fee Report',
        footer: true,
        exportOptions: {
          columns: ':visible',
        },
        customize: function (win) {
          $(win.document.body)
            .prepend($('.fee_summary').clone())
            .css('font-family', "'Poppins', sans-serif")
            .css('font-size', '10pt')
            .css('padding', '10px');

          $(win.document.head).append(`
            <style>
              @page {
                size: landscape;
                margin: 12mm;
              }

              body {
                font-family: 'Poppins', sans-serif;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                color: #333;
                background: #fff;
              }

              h2, h3 {
                text-align: center;
                margin-bottom: 15px;
                font-weight: 500;
              }

              table {
                border-collapse: collapse !important;
                width: 100% !important;
                margin-top: 20px;
                font-size: 8pt;
                color: #333;
              }

              th, td {
                border: 1px solid #ccc !important;
                padding: 4px 6px;
                text-align: left;
                vertical-align: middle;
              }

              th {
                background-color: #f4f7fc !important;
                font-weight: 600;
                color: #333;
              }

              .component-header {
                background-color: #e0ecff !important;
                font-weight: bold;
                text-align: center;
              }

              .component-subheader {
                background-color: #f0f0f0 !important;
                font-weight: 600;
                text-align: center;
                font-size: 8pt;
              }

              .fee_summary table {
                margin-bottom: 25px;
              }

              .fee_summary th[colspan] {
                text-align: center;
                background: #e0ecff;
                font-size: 11pt;
                font-weight: 500;
              }

              tfoot th {
                background-color: #f9f9f9;
                font-weight: 600;
              }
            </style>
          `);
        },
      },
      {
        extend: 'excelHtml5',
        text: '<button class="btn btn-info"><span class="fa fa-file-excel-o" aria-hidden="true"></span> Excel</button>',
        title: 'Component wise Fee Report',
        filename: 'Student_Component_Wise_Fee_Report',
        exportOptions: {
          columns: ':visible'
        },
        customize: function (xlsx) {
          var sheet = xlsx.xl.worksheets['sheet1.xml'];
          var sheetData = sheet.getElementsByTagName('sheetData')[0];

          // Get summary data
          var summaryTable = $('.fee_summary table');
          var summaryRowCount = summaryTable.find('tr').length;

          // Create summary rows with proper formatting
          var summaryRows = '';
          var rowIndex = 1;

          summaryTable.find('tr').each(function() {
            summaryRows += '<row r="' + rowIndex + '">';
            var colIndex = 0;

            $(this).find('th, td').each(function() {
              var cellValue = $(this).text().replace(/[^\w\s\.\-\(\)₹,]/gi, '');
              var colLetter = getColumnLetter(colIndex);

              summaryRows += '<c r="' + colLetter + rowIndex + '" t="inlineStr">';
              summaryRows += '<is><t>' + cellValue + '</t></is>';
              summaryRows += '</c>';
              colIndex++;
            });

            summaryRows += '</row>';
            rowIndex++;
          });

          // Add empty separator row
          summaryRows += '<row r="' + (rowIndex + 1) + '"></row>';
          var dataStartRow = rowIndex + 2;

          // Create proper two-row header structure
          var component_headers_array = Object.keys(window.currentMergedData.component_headers);
          var headerRows = '';

          // First header row (with component names and colspan)
          headerRows += '<row r="' + dataStartRow + '">';
          // Basic columns with rowspan
          headerRows += '<c r="A' + dataStartRow + '" t="inlineStr"><is><t>S.No</t></is></c>';
          headerRows += '<c r="B' + dataStartRow + '" t="inlineStr"><is><t>Student Name</t></is></c>';
          headerRows += '<c r="C' + dataStartRow + '" t="inlineStr"><is><t>Admission No</t></is></c>';
          headerRows += '<c r="D' + dataStartRow + '" t="inlineStr"><is><t>Class</t></is></c>';
          headerRows += '<c r="E' + dataStartRow + '" t="inlineStr"><is><t>Section</t></is></c>';

          // Component headers
          var colIndex = 5;
          for (var i = 0; i < component_headers_array.length; i++) {
            var colLetter = getColumnLetter(colIndex);
            headerRows += '<c r="' + colLetter + dataStartRow + '" t="inlineStr"><is><t>' + component_headers_array[i] + '</t></is></c>';
            colIndex += 5; // Skip 4 columns for the merge
          }
          headerRows += '</row>';

          // Second header row (sub-headers)
          var secondRowIndex = dataStartRow + 1;
          headerRows += '<row r="' + secondRowIndex + '">';
          // Skip basic columns (they are merged from row above)
          colIndex = 5;
          for (var i = 0; i < component_headers_array.length; i++) {
            var subHeaders = ['Fee Amount', 'Collected', 'Concession', 'Fine', 'Balance'];
            for (var j = 0; j < subHeaders.length; j++) {
              var colLetter = getColumnLetter(colIndex + j);
              headerRows += '<c r="' + colLetter + secondRowIndex + '" t="inlineStr"><is><t>' + subHeaders[j] + '</t></is></c>';
            }
            colIndex += 5;
          }
          headerRows += '</row>';

          // Get existing data (skip the original headers) and update row numbers
          var existingRows = sheetData.innerHTML;
          // Remove original header rows (first 2 rows)
          existingRows = existingRows.replace(/<row r="1"[^>]*>.*?<\/row>/g, '');
          existingRows = existingRows.replace(/<row r="2"[^>]*>.*?<\/row>/g, '');

          // Update remaining row numbers
          var bodyStartRow = secondRowIndex + 1;
          var updatedRows = existingRows.replace(/r="(\d+)"/g, function(match, rowNum) {
            var originalRowNum = parseInt(rowNum);
            if (originalRowNum > 2) { // Only update data rows, not headers
              var newRowNum = originalRowNum - 2 + bodyStartRow;
              return 'r="' + newRowNum + '"';
            }
            return match;
          });

          // Update cell references for data rows
          updatedRows = updatedRows.replace(/r="([A-Z]+)(\d+)"/g, function(match, col, rowNum) {
            var originalRowNum = parseInt(rowNum);
            if (originalRowNum > 2) {
              var newRowNum = originalRowNum - 2 + bodyStartRow;
              return 'r="' + col + newRowNum + '"';
            }
            return match;
          });

          // Combine all rows
          sheetData.innerHTML = summaryRows + headerRows + updatedRows;

          // Handle merged cells
          var mergeCells = sheet.getElementsByTagName('mergeCells')[0];
          if (!mergeCells) {
            mergeCells = sheet.createElement('mergeCells');
            sheet.getElementsByTagName('worksheet')[0].appendChild(mergeCells);
          }

          var mergeCount = 0;

          // Merge basic columns (rowspan for first 5 columns)
          for (var i = 0; i < 5; i++) {
            var colLetter = getColumnLetter(i);
            var mergeRange = colLetter + dataStartRow + ':' + colLetter + secondRowIndex;
            var mergeCell = sheet.createElement('mergeCell');
            mergeCell.setAttribute('ref', mergeRange);
            mergeCells.appendChild(mergeCell);
            mergeCount++;
          }

          // Merge component headers (colspan)
          var startCol = 5;
          for (var i = 0; i < component_headers_array.length; i++) {
            var startColLetter = getColumnLetter(startCol + (i * 5));
            var endColLetter = getColumnLetter(startCol + (i * 5) + 4);
            var mergeRange = startColLetter + dataStartRow + ':' + endColLetter + dataStartRow;

            var mergeCell = sheet.createElement('mergeCell');
            mergeCell.setAttribute('ref', mergeRange);
            mergeCells.appendChild(mergeCell);
            mergeCount++;
          }

          if (mergeCount > 0) {
            mergeCells.setAttribute('count', mergeCount.toString());
          }
        }
      },
    ],
  });

  // Add column visibility button
  initializeColVisButton(table, 'component_wise_table_wrapper', colLen);

  // Store merged_data globally for Excel export
  window.currentMergedData = merged_data;
}

function initializeColVisButton(table, wrapperId, colLen) {
  // Add a column visibility button to the given table
  new $.fn.dataTable.Buttons(table, {
    buttons: [
      {
        extend: 'colvis',
        text: '<button class="btn btn-info"><span class="fa fa-columns" aria-hidden="true"></span> Columns</button>',
        className: 'btn btn-info buttons-columnVisibility',
        columns: function (idx, data, node) {
          return idx <= colLen; // Restrict to columns <= colLen
        },
        columnText: function (dt, idx, title) {
          // Create checkbox-style column visibility options
          var isVisible = dt.column(idx).visible();
          var checkbox = '<input type="checkbox" ' + (isVisible ? 'checked' : '') + '> ';
          return checkbox + title;
        },
        collectionLayout: 'fixed columns',
        collectionTitle: 'Column Visibility',
        background: true,
        fade: 400,
        popoverTitle: 'Column Visibility'
      },
    ],
  }).container().appendTo($(`#${wrapperId} .dataTables_filter`));

  // Add custom styling after button creation
  setTimeout(function() {
    $('.dt-button-collection').addClass('custom-colvis-dropdown');
  }, 100);
}



function generateComponentSummary(merged_data) {
  var component_headers_array = Object.keys(merged_data.component_headers);
  var summaryData = {};
  var totalStudents = Object.keys(merged_data.student_data).length;

  // Initialize summary data for each component
  for(var i = 0; i < component_headers_array.length; i++){
    var componentKey = component_headers_array[i];
    summaryData[componentKey] = {
      total_fee_amount: 0,
      total_fee_collected: 0,
      total_concession: 0,
      total_fine: 0,
      total_balance: 0,
      student_count: 0
    };
  }

  // Calculate totals for each component
  for(var student_id in merged_data.feeArray){
    for(var componentKey in merged_data.feeArray[student_id]){
      var feeData = merged_data.feeArray[student_id][componentKey];
      if(summaryData[componentKey]){
        summaryData[componentKey].total_fee_amount += parseFloat(feeData.component_amount || 0);
        summaryData[componentKey].total_fee_collected += parseFloat(feeData.component_amount_paid || 0);
        summaryData[componentKey].total_concession += parseFloat(feeData.concession_amount || 0);
        summaryData[componentKey].total_fine += parseFloat(feeData.fine_amount || 0);
        summaryData[componentKey].total_balance += parseFloat(feeData.balance_amount || 0);
        summaryData[componentKey].student_count++;
      }
    }
  }

  // Generate summary HTML
  var fee_summary = '<table class="table table-bordered" style="width: 100%; margin-bottom: 20px;">';
  fee_summary += '<thead>';
  fee_summary += '<tr>';
  fee_summary += '<th>Component</th>';
  fee_summary += '<th># Students</th>';
  fee_summary += '<th>Fee Amount</th>';
  fee_summary += '<th>Collected Amount</th>';
  fee_summary += '<th>Concession</th>';
  fee_summary += '<th>Fine</th>';
  fee_summary += '<th>Balance</th>';
  fee_summary += '</tr>';
  fee_summary += '</thead>';
  fee_summary += '<tbody>';

  var gtotal_fee_amount = 0;
  var gtotal_fee_collected = 0;
  var gtotal_concession = 0;
  var gtotal_fine = 0;
  var gtotal_balance = 0;

  for(var componentKey in summaryData) {
    var data = summaryData[componentKey];
    gtotal_fee_amount += data.total_fee_amount;
    gtotal_fee_collected += data.total_fee_collected;
    gtotal_concession += data.total_concession;
    gtotal_fine += data.total_fine;
    gtotal_balance += data.total_balance;

    fee_summary += '<tr>';
    fee_summary += '<th style="font-size:14px; color:#7ea3d2"><b>' + componentKey + '</b></th>';
    fee_summary += '<td>' + totalStudents + '</td>';
    fee_summary += '<td>' + in_currency(data.total_fee_amount) + '</td>';
    fee_summary += '<td>' + in_currency(data.total_fee_collected) + '</td>';
    fee_summary += '<td>' + in_currency(data.total_concession) + '</td>';
    fee_summary += '<td>' + in_currency(data.total_fine) + '</td>';
    fee_summary += '<td>' + in_currency(data.total_balance) + '</td>';
    fee_summary += '</tr>';
  }

  // Grand total row
  fee_summary += '<tr style="background-color: #f8f9fa; font-weight: bold;">';
  fee_summary += '<th>Grand Total</th>';
  fee_summary += '<th>' + totalStudents + '</th>';
  fee_summary += '<th>' + in_currency(gtotal_fee_amount) + '</th>';
  fee_summary += '<th>' + in_currency(gtotal_fee_collected) + '</th>';
  fee_summary += '<th>' + in_currency(gtotal_concession) + '</th>';
  fee_summary += '<th>' + in_currency(gtotal_fine) + '</th>';
  fee_summary += '<th>' + in_currency(gtotal_balance) + '</th>';
  fee_summary += '</tr>';

  fee_summary += '</tbody>';
  fee_summary += '</table>';

  $('.fee_summary').html(fee_summary);
}

function in_currency(amount) {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
}

function getColumnLetter(columnIndex) {
  var letter = '';
  while (columnIndex >= 0) {
    letter = String.fromCharCode(65 + (columnIndex % 26)) + letter;
    columnIndex = Math.floor(columnIndex / 26) - 1;
  }
  return letter;
}

function get_predefined_filters() {
  $.ajax({
    url: '<?php echo site_url('feesv2/reports/get_predefined_filters1'); ?>',
    type: 'POST',
    data: { report_type: 'component_wise_fees' },
    success: function(data) {
      if (data) {
        var filters = JSON.parse(data);
        var options = '<option value="">Select Report</option>';
        filters.forEach(function(filter) {
          options += '<option value="' + filter.id + '">' + filter.title + '</option>';
        });
        $('#filter_types').html(options);
      }
    }
  });
}

function selectFilters() {
  var filterId = $('#filter_types').val();
  if (!filterId) return;

  $.ajax({
    url: '<?php echo site_url('feesv2/reports/get_filter_by_id1'); ?>',
    type: 'POST',
    data: { id: filterId },
    success: function(data) {
      if (data) {
        var filters = JSON.parse(data);
        applyFilters(filters);
      }
    }
  });
}

function applyFilters(filters) {
  // Apply filter values to form elements
  if (filters.fee_type) $('#fee_type').val(filters.fee_type.split(',')).trigger('change');
  if (filters.classId) $('#classId').val(filters.classId.split(',')).trigger('change');
  if (filters.classSectionId) $('#classSectionId').val(filters.classSectionId.split(',')).trigger('change');
  if (filters.admission_status) $('#admission_status').val(filters.admission_status);
  if (filters.admission_type) $('#admission_type').val(filters.admission_type);
  if (filters.category) $('#category').val(filters.category);
  if (filters.rte_nrteId) $('#rte_nrteId').val(filters.rte_nrteId);
  if (filters.donorsId) $('#donorsId').val(filters.donorsId);
  if (filters.combination) $('#combination').val(filters.combination);
  if (filters.staff_kid) $('#staff_kid').val(filters.staff_kid);

  // Refresh multiselect dropdowns
  $('.select').multiselect('refresh');

  // Auto-generate report
  setTimeout(function() {
    get_component_wise_report();
  }, 500);
}

function saveFilter() {
  bootbox.prompt({
    inputType: 'text',
    placeholder: 'Enter the Title name',
    title: "Save filters",
    className: 'half-width-box',
    buttons: {
      confirm: { label: 'Yes', className: 'btn-success' },
      cancel: { label: 'No', className: 'btn-danger' }
    },
    callback: function (remarks) {
      if (remarks === null) return;

      $('.bootbox .error-message').remove();
      remarks = remarks.trim();

      if (!remarks) {
        new PNotify({
          title: 'Missing Title',
          text: 'Please enter a name to save the filter.',
          type: 'error',
          delay: 3000
        });
        return false;
      }

      if (remarks.length < 5 || remarks.length > 50) {
        setTimeout(() => {
          $('.bootbox-input').after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">Title must be between 5 and 50 characters.</div>`);
        }, 10);
        return false;
      }

      let duplicate = false;
      $('#filter_types option').each(function () {
        if ($(this).text().trim().toLowerCase() === remarks.toLowerCase()) {
          duplicate = true;
          return false;
        }
      });

      if (duplicate) {
        setTimeout(() => {
          $('.bootbox-input').after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">A filter with this name already exists.</div>`);
        }, 10);
        return false;
      }

      let filterData = {
        title: remarks,
        report_type: 'component_wise_fees',
        fee_type: $('#fee_type').val() ? $('#fee_type').val().join(',') : '',
        classId: $('#classId').val() ? $('#classId').val().join(',') : '',
        classSectionId: $('#classSectionId').val() ? $('#classSectionId').val().join(',') : '',
        admission_status: $('#admission_status').val(),
        admission_type: $('#admission_type').val(),
        category: $('#category').val(),
        rte_nrteId: $('#rte_nrteId').val(),
        donorsId: $('#donorsId').val(),
        combination: $('#combination').val(),
        staff_kid: $('#staff_kid').val()
      };

      $.ajax({
        url: '<?php echo site_url('feesv2/reports/save_filters1'); ?>',
        type: 'POST',
        data: filterData,
        success: function (data) {
          if (data) {
            new PNotify({
              title: 'Success',
              text: 'Filter saved successfully.',
              type: 'success',
              delay: 3000
            });
            get_predefined_filters();
          }
        }
      });
    }
  });
}

function updateFilter() {
  var filterId = $('#filter_types').val();
  if (!filterId) {
    alert('Please select a filter to update.');
    return;
  }

  let filterData = {
    id: filterId,
    fee_type: $('#fee_type').val() ? $('#fee_type').val().join(',') : '',
    classId: $('#classId').val() ? $('#classId').val().join(',') : '',
    classSectionId: $('#classSectionId').val() ? $('#classSectionId').val().join(',') : '',
    admission_status: $('#admission_status').val(),
    admission_type: $('#admission_type').val(),
    category: $('#category').val(),
    rte_nrteId: $('#rte_nrteId').val(),
    donorsId: $('#donorsId').val(),
    combination: $('#combination').val(),
    staff_kid: $('#staff_kid').val()
  };

  $.ajax({
    url: '<?php echo site_url('feesv2/reports/update_filter1'); ?>',
    type: 'POST',
    data: filterData,
    success: function (data) {
      if (data) {
        new PNotify({
          title: 'Success',
          text: 'Filter updated successfully.',
          type: 'success',
          delay: 3000
        });
      }
    }
  });
}
</script>
