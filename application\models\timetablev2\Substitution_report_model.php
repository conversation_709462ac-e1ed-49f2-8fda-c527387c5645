<?php

use function PHPSTORM_META\type;

class Substitution_report_model extends CI_Model {
    
    public function __construct() {
        parent::__construct();
    }

    public function get_substitution_day($date) {
        return $this->db_readonly->query("SELECT * from ttv2_substitution_day where sub_date='$date'")->row();
    }

    public function get_staff_names() {
        $result = $this->db_readonly->select("sm.id as staff_id, concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name")
            ->from('staff_master sm')
            ->where('sm.status',2)
            ->order_by('sm.first_name,sm.last_name')
            ->get()->result();
        $staff = array();
        foreach ($result as $res) {
            $staff[$res->staff_id] = $res->staff_name;
        }
        return $staff;
    }

    public function get_source_type(){
        $sql = "select distinct(source_type) from ttv2_substitution_input";
        $source_type = $this->db_readonly->query($sql)->result();
        return $source_type;
    }

    public function get_day_substitutions($substitution_id, $source_type) {
        $source_type = implode(', ', array_map(function($type) {
            return "'$type'";
        }, $source_type));
        if(strpos($source_type, "all") !== false){
            $sql = "SELECT sout.class_section_name, new_sub_staff_id as sub_staff_id, sin.staff_id, sub.long_name as subject_name, sectp.copy_short_name as p_short_name, sectp.copy_long_name as p_long_name, temp.name as template_name, DATE_FORMAT(sectp.copy_start_time,'%h:%i%p') as period_start_time, DATE_FORMAT(sectp.copy_end_time,'%h:%i%p') as period_end_time
                    from ttv2_substitution_input sin 
                    join ttv2_substitution_output sout on sout.ttv2_sub_input_id=sin.id 
                    join ttv2_substitution_output_staffs sos on sout.id = sos.ttv2_sub_out_id
                    join class_section cs on sout.class_section_id=cs.id
                    join ttv2_staff_workload sw on sw.id=sout.old_ttv2_staff_workload_id 
                    join ttv2_subjects sub on sw.subject_id=sub.id 
                    join ttv2_section_template_periods sectp on sectp.id=sout.ttv2_section_period_id 
                    join ttv2_section_templates sect on sect.id=sectp.ttv2_section_template_id 
                    join ttv2_template temp on temp.id=sect.ttv2_template_id 
                    where sin.ttv2_sub_day_id=$substitution_id
                    and temp.status = 'active'
                    order by sectp.copy_template_name,cs.display_order, p_long_name";
        }
        else{
            $sql = "SELECT sout.class_section_name, new_sub_staff_id as sub_staff_id, sin.staff_id, sub.long_name as subject_name, sectp.copy_short_name as p_short_name, sectp.copy_long_name as p_long_name, temp.name as template_name, DATE_FORMAT(sectp.copy_start_time,'%h:%i%p') as period_start_time, DATE_FORMAT(sectp.copy_end_time,'%h:%i%p') as period_end_time
                from ttv2_substitution_input sin 
                join ttv2_substitution_output sout on sout.ttv2_sub_input_id=sin.id 
                join ttv2_substitution_output_staffs sos on sout.id = sos.ttv2_sub_out_id
                join class_section cs on sout.class_section_id=cs.id
                join ttv2_staff_workload sw on sw.id=sout.old_ttv2_staff_workload_id 
                join ttv2_subjects sub on sw.subject_id=sub.id 
                join ttv2_section_template_periods sectp on sectp.id=sout.ttv2_section_period_id 
                join ttv2_section_templates sect on sect.id=sectp.ttv2_section_template_id 
                join ttv2_template temp on temp.id=sect.ttv2_template_id 
                where sin.ttv2_sub_day_id=$substitution_id
                and temp.status = 'active'
                and sin.source_type in ($source_type)
                order by sectp.copy_template_name,cs.display_order, p_long_name";
        }
        $substitutions = $this->db_readonly->query($sql)->result();
        $staff = $this->get_staff_names();
        foreach ($substitutions as &$sub) {
            $sub->sub_staff_name = '<span class="text-danger">Not Substituted</span>';
            $sub->staff_name = '';
            if(array_key_exists($sub->staff_id, $staff)) {
                $sub->staff_name = $staff[$sub->staff_id];
            }
            if(array_key_exists($sub->sub_staff_id, $staff)) {
                $sub->sub_staff_name = $staff[$sub->sub_staff_id];
            }
        }

        return $substitutions;
    }

    public function get_substitution_load($from_date, $to_date, $source_type) {
        $source_type = implode(', ', array_map(function($type) {
            return "'$type'";
        }, $source_type));
        if(strpos($source_type, "all") !== false){
            $sql = "SELECT count(distinct old_ttv2_staff_period_id) as s_load,  new_sub_staff_id 
                    from ttv2_substitution_day sday 
                    join ttv2_substitution_input sin on sin.ttv2_sub_day_id=sday.id 
                    join ttv2_substitution_output sout on sout.ttv2_sub_input_id=sin.id 
                    left join ttv2_substitution_output_staffs sos on sout.id = sos.ttv2_sub_out_id
                    where sub_date>='$from_date' and sub_date<='$to_date' 
                    and new_sub_staff_id is not null
                    group by new_sub_staff_id";
        }
        else{
            $sql = "SELECT count(distinct old_ttv2_staff_period_id) as s_load,  new_sub_staff_id 
                    from ttv2_substitution_day sday 
                    join ttv2_substitution_input sin on sin.ttv2_sub_day_id=sday.id 
                    join ttv2_substitution_output sout on sout.ttv2_sub_input_id=sin.id 
                    left join ttv2_substitution_output_staffs sos on sout.id = sos.ttv2_sub_out_id
                    where sub_date>='$from_date' and sub_date<='$to_date' 
                    and new_sub_staff_id is not null
                    and sin.source_type in ($source_type)
                    group by new_sub_staff_id";
        }
        $sub_load = $this->db_readonly->query($sql)->result();

        $loads = [];
        foreach ($sub_load as $load) {
            $loads[$load->new_sub_staff_id] = $load->s_load;
        }

        $staff = $this->db_readonly->query("SELECT sm.id, concat(ifnull(sm.first_name,''),' ', ifnull(sm.last_name,'')) as staff_name from staff_master sm where sm.status=2")->result();

        foreach ($staff as &$stf) {
            $stf->load = '-';
            if(array_key_exists($stf->id, $loads)) {
                $stf->load = $loads[$stf->id];
            }
        }
        return $staff;
    }

    public function get_substitution_load_details($from_date, $to_date, $staff_id){
        $sql = "SELECT si.staff_id AS old_staff_id, CONCAT(IFNULL(sm.first_name,''), ' ', IFNULL(sm.last_name,'')) AS staff_name, 
                GROUP_CONCAT(so.class_section_name SEPARATOR ', ') AS class_section_name, sd.sub_date, stp.copy_long_name, 
                so.copy_pseq, old_ttv2_staff_period_id
                FROM ttv2_substitution_day sd
                LEFT JOIN ttv2_substitution_input si ON sd.id = si.ttv2_sub_day_id
                LEFT JOIN ttv2_substitution_output so ON si.id = so.ttv2_sub_input_id
                LEFT JOIN ttv2_staff_template_periods stp ON stp.id = so.ttv2_section_period_id
                LEFT JOIN ttv2_substitution_output_staffs sos ON so.id = sos.ttv2_sub_out_id
                JOIN staff_master sm ON si.staff_id = sm.id    
                where new_sub_staff_id = $staff_id and sub_date>='$from_date' and sub_date<='$to_date'
                GROUP BY old_ttv2_staff_period_id
                ORDER BY sd.sub_date, si.staff_id";
        $sub_load_details = $this->db_readonly->query($sql)->result();
        return $sub_load_details;
    }
}