<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('Master_dashboard') ?>">Master Dashboard</a></li>
  <li><a href="#">Config Management</a></li>
</ul>

<div class="col-md-12">
    <div class="card cd_border" style="padding-bottom:2px;">
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row align-items-center" style="margin: 0px;">
				<div class="col-md-4 d-flex align-items-center">
					<h3 class="card-title panel_title_new_style_staff mb-2 mr-3">
						<a class="back_anchor" href="<?php echo site_url('Master_dashboard');?>">
							<span class="fa fa-arrow-left"></span>
						</a> 
						Config Management
					</h3>
				</div>
				<?php if($name != 'Dev User') {?>
					<div class="col-md-8 text-right d-flex align-items-center justify-content-end">
						<div style="margin-right: 12px;font-size: medium;">
							Login: <b><?php echo ucfirst($name); ?></b>
						</div>
						<button class="btn btn-primary" onclick="logout()">Logout</button>
					</div>
				<?php } ?>
			</div>
		</div>

        <div class="card-body pt-0">
			<div class="table-responsive">
				<table class='table table-bordered' id='list_tab'>
					<thead>
						<tr id="filter-row">
							<th>
								<i class='fa fa-times' onclick='clearFilters()' style='cursor:pointer;color:#e04b4a;' title='Clear Filters'></i>
							</th>
							<th></th>
							<th><input type='text' class='form-control' id='property-filter' placeholder='Search Property' onkeyup='filterConfigList()'></th>
							<th><input type='text' class='form-control' id='value-filter' placeholder='Search Value' onkeyup='filterConfigList()'></th>
							<th><input type='text' class='form-control' id='desc-filter' placeholder='Search Description' onkeyup='filterConfigList()'></th>
							<th><select class='form-control' id='category-filter' onchange='filterConfigList()'></select></th>
							<th><input type='text' class='form-control' id='default-filter' placeholder='Search Default' onkeyup='filterConfigList()'></th>
						</tr>
						<tr>
							<th>#</th>
							<th>Actions</th>
							<th>Property</th>
							<th>Value</th>
							<th>Description</th>
							<th>Category</th>
							<th>Default</th>
						</tr>
					</thead>
					<tbody></tbody>
				</table>
			</div>
        </div>
    </div>
</div>

<style type="text/css">
	a:not([href]) {
		color: #fff;
	}
	.bootbox > .modal-dialog{
		width: 60%;
		margin: auto;
		top: 10%;
	}
	.card-body {
		overflow-x: hidden;
	}

	.table {
		table-layout: fixed;
		width: 100%;
	}

	.table th, .table td {
		word-wrap: break-word;
		white-space: normal;
	}

	.table th:first-child,
	.table td:first-child {
		width: 40px;
		text-align: center;
	}

	.table th:nth-child(2),
	.table td:nth-child(2) {
		width: 95px;
		white-space: nowrap;
	}

	/* .table th:nth-child(5),
	.table td:nth-child(5) {
		width: 225px;
		white-space: nowrap;
	} */

	.dropdown-menu {
		margin-left: 8px;
		top: 50%;
		transform: translateY(-50%);
	}

	.action-dropdown {
		position: relative;
	}

	.action-dropdown .dropdown-toggle {
		padding: 6px 10px;
		border-radius: 6px;
		border: 1px solid #ccc;
		background-color: #fff;
	}

	.action-dropdown .dropdown-menu {
		min-width: 160px;
		padding: 0.5rem 0;
		margin-left: 10px; /* Push away from the button */
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	}

	.action-dropdown .dropdown-item i {
		margin-right: 8px;
	}

	.scrollable-cell {
		max-height: 80px;
		overflow-y: auto;
		padding: 4px;
		white-space: pre-wrap;
		word-break: break-word;
		font-size: 13px;
		line-height: 1.4;
	}
</style>

<script>
	var category = 'all';
	$(document).ready(function () {
		changeType();
		loadConfigData();
	});
	function loadConfigData() {
		$.ajax({
			url: '<?php echo site_url('config/load_data'); ?>',
			type: 'post',
			dataType: 'json',
			success: function(data) {
				renderConfigTable(data);
			},
			error: function(err) {
				console.error('Error fetching config data:', err);
				$('#list_tab tbody').html('<tr><td colspan="8">Failed to load data.</td></tr>');
			}
		});
	}

	function escapeHTML(str) {
		if (typeof str !== 'string') return str;
		return str
			.replace(/&/g, "&amp;")
			.replace(/</g, "&lt;")
			.replace(/>/g, "&gt;")
			.replace(/"/g, "&quot;")
			.replace(/'/g, "&#39;");
	}

	function safeJSON(str) {
		try {
			return JSON.stringify(str || "").replace(/</g, '\\u003c').replace(/>/g, '\\u003e');
		} catch (e) {
			return '""';
		}
	}

	function renderConfigTable(templateConfig) {
		let uniqueTypes = new Set();
		let uniqueCategories = new Set();

		const rows = templateConfig.map((row, index) => {
			if (row.type) uniqueTypes.add(row.type);
			if (row.group) {
				const groups = row.group.split(',').map(g => g.trim().toLowerCase()); // lowercase for filtering
				groups.forEach(g => uniqueCategories.add(g));
			}

			const isInDB = row.isConfigInDB;

			let valueStr = '';
			if (isInDB) {
				switch (row.type) {
					case 'string':
					case 'select':
						valueStr = row.value;
						break;
					case 'multiple':
						valueStr = `<strong>Enabled: </strong><small>${row.enabledOptions.join(', ')}</small><br>` +
							`<strong>Disabled: </strong><small>${row.disabledOptions.join(', ')}</small>`;
						break;
					case 'json':
					case 'array':
						valueStr = row.value.length > 100 ? row.value.substring(0, 99) + '...' : row.value;
						break;
					case 'boolean':
						valueStr = parseInt(row.value) ? "Enabled" : "Disabled";
						break;
					case 'image':
						valueStr = `<img style="object-fit: cover;" src="${row.filePath}" alt="No Image" height="60" width="60">`;
						break;
					default:
						valueStr = row.value || '';
				}
				if (['string', 'select', 'json', 'array', 'multiple'].includes(row.type)) {
					valueStr = `<div class="scrollable-cell">${valueStr}</div>`;
				}
			}

			let defaultStr = '';
			switch (row.type) {
				case 'string':
				case 'select':
					defaultStr = row.default;
					break;
				case 'multiple':
					defaultStr = 'NA';
					break;
				case 'json':
				case 'array':
					defaultStr = row.default.length > 50 ? row.default.substring(0, 49) + '...' : row.default;
					break;
				case 'image':
					defaultStr = `<img style="object-fit: cover;" src="<?= site_url('assets/img/no_image.png') ?>" height="60" width="60">`;
					break;
				case 'boolean':
					defaultStr = row.default;
					break;
				default:
					defaultStr = '';
			}

			let actions = '';
			if (row.isEditable !== false) {
				actions += `<div class="dropend action-dropdown">
					<button class="btn dropdown-toggle action-dropdown-btn" type="button" id="dropdownMenuButton-${row.name}" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<i class="fa fa-ellipsis-v"></i>
					</button>
					<div class="dropdown-menu" aria-labelledby="dropdownMenuButton-${row.name}">`;

				switch (row.type) {
					case 'multiple':
						actions += `<form class="dropdown-item" action="<?= site_url("config/updateMultipleType") ?>" method="post" style="padding-left: 7px">
							<input type="hidden" name="enabledOptions" value='${JSON.stringify(row.enabledOptions)}'>
							<input type="hidden" name="options" value='${JSON.stringify(row.options)}'>
							<input type="hidden" name="configName" value='${row.name}'>
							<button type="submit" class="btn btn-link"><i class="fa fa-plus"></i> Add/Remove Modules</button>
							</form>`;
						if (row.isConfigInDB == 1) {
							actions += `<a class="dropdown-item text-danger" href="javascript:void(0);" onclick="deleteConfig('${row.name}')"><i class="fa fa-trash-o"></i> Delete</a>`;
						}
						break;

					case 'string':
					case 'json':
					case 'array':
					case 'select':
						actions += `<a class='dropdown-item' href='javascript:void(0);' 
										onclick='handleUpdateConfig(this)'
										data-name="${row.name}"
										data-type="${row.type ?? ''}"
										data-description="${encodeURIComponent(row.description ?? '')}" 
										data-default='${JSON.stringify(row.default ?? "")}'
										data-value='${JSON.stringify(row.value ?? "")}'>
							<i class='fa fa-pencil'></i> Edit</a>`;
						if (row.isConfigInDB == 1) {
							actions += `<a class="dropdown-item text-danger" href="javascript:void(0);" onclick="deleteConfig('${row.name}')"><i class="fa fa-trash-o"></i> Delete</a>`;
						}
						break;

					case 'image':
						const label = row.value ? 'Update Image' : 'Add Image';
						actions += `<a class="dropdown-item" href="javascript:void(0);" onclick="updateimageConfig('${row.name}', '${row.value || ''}', '${row.default}', '${row.type}', '${row.description || ''}')"><i class="fa fa-image"></i> ${label}</a>`;
						if (row.isConfigInDB == 1) {
							actions += `<a class="dropdown-item text-danger" href="javascript:void(0);" onclick="deleteConfig('${row.name}')"><i class="fa fa-trash-o"></i> Delete</a>`;
						}
						break;

					case 'boolean':
						if (row.value) {
							const checked = row.value == 1 ? 'checked' : '';
							actions += `<div class="dropdown-item"><label class="switch"><input class="changebool attr_${row.name}" data-name="${row.name}" data-value="${row.value}" type="checkbox" ${checked} /><span class="slider round"></span></label></div>`;
							if (row.isConfigInDB == 1) {
								actions += `<a class="dropdown-item text-danger" href="javascript:void(0);" onclick="deleteConfig('${row.name}')"><i class="fa fa-trash-o"></i> Delete</a>`;
							}
							break;
						} else {
							actions += `<a class="dropdown-item text-danger" href="javascript:void(0);" onclick="enableBool('${row.name}', '${row.default}')"><i class="fa fa-plus"></i> Add</a>`;
						}
				}
				actions += '</div></div>';
			} else {
				actions += `<p><font style="color: rgb(204, 204, 204);">Not Editable</font></p>`;
			}

			return {
				rowData: [
					index + 1,
					actions,
					row.name,
					valueStr,
					`<div class="scrollable-cell">${row.description ? row.description : "NA"}</div>`,
					`<span class="group-cell" data-groups="${row.group.toLowerCase()}">${row.group}</span>`,
					defaultStr,
					isInDB
				]
			};
		});

		const table = $('#list_tab').DataTable({
			paging: true,
			lengthChange: true,
			ordering: false,
			searching: true,
			destroy: true,
			columnDefs: [
				{ targets: 7, visible: false }
			],
			rowCallback: function (row, data) {
				if (data[7] == 0) {
					$(row).css('color', '#ccc');
				}
			}
		});

		table.clear();
		rows.forEach(r => table.row.add(r.rowData));
		table.draw();

		// Populate filter dropdown
		const categoryOptions = ['<option value="">All</option>'];
		[...uniqueCategories].sort().forEach(cat => {
			categoryOptions.push(`<option value="${cat}">${capitalizeFirstLetter(cat)}</option>`);
		});
		$('#category-filter').html(categoryOptions.join(''));
	}

	function filterConfigList() {
		const table = $('#list_tab').DataTable();

		const property = $('#property-filter').val();
		const value = $('#value-filter').val();
		const desc = $('#desc-filter').val();
		const category = $('#category-filter').val().toLowerCase().trim();
		const defaultVal = $('#default-filter').val();

		// Apply direct searches
		table.column(2).search(property, true, false); // name
		table.column(3).search(value, true, false);    // value
		table.column(4).search(desc, true, false);     // description
		table.column(6).search(defaultVal, true, false); // default

		// Apply category (group) filtering manually
		$.fn.dataTable.ext.search = $.fn.dataTable.ext.search.filter(fn => !fn.isCategoryFilter); // clear old filter

		if (category) {
			const categoryFilter = function (settings, data, dataIndex) {
				const row = table.row(dataIndex).node();
				const $groupCell = $(row).find('.group-cell');

				if ($groupCell.length) {
					const dataGroups = $groupCell.data('groups');
					if (!dataGroups) return false;

					const groups = dataGroups.split(',').map(g => g.trim().toLowerCase());
					return groups.includes(category);
				}
				return false;
			};
			categoryFilter.isCategoryFilter = true; // flag to help remove this filter later
			$.fn.dataTable.ext.search.push(categoryFilter);
		}

		table.draw();
	}

	function logout(){
		bootbox.confirm({
			title: "Logout",
			message: "Are you sure, you want to logout from Config Management Console?",
			buttons: {
				confirm: {
					label: 'Yes',
					className: 'btn-success'
				},
				cancel: {
					label: 'No',
					className: 'btn-danger'
				}
			},
			callback: function (result) {
				if(result){
					window.location.href = '<?php echo site_url("config/logout") ?>';
				}
			}
		})
	}

	function clearFilters() {
		$('#filter-row input, #filter-row select').val('');
		filterConfigList();
	}

	function capitalizeFirstLetter(string) {
		return string.charAt(0).toUpperCase() + string.slice(1);
	}


	function changeType(){
		var type = $("#selectedType").val();
		switch(type){
			case 'all': 
				$(".all").show();
				category = 'all';
				break;
			case 'general': 
				$(".all").hide();
				$(".general").show();
				category = 'general';				
				break;
			case 'login': 
				$(".all").hide();
				$(".login").show();
				category = 'login';
				break;	
			case 'examination': 
				$(".all").hide();
				$(".examination").show();
				category = 'examination';
				break;
			case 'school': 
				$(".all").hide();
				$(".school").show();
				category = 'school';
				break;
			case 'student': 
				$(".all").hide();
				$(".student").show();
				category = 'student';
				break;
			case 'attendance': 
				$(".all").hide();
				$(".attendance").show();
				category = 'attendance';
				break;
			case 'timetable': 
				$(".all").hide();
				$(".timetable").show();
				category = 'timetable';
				break;
			case 'staff': 
				$(".all").hide();
				$(".staff").show();
				category = 'staff';
				break;
			case 'circular': 
				$(".all").hide();
				$(".circular").show();
				category = 'circular';
				break;
			case 'parent_login': 
				$(".all").hide();
				$(".parent_login").show();
				category = 'parent_login';
				break;
			case 'transportation': 
				$(".all").hide();
				$(".transportation").show();
				category = 'transportation';
				break;
			case 'fees': 
				$(".all").hide();
				$(".fees").show();
				category = 'fees';
				break;
			case 'library': 
				$(".all").hide();
				$(".library").show();
				category = 'library';
				break;
			case 'admissions': 
				$(".all").hide();
				$(".admissions").show();
				category = 'admissions';
				break;
			case 'enquiry': 
				$(".all").hide();
				$(".enquiry").show();
				category = 'enquiry';
				break;
		}
	}

	$(document).on("click", ".changebool", function () {	
		var name = $(this).attr("data-name");
		var CurrentValue = $(this).attr("data-value");
		var $checkbox = $(this);
		var isChecked = $checkbox.prop('checked');
		
		bootbox.confirm({
			title: "Config Property Update",
			message: "Updating <strong>" + name +"</strong>. Are you sure?",
			buttons: {
				confirm: {
					label: 'Yes',
					className: 'btn-success'
				},
				cancel: {
					label: 'No',
					className: 'btn-danger'
				}
			},
			callback: function (result) {
				if(result) {
					$.ajax({
							url: '<?php echo site_url('config/switchValue'); ?>',
							type: 'post',
							data: {'name' : name, 'CurrentValue' : CurrentValue, email: "<?= $email; ?>", person_name: "<?= $name; ?>"},
							success: function (data) {
								if (data == 1) {
									let $row = $("tr").filter(function () {
										return $(this).find("td").eq(2).text().trim() === name;
									});
									// console.log($row);
									
									if ($row.length) {
										let $cells = $row.find("td");
										let $valueCell = $cells.eq(3);
										let $actionCell = $cells.eq(1);
										
										let displayVal = CurrentValue == 0 ? "Enabled" : "Disabled";
										$valueCell.html(displayVal);

										$actionCell.html('<span class="text-muted">Refresh to Edit</span>');
									}
									$(function(){
										new PNotify({
											title: 'Success',
											text: name  + " updated successfully!",
											type: 'success',
										});
									});
								} else {
									$(function(){
										new PNotify({
											title: 'Error',
											text: name  + " updated failed!",
											type: 'error',
										});
									});
								}
							}
						});
					
				} else {
					if (isChecked) {
						$checkbox.prop('checked', false).val(0).attr('data-value', 0);
					} else {
						$checkbox.prop('checked', true).val(1).attr('data-value', 1);
					}
				}
			}
		});
	});

	function deleteConfig(configName){
		bootbox.confirm({
			title: "Confirm",
			message: "Are you sure about removing config - " + configName + "?",
			buttons: {
				confirm:{
					label: 'Yes',
					className: 'btn-success'
				},
				cancel:{
					label: 'No',
					className: 'btn-danger'
				}
			},
			callback: function (result){
				if(result){
					$.ajax({
						url: '<?php echo site_url('config/deleteConfig'); ?>',
						type: 'post',
						data: {'name': configName, email: "<?= $email; ?>", person_name: "<?= $name; ?>"},
						success: function (data){
							if (data == 1) {
								let $row = $("tr").filter(function () {
									return $(this).find("td").eq(2).text().trim() === configName;
								});
								
								if ($row.length) {
									let $cells = $row.find("td");
									let $valueCell = $cells.eq(3);
									let $actionCell = $cells.eq(1);
									$actionCell.html('<span class="text-muted">Refresh to Edit</span>');
								}
								$(function(){
									new PNotify({
										title: 'Success',
										text: configName  + " updated successfully!",
										type: 'success',
									});
								});
							} else {
								$(function(){
									new PNotify({
										title: 'Error',
										text: configName  + " updated failed!",
										type: 'error',
									});
								});
							}
						}
					});
				}
			}
		});
	}

	function handleUpdateConfig(elem) {
		let { name, value, default: defaultVal, type, description } = elem.dataset;
		try {
			value = JSON.parse(value);
		} catch (e) {
			console.warn(`Invalid JSON in value for ${name}`, value);
			value = value || "";
		}

		try {
			defaultVal = JSON.parse(defaultVal);
		} catch (e) {
			console.warn(`Invalid JSON in default for ${name}`, defaultVal);
			defaultVal = defaultVal || "";
		}
		updateConfig(
			name,
			value,
			defaultVal,
			type,
			decodeURIComponent(description || '')
		);
	}

	function updateConfig(configName, configValue, configDefault, configType, description) {
		let message = '';
		let defaultOptions = [];

		// Decode all json-encoded input strings
		if (configType === 'select') {
			try {
				configValue = JSON.parse(configValue);
			} catch (e) {
				configValue = [];
			}
			try {
				configDefault = JSON.parse(configDefault);
			} catch (e) {
				configDefault = [];
			}
			try {
				description = JSON.parse(description);
			} catch (e) {
				description = '';
			}

			defaultOptions = Array.isArray(configDefault) ? configDefault : [];
			let selectedValues = Array.isArray(configValue) ? configValue : [];

			// 1. Selected items table
			let selectedTable = `<label>Currently Selected</label><br>`;
			if (selectedValues.length > 0) {
				const valuesText = selectedValues.join(', ');  // join values with newline
				selectedTable += `<input class="form-control w-50" rows="5" readonly value="${valuesText}"><br>`;
			} else {
				selectedTable += `<p><i>No values selected.</i></p>`;
			}

			// 2. Dropdown
			let selectHTML = `<label>Select Values</label><br><select id='val' name='newValue' class='form-control' style='width:46.5rem;'>`;
			defaultOptions.forEach(option => {
				let selected = selectedValues.includes(option) ? "selected" : "";
				selectHTML += `<option value="${option}" ${selected}>${option}</option>`;
			});
			selectHTML += `</select><br>`;

			// Full message
			message = `
				${selectedTable}
				${selectHTML}
				<label>Default Value</label><br>
				<textarea rows='3' disabled cols='90'>${defaultOptions.join(', ')}</textarea>
				<br><label>Description</label><br>
				<textarea rows='3' disabled cols='90'>${description}</textarea>
			`;
		} else {
			const text = configValue || configDefault;			
			message = `
				<label>Value</label><br>
				<textarea id='val' rows='5' cols='90' name='newValue'>${text}</textarea>
				<br><label>Default Value</label><br>
				<textarea rows='3' disabled cols='90'>${configDefault}</textarea>
				<br><label>Description</label><br>
				<textarea rows='3' disabled cols='90'>${description}</textarea>
			`;
		}

		bootbox.confirm({
			title: "Add/Update the <strong>" + configName + "</strong> value",
			message: message,
			buttons: {
				confirm: {
					label: 'Yes',
					className: 'btn-success'
				},
				cancel: {
					label: 'No',
					className: 'btn-danger'
				}
			},
			callback: function (result) {
				if (result) {
					let newValue;

					if (configType === 'select') {
						newValue = $('#val').val(); // returns array
						if (!Array.isArray(newValue)) newValue = [newValue];
						newValue = JSON.stringify(newValue);
					} else {
						newValue = $('#val').val();
					}

					$.ajax({
						url: '<?php echo site_url('config/updateSingleType'); ?>',
						type: 'post',
						data: {
							name: configName,
							newValue: newValue,
							type: configType,
							email: "<?= $email; ?>",
							person_name: "<?= $name; ?>"
						},
						success: function (data) {
							if (data == 1) {
								let $row = $("tr").filter(function () {
									return $(this).find("td").eq(2).text().trim() === configName;
								});
								// console.log($row);
								
								if ($row.length) {
									let $cells = $row.find("td");
									let $valueCell = $cells.eq(3);
									let $actionCell = $cells.eq(1);

									let displayVal = newValue;
									if (displayVal.length > 100) displayVal = displayVal.substring(0, 99) + "...";
									$valueCell.html(displayVal);

									$actionCell.html('<span class="text-muted">Refresh to Edit</span>');
								}
								$(function(){
									new PNotify({
										title: 'Success',
										text: configName  + " updated successfully!",
										type: 'success',
									});
								});
							} else {
								$(function(){
									new PNotify({
										title: 'Error',
										text: configName  + " updated failed!",
										type: 'error',
									});
								});
							}
						}
					});
				}
			}
		});
	}

	

	function enableBool(configName, configDefault){
		bootbox.confirm({
			title: "Confirm",
			message: "Add property <strong>" + configName + "</strong> to DB?",
			buttons: {
				confirm: {
					label: 'Yes',
					className: 'btn-success'
				},
				cancel: {
					label: 'No',
					className: 'btn-danger'
				}
			},
			callback: function (result) {
				if(result) {        
					$.ajax({
						url: '<?php echo site_url('config/updateSingleType'); ?>',
						type: 'post',
						data: {'name' : configName, 'newValue' : configDefault, 'type': 'boolean', email: "<?= $email; ?>", person_name: "<?= $name; ?>"},
						success: function (data) {
							if(data == 1) {
								let $row = $("tr").filter(function () {
									return $(this).find("td").eq(2).text().trim() === configName;
								});
								
								if ($row.length) {
									let $cells = $row.find("td");
									let $actionCell = $cells.eq(1);

									$actionCell.html('<span class="text-muted">Refresh to Edit</span>');
								}
								$(function(){
									new PNotify({
										title: 'Success',
										text: configName  + " updated successfully!",
										type: 'success',
									});
								});
							} else {
								$(function(){
									new PNotify({
										title: 'Error',
										text: configName  + " updated failed!",
										type: 'error',
									});
								});
							}
						}
					});
				}
			}
		});
	}

	function HtmlEncode(s) {
		var el = document.createElement("div");
		el.innerText = el.textContent = s;
		s = el.innerHTML;
		return s;
	}

	function updateimageConfig(configName, configValue, configDefault, configType, description){
		
		bootbox.confirm({
			title: "Add/Update the <strong>" + configName + "</strong> value",
			message: "<div class='form-group'><label class='col-md-3'></label><div class='col-md-8 '><div id='dvPreview'><img id='logopreviewing' name='photograph' style='width: 120px;height: 80px;display:none'src='' /><span id='percentage_logo_completed'style='font-size: 20px; display: none; position: absolute;top: 25px;left: 52px;right: 0;'>0 %</span></div></div></div><div  class='form-group'><label class='col-md-3 control-label' for='Logo'>Upload Image </label><div class='col-md-8'><input class='form-control' onchange='logo_load()' id='logo_load' name='signature' type='file' accept='image/*' /><input type='hidden' name='value' id='logo_url'><span id='logouploadError' style='color:red;'></span></div></div>",
			buttons: {
				confirm: {
					label: 'Yes',
					className: 'btn-success'
				},
				cancel: {
					label: 'No',
					className: 'btn-danger'
				}
			},
			callback: function (result) {
				if(result) {        
					var newValue = $('#logo_url').val();
					
					$.ajax({
						url: '<?php echo site_url('config/updateImageType'); ?>',
						type: 'post',
						data: {'name' : configName, 'newValue' : newValue, 'type': configType, email: "<?= $email; ?>", person_name: "<?= $name; ?>"},
						success: function (data) {
							if (data == 1) {
								let $row = $("tr").filter(function () {
									return $(this).find("td").eq(2).text().trim() === configName;
								});
								
								if ($row.length) {
									let $cells = $row.find("td");
									let $valueCell = $cells.eq(3);
									let $actionCell = $cells.eq(1);

									let displayVal = newValue;
									if (displayVal.length > 100) displayVal = displayVal.substring(0, 99) + "...";
									$valueCell.html(displayVal);

									$actionCell.html('<span class="text-muted">Refresh to Edit</span>');
								}
								$(function(){
									new PNotify({
										title: 'Success',
										text: configName  + " updated successfully!",
										type: 'success',
									});
								});
							} else {
								$(function(){
									new PNotify({
										title: 'Error',
										text: configName  + " updated failed!",
										type: 'error',
									});
								});
							}
						}
					});
				}
			}
		});
	}


	function logo_load() {
    	var src = $('#logo_load')[0];
		if (src.files && validatelogo(src.files[0], 'logo_load')) {
			completed_promises = 0;
			current_percentage = 0;
			total_promises = 1;
			in_progress_promises = total_promises;
			saveFileToStorage(src.files[0]);
			$('#logopreviewing').css('opacity', '0.3');
			$("#logouploadError").html("");
			readURL(src);
		} else {
			src.value = null;
		}
}

function validatelogo(file, errorId) {
    if (file.size > 10000000 || file.fileSize > 10000000) {
        $("#" + errorId + "Error").html("Allowed file size exceeded. (Max. 10 MB)")
        return false;
    }
    if (file.type != 'image/jpeg' && file.type != 'image/jpg' && file.type != 'image/png') {
        $("#" + errorId + "Error").html("Allowed file types are jpeg, jpg and png");
        return false;
    }
    return true;
}

function readURL(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function(e) {
			$('#logopreviewing').show();
            $('#logopreviewing').attr('src', e.target.result);
        }

        reader.readAsDataURL(input.files[0]);
    }
}

function saveFileToStorage(file) {
    $('#percentage_logo_completed').show();
    $('#logo_load').attr('disabled', 'disabled');
    $("#btnSubmit").prop('disabled', true);
    $.ajax({
        url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
        type: 'post',
        data: {
            'filename': file.name,
            'file_type': file.type,
            'folder': 'public'
        },
        success: function(response) {
            // console.log('Response: ',response)
            single_file_progress(0);
            response = JSON.parse(response);
            var path = response.path;
            var signedUrl = response.signedUrl;
			
            $.ajax({
                url: signedUrl,
                type: 'PUT',
                headers: {
                    "Content-Type": file.type,
                    "x-amz-acl": "public-read"
                },
                processData: false,
                data: file,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    xhr.upload.onprogress = function(e) {
                        // For uploads
                        if (e.lengthComputable) {
                            single_file_progress(e.loaded / e.total * 100 | 0);
                        }
                    };
                    return xhr;
                },
                success: function(response) {
                    $('#logo_url').val(path);  
                    $('#percentage_logo_completed').hide();
                    $('#logo_load').removeAttr('disabled');
                    $('#logopreviewing').css('opacity', '1');
                    $("#btnSubmit").prop('disabled', false);
                   
                },
                error: function(err) {
                    // console.log(err);
                    reject(err);
                }
            });
        },
        error: function(err) {
            reject(err);
        }
    });
}

function single_file_progress(percentage) {
    if (percentage == 100) {
        in_progress_promises--;
        if (in_progress_promises == 0) {
            current_percentage = percentage;
        }
    } else {
        if (current_percentage < percentage) {
            current_percentage = percentage;
        }
    }
    $("#percentage_logo_completed").html(`${current_percentage} %`);
    return false;
}


</script>
