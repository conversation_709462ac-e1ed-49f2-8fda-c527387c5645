<div class="col-md-12">
    <div class="card panel_new_style">
        <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
            <h3 class="card-title panel_title_new_style">
                <strong>Day wise status report</strong>
            </h3>
        </div>
        <div class="card-body px-2 py-1">
	      	<div class="row mb-5">
		        <div class="col-md-12 form-group">
		          	<label for="fromdateId" class="control-label">From Date</label>
		          	<div class="input-group date" id="start_date_picker"> 
		                <input required="" autocomplete="off" type="text" value="<?php echo date('d-m-Y', strtotime('-7 days')); ?>" class="form-control" id="fromdateId" name="from_date" >
		                <span class="input-group-addon">
		                    <span class="glyphicon glyphicon-calendar"></span>
		                </span>
		            </div>
		        </div>
		        <div class="col-md-12 form-group">
		          	<label for="todateId" class="control-label">To Date</label>
		          	<div class="input-group date" id="end_date_picker"> 
		               	<input required=""  autocomplete="off" type="text" value="<?php echo date('d-m-Y', strtotime("-1 days")); ?>" class="form-control " id="todateId" name="to_date">
		                <span class="input-group-addon">
		                	<span class="glyphicon glyphicon-calendar"></span>
		                </span>
		            </div>
		        </div>

				<div class="col-md-12 form-group">
					<label class="control-label">Staff Type</label>
					<select class="form-control" name="selected_staff_type" id="selected_staff_type">
						<option value="all">All</option>
						<?php foreach ($staff_types as $key => $val) {
							echo "<option value='$key'>$val</option>";
						}
						?>
					</select>
				</div>
				
				<div class="col-md-12 form-group">
		          	<label class="control-label">Staff Status Type</label>
					<select class="form-control" name="staff_status_type" id="staff_status_type" onChange="hideDataArea()">
						<!-- <option value="all">All</option> -->
							<option value='2'>Approved</option>
							<option value='4'>Resigned</option>
					</select>
		        </div>

				<div class="col-md-12 form-group">
					<label class="control-label">Late</label>
					<select class="form-control" name="show_late_only" id="show_late_only">
						<option value="all">All</option>
						<option value='1'>Show Late Only</option>
					</select>
				</div>

				<div >
					<div class="form-group mt-3">
						<label class="radio-inline" for="show_in_out_time">
							<input type="checkbox" style="width: 1.3rem;height: 1.3rem;" name="report_type" id="show_in_out_time" onclick="show_hide_timings()" value="percentage">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>Show In/Out Time</b>
						</label>
					</div>

					<div class="form-group mt-3" style="">
						<label class="radio-inline" for="show_hide_leave_Categories">
							<input type="checkbox" style="width: 1.3rem;height: 1.3rem;" name="report_type" id="show_hide_leave_Categories" onclick="showHideLeaveCategories()" value="percentage">&nbsp;&nbsp;<b style="margin-left: 12px;">Show Leave Categories</b>
						</label>
					</div>
				</div>

		        <div class="col-md-12 form-group">
		          	<button class="btn btn-primary mt-3 w-100" id="getReportBtn" onclick="getAttendanceData()">Get Report</button>
		        </div>
	      	</div>

            <div id="attendance-data">
            
            </div>

        </div>  
    </div>
</div>

<a href="<?php echo site_url('staff/attendance');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>



<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.16.9/xlsx.full.min.js" integrity="sha512-wBcFatf7yQavHQWtf4ZEjvtVz4XkYISO96hzvejfh18tn3OrJ3sPBppH0B6q/1SHB4OKHaNNUKqOmsiTGlOM/g==" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js" integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ==" crossorigin="anonymous"></script>
<script type="text/javascript">
var headers = [];
var json_data = [];
$(document).ready(function() {
	$('#start_date_picker').datepicker({
        format: 'dd-mm-yyyy',
        "autoclose": true
    });
    $('#end_date_picker').datepicker({
        format: 'dd-mm-yyyy',
        "autoclose": true,
        endDate: new Date()
    });
    getAttendanceData();
});

function hideDataArea(){
	$("#attendance-data").html("");
}

function show_hide_timings() {
	if($("#show_in_out_time").is(':checked')) {
		$(".date-td").attr('colspan', 4);
		$(".in-out").show();
		add_scroller('report-container');
	} else {
		$(".date-td").attr('colspan', 1);
		$(".in-out").hide();
		add_scroller('report-container');
	}
}

let isLeaveCateVissible=false;
function showHideLeaveCategories() {
	if(isLeaveCateVissible){
		$(".leave-cat-show-hide").hide();
	}else{
		$(".leave-cat-show-hide").show();
	}
	isLeaveCateVissible=!isLeaveCateVissible;
}

function constructHeader(dates,all_active_leave_categories) {
	headers = ['Sl', 'Staff','Email','Working Days','Present Days','Late Days','Absent Days'];

	var html = '';
	html += '<thead>';
	html += '<tr>';
	html += '<th rowspan="2">#</th>';
	html += '<th rowspan="2" style="min-width: 150px;">Staff Name</th>';
	html += '<th rowspan="2" style="min-width: 150px;">Email</th>';
	html += '<th rowspan="2" style="min-width: 150px;">Total Working Days</th>';
	html += '<th rowspan="2" style="min-width: 150px;">Total Present Days</th>';
	html += '<th rowspan="2" style="min-width: 150px;">Total Late Days</th>';
	html += '<th rowspan="2" style="min-width: 150px;">Absent Days</th>';

	//Here we need to show the all the active leave categories 
	if(Object.entries(all_active_leave_categories)?.length){
		for(let [catId,catName] of Object.entries(all_active_leave_categories)){
			headers.push(catName);
			html += `<th class="leave-cat-show-hide" rowspan="2" style="min-width: 150px;display:${isLeaveCateVissible==false && "none" || ""};" data-catid="${catId}">${catName}</th>`;
		}
	}

	for(var i in dates) {
		headers.push(dates[i].format2);
		if($("#show_in_out_time").is(':checked')) {
			html += '<th class="date-td" style="text-align:center;" colspan="4">'+dates[i].format2+'</th>';
		} else {
			html += '<th class="date-td" style="text-align:center;">'+dates[i].format2+'</th>';
		}
	}
	html += '</tr>';
	html += '<tr>';
	for(var i in dates) {
		if($("#show_in_out_time").is(':checked')) {
			html += '<th class="in-out" style="text-align:center;min-width: 80px;">In</th>';
			html += '<th class="in-out" style="text-align:center;min-width: 80px;">Out</th>';
			html += '<th class="in-out" style="text-align:center;min-width: 100px;">Duration</th>';
		} else {
			html += '<th class="in-out" style="text-align:center;display:none;min-width: 80px;">In</th>';
			html += '<th class="in-out" style="text-align:center;display:none;min-width: 80px;">Out</th>';
			html += '<th class="in-out" style="text-align:center;display:none;min-width: 100px;">Duration</th>';
		}
		html += '<th style="text-align:center;min-width: 100px;">Status</th>';
	}
	html += '</tr>';
	html += '</thead>';
	return html;
}

function constructReport(dates, attendance, all_active_leave_categories) {
	json_data = [];
	var html = '<tbody>';
	var j = 1;
	var colors = {
		AB : '#E06469',
		HD : '#FFAB76',
		P:"#E5F9DB",
		OOD:"#E5F9DB",
		WO:"#DBDFEA"
	};
	for(var i in attendance) {
		const totalAbsentDays=attendance[i].totalWorkingDays- attendance[i].totalPresentDays;

		var json = {};
		json['Sl'] = j;
		json['employee_code'] = attendance[i].employee_code;
		json['Staff'] = attendance[i].staff_name;
		json['Email'] = attendance[i].email;
		json['Working Days'] = attendance[i].totalWorkingDays;
		json['Present Days'] = attendance[i].totalPresentDays;
		json['Late Days'] = attendance[i].total_late_count;
		json['Absent Days'] = `${totalAbsentDays} (LF: ${attendance[i].totalleavestaken}, LNF: ${totalAbsentDays-attendance[i].totalleavestaken})`;

		html += '<tr>';
		html += '<td>'+(j++)+'</td>';
		html += '<td>'+attendance[i].staff_name+'</td>';
		if (attendance[i].email)
			html += `<td><a href="mailto:${attendance[i].email}">${attendance[i].email}</a></td>`;
		else
			html += `<td>-</td>`;
		html += '<td style="text-align:center;font-size: 14px;">'+attendance[i].totalWorkingDays+'</td>';
		html += '<td style="text-align:center;font-size: 14px;">'+attendance[i].totalPresentDays+'</td>';
		html += '<td style="text-align:center;font-size: 14px;">'+attendance[i].total_late_count+'</td>';
		
		html += `<td style="text-align:center;font-size: 14px;">${totalAbsentDays} (LF: ${attendance[i].totalleavestaken}, LNF: ${totalAbsentDays-attendance[i].totalleavestaken})</td>`;

		// here we need to the leave category's leave taken count
		if(Object.entries(all_active_leave_categories)?.length){
			for(let [catId,catName] of Object.entries(all_active_leave_categories)){
				if(catId in attendance[i].total_leave_taken_for_duration){
					const noOfDays=attendance[i].total_leave_taken_for_duration[catId];
					html += `<td class="leave-cat-show-hide" style="text-align:center;font-size: 14px;display:${isLeaveCateVissible==false && "none" || ""};">${noOfDays}</td>`;
					json[catName] = noOfDays;
				}else{
					html += `<td class="leave-cat-show-hide" style="text-align:center;font-size: 14px;display:${isLeaveCateVissible==false && "none" || ""};">0</td>`;
					json[catName] = 0;
				}
			}
		}

		for(var k in dates) {
			if(dates[k].format2 in attendance[i]) {
				var att_status = attendance[i][dates[k].format2].status;
				var is_late = attendance[i][dates[k].format2].is_late;
				const isToday=attendance[i][dates[k].format2].is_today;

				if($("#show_in_out_time").is(':checked')) {
					html += '<td class="in-out" style="text-align:center;min-width: 80px;">'+attendance[i][dates[k].format2].first_check_in_time+'</td>';
					html += `<td class="in-out" style="text-align:center;min-width: 80px;">${attendance[i][dates[k].format2].last_check_out_time} ${attendance[i][dates[k].format2].is_auto_check_out}</td>`;
					html += '<td class="in-out" style="text-align:center;min-width: 100px;">'+calculateTime(attendance[i][dates[k].format2].duration)+'</td>';
				} else {
					html += '<td class="in-out" style="text-align:center;display:none;min-width: 80px;">'+attendance[i][dates[k].format2].first_check_in_time+'</td>';
					html += `<td class="in-out" style="text-align:center;display:none;min-width: 80px;">${attendance[i][dates[k].format2].last_check_out_time} ${attendance[i][dates[k].format2].is_auto_check_out}</td>`;
					html += '<td class="in-out" style="text-align:center;display:none;min-width: 100px;">'+calculateTime(attendance[i][dates[k].format2].duration)+'</td>';
				}

				if(isToday==1){
					html += '<td style="min-width: 100px;text-align:center;">';
					html += "-";
					json[dates[k].format2] = att_status;
				}else{
					html += '<td style="min-width: 100px;text-align:center;background-color: '+colors[att_status]+'">';

					if(attendance[i][dates[k].format2].on_leave==1 && attendance[i][dates[k].format2]?.leave_information?.all_taken_leaves_names_for_single_Day){
						html += attendance[i][dates[k].format2]?.leave_information?.all_taken_leaves_names_for_single_Day;
						json[dates[k].format2] = attendance[i][dates[k].format2]?.leave_information?.all_taken_leaves_names_for_single_Day;
					}else{
						html += att_status;
						json[dates[k].format2] = att_status;
					}
				}
				// if(att_status == 'AB' || att_status == 'HD') {
				// 	// html += ' (LNF)';
				// }
				if(att_status.slice(0,2) !== 'AB' && is_late == 1) {
					html += ' <span style="color: #a30404;">(L)</span>';
				}
				html += '</td>';
			} else {
				const isVisible = $("#show_in_out_time").is(':checked');

				const displayStyle = isVisible ? '' : 'display:none;';
				
				html += `<td class="in-out" style="text-align:center;${displayStyle}min-width: 80px;">-</td>`;
				html += `<td class="in-out" style="text-align:center;${displayStyle}min-width: 80px;">-</td>`;
				html += `<td class="in-out" style="text-align:center;${displayStyle}min-width: 100px;">-</td>`;
				html += `<td style="min-width: 100px;text-align:center;">-</td>`;
				json[dates[k].format2] = '-';
			}
		}
		json_data.push(json);
		html += '</tr>';
	}
	html += '</tbody>';
	return html;
}

function calculateTime(duration) {
	if(duration == 0) return '';
    if(duration < 60) {
      return duration + ' mins';
    } else {
      var hr = duration / 60;
      return parseInt(hr) + ' hr ' + (duration % 60) + ' mins';
    }
  }

function getAttendanceData() {
	$("#getReportBtn").prop("disabled",true).text("Please wait...");

	$("#attendance-data").html('<div class="text-center"><i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i></div>')
	var from_date = $("#fromdateId").val();
	var to_date = $("#todateId").val();

	var staff_status_type = $("#staff_status_type").val();
	var selected_staff_type=$("#selected_staff_type").val();
	var show_late_only = $("#show_late_only").val();

	$.ajax({
        url: '<?php echo site_url('staff/attendance/getDayWiseAttendance'); ?>',
        type: 'post',
        data: {'from_date':from_date, 'to_date': to_date,'staff_status_type':staff_status_type,'selected_staff_type':selected_staff_type,'show_late_only':show_late_only},
        success: function(data) {
			$("#getReportBtn").prop("disabled",false).text("Get Report");

			if(data!=0){
				var data = JSON.parse(data);
				var dates = data.dates;
				var attendance = data.attendance;
				var all_active_leave_categories = data.all_active_leave_categories;

				var html = '<div class="justify-content-between align-items-center"><p><b style="color: red;">LF </b>: Leave Filed</p><b style="color: red;">LNF </b>: Leave Not Filed</p><div class="d-flex align-items-center"><div class="form-group" id="range-input" style="width: 300px;"></div><button class="mx-2 btn btn-primary mb-2 pull-right" onclick="exportToExcel()">Excel</button></div></div>';
				html += '<div class="table-responsive" id="report-container">';
				html += '<table id="att-table" class="table table-bordered">';
				html += constructHeader(dates, all_active_leave_categories);
				html += constructReport(dates, attendance, all_active_leave_categories);
				html += '</table>';
				html += '</div>';
				$("#attendance-data").html(html);
				add_scroller('report-container');
			}else{
					let msg = `
						<div style="color:red;text-align:center;
							color: black;
							border: 2px solid #fffafa;
							text-align: center;
							border-radius: 6px;
							position: relative;
							margin-left: 14px;
							padding: 10px;
							font-size: 14px;
							margin-top: 14px;
							background: #ebf3ff;">
							Found no data to show
							</div>
						`;
				$("#attendance-data").html(msg);
			}
        },
        error: function (err) {
		    console.log(err);
		}
    });
}

function exportToExcel() {
	var wb = XLSX.utils.book_new();
	var from_date = $("#fromdateId").val();
	var to_date = $("#todateId").val();
	wb.Props = {
	      Title: "Staff Attendance Report - "+from_date+" to "+to_date,
	      Subject: "Attendance Report",
	      Author: "NextElement",
	      CreatedDate: new Date()
	};

	wb.SheetNames.push("Attendance");
	var ws_school;
	if($("#show_in_out_time").is(':checked')) {
		ws_school = XLSX.utils.table_to_sheet(document.getElementById('att-table'));
	} else {
		ws_school = XLSX.utils.json_to_sheet(json_data, {'headers' : headers});
	}
	wb.Sheets["Attendance"] = ws_school;

	var wbout = XLSX.write(wb, {bookType:'xlsx',  type: 'binary'});
	downloadSample(wbout);
}

function s2ab(s) {
  	var buf = new ArrayBuffer(s.length);
  	var view = new Uint8Array(buf);
  	for (var i=0; i<s.length; i++) view[i] = s.charCodeAt(i) & 0xFF;
  	return buf;
  
}

function downloadSample(wbout){
  	saveAs(new Blob([s2ab(wbout)],{type:"application/octet-stream"}), 'Staff_Attendance.xlsx');
};
</script>