<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('staff/attendance');?>">Staff Attendance</a></li>
  <li>Individual Staff Attendance</li>
</ul>

<div class="col-md-12">
  	<div class="card cd_border" style="position: relative;">
	    <div class="card-header panel_heading_new_style_staff_border">
	      <div class="row" style="margin: 0px;">
	        <div class="d-flex justify-content-between" style="width:100%;">
	          <h3 class="card-title panel_title_new_style_staff">
	            <a class="back_anchor" href="<?php echo site_url('staff/attendance'); ?>">
	              <span class="fa fa-arrow-left"></span>
	            </a> 
	            Individual Staff Attendance
	          </h3>
	        </div>
	      </div>
	    </div>

        <div class="card-body pt-1">

            <div class="col-md-2 form-group">
                <label for="fromdateId" class="control-label">Attendance range</label>
                <div id="reportrange" class="dtrange" style="width: 100%">                                            
                    <span></span>
                    <input type="hidden" id="from_date">
                    <input type="hidden" id="to_date">
                </div>
            </div>

            <div class="col-md-2 form-group">
                <label class="control-label">Staff Status Type</label>
                <select class="form-control" name="staff_status_type" id="staff_status_type" onChange="getStaffNames()">
                    <!-- <option value="all">All</option> -->
                        <option value='2'>Approved</option>
                        <option value='4'>Resigned</option>
                </select>
            </div>

            <div class="col-md-2 form-group">
                <label class="control-label">Staff</label>
                <select class="form-control select2" name="selected_staff_id" id="selected_staff_id">
                    <option value="-1">Select Staff</option>
                    <?php foreach ($staff_list as $staff) {
                            $selected=$staff_id==$staff->id ? 'Selected' : '';
                            echo "<option $selected value='$staff->id'>$staff->employee_code. - .$staff->staff_name</option>";
                        }
                    ?>
                </select>
            </div>
            <div class="col-md-3 form-group pt-3">
                <button class="btn btn-primary mt-3" onclick="get_indv_staff_attendance()">Get Report</button>
            </div>
    	</div>

        <!-- <button id="exportBtn" style="display:none;width: 100px;position: absolute;right: 10px;top: 67px;" class="btn btn-warning" onClick="exportToExcel()">Export</button> -->

        <div id="export_data">
            <div class="card-body pt-1">
                <div id="attendance_summary"></div>
            </div>
            
            <div class="card-body pt-1">
                <div id="indv-attendance-data" style="overflow:auto;"></div>
            </div>
        </div>
  	</div>
</div>


<style>
  /* styles over here */
  .modal {
    overflow-y:auto;
  }
  
  .modal-dialog{
    margin: 4% auto;
    width: 80%;
  }
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .dt-buttons{
    font-size: 14px;
    background:"red";
  }

    #staff_table_wrapper{
        width:200%;
    }

     .modal {
    overflow-y:auto;
  }
  
  .modal-dialog{
    margin: 4% auto;
    width: 80%;
  }
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .dt-buttons{
    font-size: 14px;
    background:"red";
  }

  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 20%;
	}

	.dt-buttons{
		position:absolute;
		right:15px;
	}

	@media only screen and (min-width:1404px){
		.dataTables_filter{
			position:absolute;
			right: 15%;
		}	
	}

	@media only screen and (min-width:1734px){
		.dataTables_filter{
			position:absolute;
			right: 11%;
		}	
	}

    .swal2-modal{
        width: 50%;
    }
</style>


<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script language="javascript">
    var total_days = 0;
    var total_present = 0;
    var total_absent = 0;
    var total_wo = 0;
    var total_late = 0;
    var total_outside = 0;
    var total_holiday = 0;

    $(document).ready(function() {
        $("#reportrange").daterangepicker({
            ranges: {
                'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Last 7 Days': [moment().subtract(7, 'days'), moment().subtract(1, 'days')],
                'Last 30 Days': [moment().subtract(30, 'days'), moment().subtract(1, 'days')],
            },
            dateLimit: {
                'months': 1,
                'days': 0
            },
            opens: 'right',
            buttonClasses: ['btn btn-default'],
            applyClass: 'btn-small btn-primary',
            cancelClass: 'btn-small',
            format: 'DD-MM-YYYY',
            separator: ' to ',
            startDate: moment().subtract(7, 'days'),
            endDate: moment().subtract(1, 'days')
        },function(start, end) {
                $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
                $('#from_date').val(start.format('DD-MM-YYYY'));
                $('#to_date').val(end.format('DD-MM-YYYY'));
        });

        $("#reportrange span").html(moment().subtract(7, 'days').format('MMM D, YYYY') + ' - ' + moment().subtract(1, 'days').format('MMM D, YYYY'));
        $('#from_date').val(moment().subtract(7, 'days').format('DD-MM-YYYY'));
        $('#to_date').val(moment().subtract(1, 'days').format('DD-MM-YYYY'));

        get_indv_staff_attendance();

        $(".select2").select2();
    });

    function getStaffNames(){
        const staff_status_type = $("#staff_status_type").val();

        $.ajax({
            url: '<?php echo site_url('staff/attendance/get_staff_by_status'); ?>',
            type: 'post',
            data: {'staff_status_type':staff_status_type},
            success: function(data) {
                data=JSON.parse(data);

                let options=``;
                if(data?.length){
                    data.forEach(s=>{
                        options+=`<option value=${s.id}>${s.employee_code} - ${s.staff_name}</option>`;
                    });
                }else{
                    options=`<option value="0">No Staff</option>`;
                }

                $("#selected_staff_id").html(options);

                get_indv_staff_attendance();
            }
        });
    }

    function get_indv_staff_attendance() {
         const msg = `
            <div style="color:red;text-align:center;
              color: black;
              border: 2px solid #fffafa;
              text-align: center;
              border-radius: 6px;
              position: relative;
              margin-left: 14px;
              padding: 10px;
              font-size: 14px;
              margin-top: 14px;
              ">
                Loading...
            </div>`;

        $("#indv-attendance-data").hide();

	    $("#attendance_summary").html(msg);

        //Initialize summary
        total_days = 0;
        total_present = 0;
        total_absent = 0;
        total_wo = 0;
        total_late = 0;
        total_outside = 0;
        total_holiday = 0;
        
        var from_date = $("#from_date").val();
        var to_date = $("#to_date").val();
        var selected_staff_id = $("#selected_staff_id").val();
        
        var staff_status_type = $("#staff_status_type").val();

        $.ajax({
            url: '<?php echo site_url('staff/attendance/get_indv_staff_attendance'); ?>',
            type: 'post',
            data: {'from_date':from_date, 'to_date': to_date, 'selected_staff_id': selected_staff_id,'staff_status_type':staff_status_type},
            success: function(data) {
                var data = JSON.parse(data);

                if(!data?.length){
                     const selectStaff = `
                        <div style="color:red;text-align:center;
                        color: black;
                        border: 2px solid #fffafa;
                        text-align: center;
                        border-radius: 6px;
                        position: relative;
                        margin-left: 14px;
                        padding: 10px;
                        font-size: 14px;
                        margin-top: 14px;
                        background:#b5b5df;
                        ">
                            Please Select A Staff To Proceed
                        </div>`;

                    $("#indv-attendance-data").hide();

                    $("#attendance_summary").html(selectStaff);
                    return;
                }

                data?.length && $("#exportBtn").css("display","block") || $("#exportBtn").css("display","none");

                var output = construct_table (data);
			    $("#indv-attendance-data").html(output);

                var summary = construct_summary ();
                $('#attendance_summary').html(summary); 

                $("#indv-attendance-data").show();

                const reportName=`staff_attendance_individual_report_${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}`;

                $('#staff_table').DataTable( {
                    "language": {
                        "search": "",
                        "searchPlaceholder": "Enter Search..."
                    },
                    "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
                            "pageLength": 10,
                    dom: 'lBfrtip',
                    buttons: [
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            filename: reportName,
                            className: 'btn btn-info'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            filename: reportName,
                            className: 'btn btn-info'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            filename: reportName,
                            className: 'btn btn-info'
                        }
                    ]
                });
                add_scroller('individual_staff_scroll');
            }
        });
    }

    function construct_table (data) {
        var output = `
            <div class="d-flex justify-content-between align-items-center"><p><b style="color: red;"> </b><b style="color: red;"> </b></p><div class="d-flex align-items-center"><div class="form-group" id="range-input" style="width: 300px;"></div></div></div>`;

            output += '<div class="table-responsive" id="individual_staff_scroll">';
            
        output+=`
            <table class="table table-bordered" id="staff_table" style="width:100%;white-space:nowrap;">
                <thead>
                    <th>Sl #</th>
                    <th>Date</th>
                    <th>Day</th>
                    <th>Shift-start time</th>
                    <th>Shift-end time</th>
                    <th>Check-in time</th>
                    <th>Check-in location</th>
                    <th>Check-out time</th>
                    <th>Check-out location</th>
                    <th>Total time spent</th>
                    <th>Late?</th>
                    <th>Over-ride?</th>
                    <th>Status</th>
                    <th>Leave taken</th>
                    <th>Check-in/Late remark</th>
                    <th>Check-out remark</th>
                    <th>Check-in source</th>
                    <th>Face Check-in no. of attempts</th>
                    <th>Check-in device Info</th>
                    <th>Check-out source</th>
                    <th>Face Check-out no. of attempts</th>
                    <th>Check-out device Info</th>
                </thead>
                <tbody>
                `;

        let curDate=new Date();
        const today=curDate.setDate(curDate.getDate()-1);
        var i = 1;
        data.forEach(staff => {
            if(new Date(staff.date).getTime()<today){
                output += _construct_single_staff(staff, i++);
            }
        });

        total_days = i-1;

        output += `
                </tbody>
            </table>
            </div>
        `;

        return output;
    }

    function construct_summary() {
        var output = `
            <h4>Summary</h4>
            <table class="table table-bordered" style='width:25%'>
                <tbody>
                    <tr><td width="70%">Days</td><td width="30%">${total_days}</td></tr>
                    <tr><td>Weekly-off/Holiday</td><td>${total_wo + total_holiday}</td></tr>
                    <tr><td>Present</td><td>${total_present}</td></tr>
                    <tr><td>Absent</td><td>${total_absent}</td></tr>
                    <tr><td>Late</td><td>${total_late}</td></tr>
                    <tr><td>Outside Campus Check-in</td><td>${total_outside}</td></tr>
                </tbody>
            </table>
                `;
        
        return output;
    }

    function _construct_single_staff(staff,i) {
        const [date,month,year]=staff.date.split(" ");
        
        const currentDateTime=new Date();
        const [currentDate,currentMonth,currentYear]=[currentDateTime.getDate(),currentDateTime.toLocaleString('default', { month: 'short' }),currentDateTime.getFullYear()];

        //Late
        var late_str = (staff.is_late != null) ? (staff.is_late === '0' ? '<span style="color:green">On-time</span>' : '<span style="color:red">Late</span>') : '-';

        (staff.is_late === '1') ? total_late ++ : '';

        //Over-ride
        var is_manually_changed_str = (staff.is_manually_changed != null) ? (staff.is_manually_changed === '0' ? '-' : 'Over-ridden') : '-';

        //Status
        var status_str = '';
        var row_color = '#e3b5b5';
        switch (staff.status) {
            case 'P':
                status_str = 'Present';
                row_color = '#cae7ca';
                total_present ++;
                break;
            case 'OOD':
                status_str = 'OOD';
                row_color = '#cae7ca';
                total_present ++;
                break;
            case 'AB':
                status_str = 'Absent';
                row_color = '#e3b5b5';
                total_absent ++;
                break;
            case 'WO':
                status_str = 'Weekly Off';
                row_color = 'grey';
                total_wo ++;
                break;
                case 'HD':
                status_str = 'Half Day';
                row_color = '#cae7ca';
                total_present += 0.5;
                total_absent += 0.5;
                break;
            case 'H':
                status_str = 'Holiday';
                row_color = '#e3b5b5';
                total_holiday ++;
                break;
        }

        if((!staff.last_check_out_time) && +date===+currentDate && month===currentMonth && +year===+currentYear){
            status_str="NA";
            row_color="";
        }

        var leave_str = '';
        switch (staff.leave_taken_status) {
            case 'CL':
                leave_str = 'Casual Leave';
                break;
            case 'PL':
                leave_str = 'Privilege Leave';
                break;
            case 'SL':
                leave_str = 'Sick Leave';
                break;
            case 'LOP':
                leave_str = 'Leave Without Pay';
        }

        var is_checkin_outside_str = (staff.first_check_in_time != null && staff.first_check_in_time != '') ? (staff.is_checkin_outside_campus == '1' ? '<span style="color:"><b>Outside check-in</span></b>' : '') : '';
        var is_checkout_outside_str = (staff.last_check_out_time != null && staff.last_check_out_time != '') ? (staff.is_checkout_outside_campus == '1' ? '<span style="color:"><b>Outside check-out</span></b>' : '') : '';

        (staff.is_checkin_outside_campus == '1') ? total_outside ++ : '';
        (staff.is_checkout_outside_campus == '1') ? total_outside ++ : '';
        var checkedInPhoto = staff.first_check_in_time != '' && staff.check_in_device_info?.source != 'Staff Login' ? `<button class="btn btn-primary" onclick="get_checked_in_out_photo('checkIn', '${staff.attendance_id}')">View</button>` : '';
        var checkedOutPhoto = staff.last_check_out_time != '' && !staff.last_check_out_time.includes('Auto Checkout') && staff.check_in_device_info?.source != 'Staff Login' ? `<button class="btn btn-primary" onclick="get_checked_in_out_photo('checkOut', '${staff.attendance_id}')">View</button>` : '';
        var output = `
                <tr style="background:${row_color}">
                    <td>${i}</td>
                    <td>${staff.date}</td>
                    <td>${staff.day}</td>
                    <td>${staff.shift_start_time}</td>
                    <td>${staff.shift_end_time}</td>
                    <td>${checkedInPhoto} ${staff.first_check_in_time}</td>
                    <td>
                        ${is_checkin_outside_str}
                        ${is_checkin_outside_str ? staff.check_in_location?.toLowerCase().includes("near") ? "" : staff.check_in_location ?"Near" : "NA"  : ""}
                        ${staff.check_in_location}
                    </td>
                    <td>${checkedOutPhoto} ${staff.last_check_out_time} ${staff.is_auto_check_out.trim()?.length ? `(${staff.is_auto_check_out})` : ""}</td>
                    <td>
                        ${is_checkout_outside_str}
                        ${is_checkout_outside_str ? staff.check_out_location?.toLowerCase().includes("near") ? "" : staff.check_out_location ?"Near" : "NA"  : ""}
                        ${staff.check_out_location}
                    </td>`;
                    
                    if(staff.attendance_id!=null){
                        output+=`<td>${staff.hours_spent || 0} Hours : ${staff.mins_spent || 0} mins : ${staff.mins_spent || 0} secs</td>`;
                    }else{
                        output+=`<td>-</td>`;
                    }

                    output+=`<td>${late_str}</td>
                    <td>${is_manually_changed_str}</td>
                    <td>${status_str}</td>
                    <td>${staff.leave_taken_status ? `On ${staff.leave_taken_status}` : "-"}</td>
                    <td>${staff.check_in_remark || "-"}</td>
                    <td>${staff.check_out_remark || "-"}</td>`;

                    if(!staff.check_in_device_info?.device_info?.length){
                        output += `<td>-</td>`;
                        output += `<td>-</td>`;
                        output += `<td>-</td>`;
                    }else{
                        output += `<td>${staff.check_in_device_info?.source}</td>`;
                        output += `<td class="${staff.check_in_device_info?.number_of_attempts=="Attempt 3" ? 'bg-danger' : ''}">${staff.check_in_device_info?.number_of_attempts==="Attempt 3" ? "Exception Check-in" : `${staff.check_in_device_info?.number_of_attempts || "-"}`}</td>`;
                        output += `<td>
                            <a href="javascript:void(0)" class="link link-danger" id="device-info-${i}" data-info='${staff.check_in_device_info?.device_info}' onclick="showDeviceInfo(${i},'${staff.date}','Check-in')">
                                View info ${staff.check_in_device_info?.is_diff_device==1 ? "<span style='padding: 3px;border-radius: 4px;' class='bg-danger'>Device has changed</span>" : ""}
                            </a>
                        </td>`;
                    }

                    if(!staff.check_out_device_info?.device_info?.length){
                        output += `<td>-</td>`;
                        output += `<td>-</td>`;
                        output += `<td>-</td>`;
                    }else{
                        output += `<td>${staff.check_out_device_info?.source}</td>`;
                        output += `<td class="${staff.check_out_device_info?.number_of_attempts=="Attempt 3" ? 'bg-danger' : ''}">${staff.check_out_device_info?.number_of_attempts==="Attempt 3" ? "Exception Check-out" : `${staff.check_out_device_info?.number_of_attempts || "-"}`}</td>`;
                        output += `<td>
                            <a href="javascript:void(0)" class="link link-danger" id="device-info-${i+1}" data-info='${staff.check_out_device_info?.device_info}' onclick="showDeviceInfo(${i+1},'${staff.date}','Check-out')">
                                View info ${staff.check_out_device_info?.is_diff_device==1 ? "<span style='padding: 3px;border-radius: 4px;' class='bg-danger'>Device has changed</span>" : ""}
                            </a>
                        </td>`;
                    }

                output += `</tr>
            `;

        return output;
    }

    function showDeviceInfo(indexId,date,attendanceType){
        const deviceInformation=$(`#device-info-${indexId}`).data("info");

        const deviceId=deviceInformation?.deviceId || "Not captured";
        const deviceName=deviceInformation?.deviceName ? deviceInformation?.deviceName : "Not captured";
        const isTouchDevice=deviceInformation?.isTouchDevice || "Not captured";
        const language=deviceInformation?.language || "Not captured";
        const platform=deviceInformation?.platform || "Not captured";
        const screenHeight=deviceInformation?.screenHeight || "Not captured";
        const screenWidth=deviceInformation?.screenWidth || "Not captured";

        let html=`
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <th style="width: 41%;text-align: left;">Device Id</th>
                        <td>${deviceId}</td>
                    </tr>

                    <tr>
                        <th style="width: 41%;text-align: left;">Device name</th>
                        <td>${deviceName}</td>
                    </tr>

                    <tr>
                        <th style="width: 41%;text-align: left;">Is touch device</th>
                        <td>${isTouchDevice}</td>
                    </tr>

                    <tr>
                        <th style="width: 41%;text-align: left;">Language</th>
                        <td>${language}</td>
                    </tr>

                    <tr>
                        <th style="width: 41%;text-align: left;">Platform</th>
                        <td>${platform}</td>
                    </tr>

                    <tr>
                        <th style="width: 41%;text-align: left;">Screen height</th>
                        <td>${screenHeight}</td>
                    </tr>

                    <tr>
                        <th style="width: 41%;text-align: left;">Screen width</th>
                        <td>${screenWidth}</td>
                    </tr>

                </tbody>
            </table>
        `;

        Swal.fire({
            title: `${attendanceType} Device Information for ${date}`,
            icon: "info",
            html:html,
        });
    }

//     function exportToExcel(){
//       var htmls = "";
//       var uri = 'data:application/vnd.ms-excel;base64,';
//       var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
//       var base64 = function(s) {
//           return window.btoa(unescape(encodeURIComponent(s)))
//       };

//       var format = function(s, c) {
//           return s.replace(/{(\w+)}/g, function(m, p) {
//               return c[p];
//           })
//       };

      
//       var mainTable = $("#export_data").html();
     
//       htmls = mainTable;
//       var htmls = htmls.replace(/<img[^>]*>/gi,"");
//       var ctx = {
//           worksheet : 'Spreadsheet',
//           table : htmls
//       }
//       var link = document.createElement("a");
//       link.download = `individual_staff_report_${new Date().toLocaleDateString()}_${new Date().getHours()}:${new Date().getMinutes()}.xls`;
//       link.href = uri + base64(format(template, ctx));
//       link.click();

//   }

function get_checked_in_out_photo(type, attendance_id){
    console.log(type);
    console.log(attendance_id);
    if(attendance_id == '' || attendance_id <= 0){
        Swal.fire({
            icon: "error",
            title: "Attendance Id Not Found!, Try Again Later",
            showConfirmButton: false,
            timer: 1500,
            width: 250,
        });
        return;
    }

    $.ajax({
        url: '<?php echo site_url('staff/attendance/get_checked_in_out_photo'); ?>',
        type: "post",
        data: {
            'type': type,
            'attendance_id': attendance_id
        },
        success: function (data) {
            // console.log(data);
            let parsedVal = JSON.parse(data);
            // console.log(parsedVal);
            if(type == 'checkIn') {
                type = 'Check-in';
            } else if(type == 'checkOut') {
                type = 'Check-out';
            } else {
                type = '';
            }
            if(parsedVal != 0){
                Swal.fire({
                    title: `${type} Photo`,
                    // text: `Here is the staff ${type} image.`,
                    imageUrl: `${parsedVal}`,
                    imageWidth: 250,
                    imageHeight: 200,
                    imageAlt: `Staff ${type} Photo`,
                    confirmButtonText: 'Close',
                    width: 300,
                });
                // window.open(parsedVal, '_blank');
            } else {
                Swal.fire({
                    icon: "info",
                    title: `${type} Photo Not Found!`,
                    showConfirmButton: false,
                    timer: 1500,
                    confirmButtonText: 'Close',
                    width: 250,
                });
                return;
            }
        },
        error: function(err) {
            Swal.fire({
                icon: "error",
                title: "No Data Found!, Try Again Later",
                showConfirmButton: false,
                timer: 1500,
                width: 250,
            });
        }
    });
}
</script>
