<ul class="breadcrumb">
    <li><a href="<?php echo site_url('avatars') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('academics/academics_menu/index') ?>">Academics</a></li>
    <li class="active">Plan Syllabus Schedule</li>
</ul>
<div class="col-md-12 col_new_padding">
    <div class="card cd_border" style="border: none;">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-4 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('academics/academics_menu/index') ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Plan Syllabus Schedule
                    </h3>
                </div>
                <div class="col-md-6" id="" style="position: absolute;right: 2rem;top:1.5rem;">
                    <div style="display:flex;float:right;">
                        <button class="btn btn-primary mr-2" id="activate-deactivate-btn" name="deactive-session" id="deactive-session" style="display: none;" onclick="showDeActiveSessions()">
                            Activate Sessions
                        </button>

                        <!-- Plan Approval buttons for Admin -->
                        <div id="admin_approval_btns">

                        </div>
                        <!-- Plan Approval buttons for staff -->
                        <div id="staff_approval_btns">

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body_new mb-3">
            <div class="container">
                <div class="show-grade-subject margin-bottom">
                    <!-- <div class="d-flex align-items-center" id=""> -->
                    <!-- <div class="mr-auto"> -->
                    <div class="page-context-header">
                        <div class="page-header-headings">
                            <div class="class-subject-section" style="display: flex;">
                                <div class=""
                                    style="margin-right: 2%; width: <?php echo $is_semester_scheme == 1 ? '31.33%' : '48%'; ?>;"
                                    id="class_details">
                                    <label>Choose class <font color="red">*</font></label>
                                    <select class="form-control" name="class_id_main" id="class_id_main" onchange="<?php echo $is_semester_scheme == 1 ? 'getSemesters()' : 'getSubjetsList()'; ?>">
                                        <option value="">Choose class</option>
                                    </select>
                                    <div style="position: absolute; right: <?php echo $is_semester_scheme == 1 ? '63.5%' : '52.5%'; ?>; top: 109px; transform: translateY(-50%);z-index:5;">
                                        <i class="fa fa-caret-down"></i>
                                    </div>
                                </div>

                                <div class="" id="semester_container"
                                    style="margin-right: 2%; display: <?php echo $is_semester_scheme == 1 ? 'block' : 'none'; ?>; width: <?php echo $is_semester_scheme == 1 ? '31.33%' : '48%'; ?>;">
                                    <label>Choose semester <font color="red">*</font></label>
                                    <select class="form-control" name="select_grade" id="select_semester"
                                        onchange="getSubjetsList()">
                                        <option value="">Choose semester</option>
                                    </select>
                                    <div style="position: absolute; right: 41%; top: 109px; transform: translateY(-50%);z-index:5;">
                                        <i class="fa fa-caret-down"></i>
                                    </div>
                                </div>

                                <div class="" style="width: <?php echo $is_semester_scheme == 1 ? '33.33%' : '50%'; ?>;"
                                    id="subject_details">
                                    <label>Choose subject <font color="red">*</font></label>
                                    <select class="form-control" name="subject_id_main" id="subject_id_main">
                                        <option value="">Choose subject</option>
                                    </select>
                                    <div style="position: absolute; right: <?php echo $is_semester_scheme == 1 ? '17.5%' : '17.5%'; ?>; top: 109px; transform: translateY(-50%);z-index:5;">
                                        <i class="fa fa-caret-down"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="heading" style="display:none;">
                            <div class="heading-content">
                                <h2>
                                    Plan Syllabus Schedule For Grade <span class="grade-subject"><span id="heading_grade">Loading...</span> - <span id="heading_subject">Loading...</span></span>
                                </h2>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="weeks_section">
                    <div class="display-weeks margin-bottom blur">
                        <div class="week week-1 set-week-width" id="week-1">
                            <div class="col-md-11 d-flex align-items-center pl-0">
                                <i class="fa fa-chevron-down mr-3" aria-hidden="true"></i>
                                <span style="font-size:1.8rem; user-select: none;"> Week 1</span>
                            </div>
                        </div>
                        <div class="show-topic-session" id="week-1-area" style="margin-top:20px;">
                            <div class="outer-card">
                                <div class="left-part">
                                    <div class="title-desc">
                                        <div class="col-md-10 py-3" style="max-width:30.25rem;user-select: none;">
                                            <h4 class="mb-2"><i>Topic 1</i></h4>
                                            <h5 class="m-0">Session 1</h5>
                                        </div>
                                    </div>
                                </div>
                                <div class="move-session-arrows">
                                    <div class="btn-group btn-group-lg" role="group">
                                        <button disabled type="button" class="btn btn-info"><i
                                                class="fa fa-arrow-down m-0" aria-hidden="true"></i></button>
                                        <button disabled type="button" class="btn btn-info"><i
                                                class="fa fa-arrow-up m-0" aria-hidden="true"></i></button>
                                    </div>
                                </div>
                            </div>
                            <div class="btn-div">
                                <button disabled data-toggle="modal" data-target="#add_session"
                                    class="new_circleShape_res addSession" data-action="open-chooser" data-sectionid="0"
                                    data-sectionreturnid="0" id=""
                                    style="margin-left: 8px; background-color: #fe970a; position: absolute;top: -10px;right: 10px;width: 80px;height: 39px;">
                                    <span class="fa fa-plus" aria-hidden="true"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="deactiveSessionModal" tabindex="-1" role="dialog"
        aria-labelledby="deactiveSessionModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deactiveSessionModalLabel">Activate Session(s)</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body deactive-session-container">
                    Loading...
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <!-- <button type="button" class="btn btn-primary">Save changes</button> -->
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->load->view('academics/lesson_plan/report/manage_session/add_session_modal'); ?>
<?php $this->load->view('academics/lesson_plan/report/manage_session/edit_session_modal'); ?>

<style>
    .heading {
        background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .heading-content {
        text-align: center;
    }

    .heading h2 {
        color: #2c3e50;
        font-size: 1.8rem;
        margin: 0;
        font-weight: 600;
    }

    .card-body {
        width: 50%;
        margin: auto;
    }

    .margin-bottom {
        margin-bottom: 2rem;
    }

    .form-control {
        margin-bottom: 10px;
    }

    .h2 {
        /* color: #b85b05; */
        font-weight: 700;
        line-height: 1.2;
    }

    .weeks_section {
        display: flex;
        flex-direction: column;
        justify-content: center;
        /* align-items: center; */
        border: solid 1px #eee;
        border-radius: 1rem;
        padding: 1rem 1rem 1rem 1rem;
        background: #f5f5f5;
        /* background: #f5f5f5 url(../img/bg.png) left top repeat; */
        /* max-height: 80vh;
        overflow-y: auto; */
    }

    .outer-card {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 5px;
        border: 1px solid #DEE2E6;
        padding: 5px;
        margin-bottom: 10px;
        transition: all 300ms ease-out;
        position: relative;
        margin-top: 5px;
        border: solid 1px #eee;
        border-radius: 1rem;
    }

    .outer-card:hover {
        background-color: #f5f9fc;
        border: 1px solid #3584c9;
    }

    .left-part {
        display: flex;
        justify-content: center;
    }

    .title-desc {
        font-size: 15px;
        font-weight: 400;
    }

    .title {
        color: #336DB5;
    }

    .desc {
        color: #399BE2;
    }

    .menu-action:hover {
        color: #fff;
        background: #0f6cbf;
    }

    .menu-action{
        padding: 8px 15px;
        text-align: left;
        color: #333;
        background-color: #fff;
        border-bottom: 1px solid #e9e9e9;
        border-radius: 3px;
        font-size: 12px;
        cursor: pointer;
        text-align: left;
        transition: all 0.3s ease;
    }

    .set-week-width {
        /* display: inline-block; */
        display: flex;
        align-items: center;
        width: 65%;
        cursor: pointer;
    }

    .addSession {
        border-radius: 0.5rem;
        color: #0f6cbf;
        background-color: #f5f9fc;
        border: 1px solid #3584c9;
        width: 100%;
        font-size: 15px;
        padding: 21px;
        transition: all 200ms ease-in;
    }

    .addSession:hover {
        border: 1px solid #3584c9;
        background: #CFE2F2;
        text-decoration: underline;
    }

    .pluscontainer {
        border: 1px solid;
    }

    .fa-plus {
        position: relative;
        left: 50%;
        transform: translateX(-50%);
    }

    .move-session-arrows {
        display: flex;
        font-size: 27px;
        cursor: pointer;
        justify-content: space-between;
        align-items: center;
        position: absolute;
        right: 7%;
    }

    .bootbox .modal-dialog {
        width: 50%;
        margin: auto;
    }

    .blur {
        filter: blur(3px)
    }

    @media screen and (max-width:1290px) {
        .card-body {
            width: 70%;
        }
    }

    .content_box {
        /* border: solid 1px #eee;
        border-radius: 1rem;
        margin-bottom: 1rem; */
    }

    .card-body_new {
        width: 70%;
        margin: auto;
    }

    .new_circleShape_res {
        padding: 10px;
        border-radius: 50% !important;
        color: white !important;
        font-size: 22px;
        height: 3.2rem !important;
        width: 3.2rem !important;
        text-align: center;
        vertical-align: middle;
        float: left;
        border: none !important;
        box-shadow: 0px 3px 7px #ccc;
        line-height: 1.7rem !important;
    }

    .badge-info {
        color: #fff;
        background-color: #17a2b8;
        height: 2rem;
        font-size: 1rem;
        padding-top: 5px;
        text-align: center;
    }

    .dropdown-item{
        margin: 4px 0px;
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    function loadClass() {
        let isClassSessionPresent = false;
        let options = `<option value="">Choose class</option>`;
        classArray.forEach(c => {
            options += `<option ${window.localStorage.getItem("manage_class_master_id") == c.class_master_id && "Selected"} value="${c.class_master_id}">${c.class_name}</option>`;
            if (window.localStorage.getItem("manage_class_master_id") == c.class_master_id) isClassSessionPresent = true;
        })
        $("#class_id_main").html(options);

        if (isClassSessionPresent) {
            const is_semester_scheme = "<?php echo $is_semester_scheme; ?>";
            if (is_semester_scheme == 1) {
                getSemesters();
            } else {
                getSubjetsList();
            }
        }
    }

    $("document").ready(_ => {
        const class_master_id = window.location.pathname.split("/").splice(-2)[0];
        const subjectId = window.location.pathname.split("/").splice(-2)[1];

        if (+class_master_id > 0) {
            window.localStorage.setItem("manage_class_master_id", class_master_id);
            window.localStorage.setItem("manage_subject_id", subjectId);

            const subInfo = {
                classMasterId: class_master_id,
                subjectId
            }

            loadClass();
            getLPWeeksList(subInfo);

        }
    })

    let classArray = <?php echo json_encode($classes); ?>;

    if (classArray.length) {
        loadClass();
    } else {
        options += `<option value="">Classes not found</option>`;
        $("#class_id_main").html(options);
    }

    function loadWeeks() {
        const classMasterId = $("#class_id_main").val();
        const subjectId = $("#subject_id_main").val();

        const subInfo = {
            classMasterId,
            subjectId
        }
        getLPWeeksList(subInfo);
    }

    $("#subject_id_main").change(() => {
        loadWeeks();
        const subjectId = $("#subject_id_main").val();
        window.localStorage.setItem("manage_subject_id", subjectId);
    })

    var selected_subject = 0;
    function showHideWeekSession(weekAreaName, weekNo) {
        $(`#${weekAreaName}`).slideToggle()
        let dropdown = $(`#week-${weekNo}-arrow-icon`);
        if(dropdown.hasClass("fa-chevron-down")){
            dropdown.removeClass("fa-chevron-down");
            dropdown.addClass("fa-chevron-up");
        }else{
            dropdown.removeClass("fa-chevron-up");
            dropdown.addClass("fa-chevron-down");
        }
        
        const isArrowDown = $(`#${weekAreaName}-a`).hasClass("arrow-down");
        const arrow = $(`#${weekAreaName}-a`);
        if (isArrowDown) {
            $(`#${weekAreaName}-a`).text("⬆️");
        } else {
            $(`#${weekAreaName}-a`).text("⬇️");
        }
        arrow.toggleClass("arrow-down");
    }

    function getSubjetsList() {
        var class_master_id = $("#class_id_main").val();
        window.localStorage.setItem("manage_class_master_id", class_master_id);

        const is_semester_scheme = "<?php echo $is_semester_scheme; ?>";
        const semester_id = $("#select_semester").val() || 0;
        if (semester_id) {
            window.localStorage.setItem("manage_semester_id", semester_id);
        }

        $.ajax({
            url: '<?php echo site_url('academics/lesson_plan/get_subjects_list') ?>',
            type: 'POST',
            data: { 'class_master_id': class_master_id, "is_semester_scheme": is_semester_scheme, "semester_id": semester_id },
            success: function (data) {
                let subjectSessionPresent = false;
                var resData = $.parseJSON(data);
                output = '<option value="">Choose subject</option>';
                for (var k = 0; k < resData.length; k++) {
                    output += `<option ${window.localStorage.getItem("manage_subject_id") == resData[k].id && "Selected"}  value=${resData[k].id}>${resData[k].subject_name}</option>`;

                    if (window.localStorage.getItem("manage_subject_id") == resData[k].id) subjectSessionPresent = true;
                }
                $('#subject_id_main').html(output);
                if (subjectSessionPresent) {
                    loadWeeks();
                } else {
                    $(".weeks_section").html(`<div class="no-data-display">Please Select A Subject</div>`);
                }
            },
        });
    }

    function setHeading() {
        const gradeName = $("#class_id_main option:selected").text();
        const subjectName = $("#subject_id_main option:selected").text();

        $("#heading_grade").text(gradeName);
        $("#heading_subject").text(subjectName);

    }

    function hideAllActionBtns() {
        $("#deactive-session").hide();
        $(".btn-div").hide();

        const weekSessions = document.querySelectorAll(".outer-card");
        if (weekSessions?.length) {
            weekSessions.forEach(s => {
                s.querySelector("#move-session-arrows").style = "display:none";
                s.querySelector("#right-part").style = "display:none";
                s.querySelector("#title-desc").removeAttribute("href")
                s.querySelector("#title-desc").removeAttribute("target")
            })
        }
    }

    const isLessonPlanAdmin = "<?php echo $isLessonPlanAdmin; ?>";

    function getLPWeeksList({ classMasterId: class_master_id, subjectId: subject_id }) {
        setHeading();
        $(".weeks_section").html(`<div class="no-data-display">Loading...</div>`);
        $.ajax({
            url: "<?php echo site_url("academics/Lesson_plan/get_lp_weeks_data") ?>",
            type: "POST",
            data: { class_master_id, "lp_subject_id": subject_id },
            success(data) {
                $('.actionsButtons').prop("disabled", false);
                data = JSON.parse(data);
                let lp_weeks_data = data.lp_weeks_data;
                let lp_program_weeks_data = data.lp_program_weeks_data;

                const final_approval_status = +data.final_approval_status;
                $('#activate-deactivate-btn').show();
                if (+isLessonPlanAdmin == 1) {
                    $("#staff_approval_btns").hide();
                    $("#admin_approval_btns").show();

                    // based on the final approval status  we'll insert  the buttons for the Admin
                    let btn;
                    if (+final_approval_status == 0) {
                        btn = `<button class="btn btn-danger actionsButtons" name="freeze-unfreeze-session" id="freeze-session" style="display: ;" onclick="enableOrDisablePlanEdit(${subject_id},2)">
                                Disable Plan
                            </button>`;
                    } else if (+final_approval_status == 1) {
                        btn = `<button class="btn btn-primary actionsButtons" name="freeze-unfreeze-session" id="freeze-session" style="display: ;" onclick="approveOrRejectPlanEdit(${subject_id})">
                                Approve/Reject Plan
                            </button>`;
                    } else if (+final_approval_status == 2) {
                        btn = `<button class="btn btn-success actionsButtons" name="freeze-unfreeze-session" id="freeze-session" style="display: ;" onclick="enableOrDisablePlanEdit(${subject_id},0)">
                                Enable Plan
                            </button>`;
                    }

                    $("#admin_approval_btns").html(btn);
                } else {
                    $("#admin_approval_btns").hide();
                    $("#staff_approval_btns").show();

                    // based on the final approval status  we'll insert  the buttons for the Admin 
                    let btn;
                    if (+final_approval_status == 0) {
                        btn = `<button class="btn btn-primary actionsButtons" name="freeze-unfreeze-session" id="freeze-session" style="display: ;" onclick="sendForFinalApproval(${subject_id},1)">
                                Send for approval
                            </button>`;
                    } else if (+final_approval_status == 1) {
                        btn = `<span class="badge badge-info">Approval in process</span>`;
                    } else if (+final_approval_status == 2) {
                        btn = `<span class="badge badge-info">Approved</span>`;
                    }

                    $("#staff_approval_btns").html(btn);
                }

                $(".weeks_section").html = ``;
                let html = ``
                const lpWeekIds = [];
                if (lp_weeks_data.length) {
                    $(".heading").show();
                    // $("#deactive-session").show();
                    // $("#freeze-session").show();
                    lp_weeks_data.forEach((w, i, arr) => {
                        lpWeekIds.push(w.id);
                        const fromDate = formatDate(w.from_date);
                        const toDate = formatDate(w.to_date);

                        function formatDate(date) {
                            return new Date(date).toLocaleString('en-GB', {
                                month: "long",
                                day: "numeric",
                                timeZone: 'UTC'
                            })
                        }
                        let weekNo = i;
                        weekNo = ++weekNo;
                        let hasSession = false;
                        // <span class="week-arrow arrow-down" id="week-${weekNo}-area-a">⬇️</span> line 342
                        html += `
                        <div class="card cd_border p-4 mb-2">
                            <div class="display-weeks margin-bottom" style="position:relative;">
                            <div class="week week-${weekNo} set-week-width" id="week-${weekNo}" onclick="showHideWeekSession('week-${weekNo}-area', ${weekNo})">
                                <i class="fa fa-chevron-down mr-3" id="week-${weekNo}-arrow-icon" aria-hidden="true" style="margin-bottom: 5px;"></i>
                                <span style="font-size:1.8rem"> ${w.program_week_name[0].toUpperCase()}${w.program_week_name.slice(1)} </span>
                                <span class="schedule-date pl-2" style="opacity: 0.5;font-size: 17px;">[${fromDate} To ${toDate}]</span>
                            </div>
                            <div class="show-topic-session" id="week-${weekNo}-area" style="margin-top:20px;">`

                            lp_program_weeks_data.forEach(s => {
                                if (s.program_week_id != w.id || s.class_master_id != class_master_id || s.lp_subject_id != subject_id) return;

                                hasSession = true;
                                const classId = $("#class_id_main").val();
                                const subjectId = $("#subject_id_main").val();
                                const origin = window.location.origin;
                                const designSessionURL = `${origin}/${origin.split(".").includes("localhost") && "oxygenv2/" || ""}academics/lesson_plan/design_session/${classId}/${subjectId}/${s.lp_lesson_id}/${s.lp_topic_id}/${s.lp_session_id}`;

                                html+=`
                                    <div id="${weekNo}-outer-cards-container">
                                    <div class="outer-card">
                                    <div class="left-part">
                                        <a href="${designSessionURL}" target="_blank" class="title-desc" id="title-desc">
                                        <div class="col-md-10 py-3" style="max-width:30.25rem">
                                            <h4 class="mb-2"><i>${s?.lesson_name}</i></h4>
                                            <h4 class="mb-2"><i>${s?.topic_name?.sub_topic_name}</i></h4>
                                            <h5 class="m-0">${s.session_code}</h5>
                                            <br>
                                            <h5 class="m-0"><button class="badge badge-primary">${s.session_type}</button></h5>
                                        </div>
                                        </a>
                                    </div>
                                    <div class="move-session-arrows" id="move-session-arrows" style=;">
                                        <div class="btn-group btn-group-lg" role="group">
                                            <button type="button" style="display:${i == (arr.length - 1) && 'none'}" onClick="goToPreviousWeek('${s.id}','${s.program_week_id}',${false},${class_master_id},${subject_id},${weekNo})" class="btn btn-info"><i class="fa fa-arrow-down m-0" aria-hidden="true"></i></button>
                                            <button type="button" style="display:${i == 0 && 'none'}" onClick="goToPreviousWeek('${s.id}','${s.program_week_id}',${true},${class_master_id},${subject_id},${weekNo})" class="btn btn-info"><i class="fa fa-arrow-up m-0" aria-hidden="true"></i></button>

                                        </div>
                                    </div>
                                    <div class="right-part" id="right-part">
                                        <div class="three-dots">

                                            <a href="#" tabindex="0"
                                                class="btn btn-icon d-flex align-items-center justify-content-center icon-no-margin"
                                                id="action-menu-toggle" aria-label="" data-toggle="dropdown" role="button"
                                                aria-haspopup="true" aria-expanded="true"
                                                aria-controls="action-menu-8-menu">
                                                <i class="icon fa fa-ellipsis-v fa-fw " title="Options" role="img"
                                                    aria-label="Options" id="" style="font-size:19px;"></i>
                                            </a>

                                            <div class="dropdown-menu menu dropdown-menu-right" id="action-menu-8-menu" data-rel="menu-content" style="font-weight:600" aria-labelledby="action-menu-toggle" role="menu" data-constraint=".course-content" style="will-change: transform; position: absolute; transform: translate3d(-128px, 36px, 0px); top: 0px; left: 0px;" x-placement="bottom-end">
                                                
                                                <a href="${designSessionURL}" target="_blank"
                                                    class="dropdown-item editing_duplicate menu-action cm-edit-action"
                                                    data-action="duplicate" data-sectionreturn="0" role="menuitem"
                                                    tabindex="-1" aria-labelledby="">
                                                    <i style="font-weight:600" class="icon fa fa-cog fa-fw" aria-hidden="true"></i>
                                                    <span class="menu-action-text" id="">Design session</span>
                                                </a>

                                                <div style="cursor:pointer;" class="dropdown-item editing_update menu-action cm-edit-action" data-toggle="modal" data-target="#edit_session" data-lesson_id="${s.lp_lesson_id}" data-topic_id="${s.lp_topic_id}" data-session_name="${s.session_code}" data-class_master_id="${class_master_id}" data-subject_id=${subject_id} data-lp_week_id=${w.id} data-lp_program_weeks_session_id=${s.id} data-lp_session_id=${s.lp_session_id} data-week-no="${weekNo}" data-action="update" role="menuitem" tabindex="-1" aria-labelledby="" onclick="resetForm('Edit'); adjustEditModal('${s?.lesson_name}', '${s?.topic_name?.sub_topic_name}', '${w.program_week_name.toUpperCase()}')">
                                                    <i style="font-weight:600" class="icon fa fa-edit" aria-hidden="true"></i>
                                                    <span class="menu-action-text" id="">Edit session</span>
                                                </div>

                                                <div style="cursor:pointer;" class="dropdown-item editing_delete menu-action cm-edit-action" onClick="deleteLPSession('${s.lp_session_id}','${s.session_code}',${class_master_id},${subject_id},${w.id},${weekNo})" data-action="delete" data-sectionreturn="0" role="menuitem" tabindex="-1" aria-labelledby="">
                                                    <span style="font-weight:600;" class="fa fa-ban"></span>
                                                    <span class="menu-action-text">Deactivate session</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                </div>`
                            })

                            if (!hasSession) {
                                html += `
                                <div id="${weekNo}-outer-cards-container" class="content_box">
                                    <div class="row m-0">
                                        <div class="col-md-10 py-3" style="max-width:30.25rem;">
                                            <h3 class="mb-2"><i>No Session Added</i> </h3>
                                        </div>
                                    </div>
                                </div>`
                            }

                            html += `<div class="btn-div">
                                    <button
                                        data-toggle="modal" data-week-no="${weekNo}" data-target="#add_session" data-class_master_id="${class_master_id}" data-subject_id=${subject_id} data-lp_week_id=${w.id}
                                        class="new_circleShape_res addSession"
                                        onclick="resetForm('Add'); adjustAddModal('${w.program_week_name.toUpperCase()}')"
                                        data-action="open-chooser" data-sectionid="0" data-sectionreturnid="0" id=""
                                        style="margin-left: 8px; background-color: #fe970a; position: absolute;top: -3px;right: 10px;width: 80px;height: 39px;">
                                        <span class="fa fa-plus" aria-hidden="true"></span>
                                    </button>
                                </div>
                            </div>
                            </div>
                        </div>
                        `
                    })
                } else {
                    $("#admin_approval_btns").hide();
                    $("#staff_approval_btns").hide();
                    $("#activate-deactivate-btn").hide();
                    html = `<div class="no-data-display">No Week(s) To Show</div>`;
                }
                $(".weeks_section").html(html);

                // console.log(lpWeekIds)
                window.localStorage.setItem("lpWeekIds", lpWeekIds);

                // diable all the action buttons for 1,2 final approval status
                if (final_approval_status == 1 || final_approval_status == 2) {
                    hideAllActionBtns();
                }
            }
        })
    }

    function adjustEditModal(lessonName, subTopicName, weekName){
        $("#edit_session #weekName").html(`From <b>(${weekName})</b>`);
    }

    function adjustAddModal(weekName){
        $("#add_session #weekName").html(`For <b>${weekName}</b>`);
    }

    function resetForm(form) {
        if(form == 'Edit'){
            var form = $('#edit_session-form');
            form[0].reset();
            form.parsley().reset();
        } else if(form == 'Add'){
            var form = $('#session-form');
            form[0].reset();
            form.parsley().reset();
        }
    }

    async function goToPreviousWeek(sessionId, weekId, toPreviousWeek,classMasterId,subjectId,currentSessionWeekNo) {
        await $.ajax({
            url: "<?php echo site_url("academics/Lesson_plan/go_to_previous_week") ?>",
            type: "POST",
            data: { sessionId, weekId, toPreviousWeek },
            success(data) {
                let parsedData = JSON.parse(data);
                if(parsedData){
                    Swal.fire({
                        icon: "success",
                        title: "Session moved",
                        text: "Session has been moved successufully!",
                    }).then(() => {
                        // loadWeeks();
                        loadNewlyAddedSessions(classMasterId,subjectId,+weekId,currentSessionWeekNo);
                        if(toPreviousWeek==true){
                            loadNewlyAddedSessions(classMasterId,subjectId,+weekId-1,+currentSessionWeekNo-1);
                        }else{
                            loadNewlyAddedSessions(classMasterId,subjectId,+weekId+1,+currentSessionWeekNo+1);
                        }
                    });
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Something went wrong!",
                        text: "Failed to move session!",
                    });
                }
            },
            error: function (error) {
                console.log(error);
                Swal.fire({
                    icon: "error",
                    title: "Something went wrong!",
                    text: "Failed to move session!",
                });
            }
        });
    }

    function addLPSession() {
        const lesson_id = $("#lesson_id").val();
        const topic_id = $("#topic_id").val();
        const session_name = $("#session_name").val();
        if(!lesson_id && !topic_id && !session_name){
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Please fill all the fields!",
            });
            return false;
        }
        if(!lesson_id){
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Please select a lesson!",
            });
            return false;
        }
        if(!topic_id){
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Please select a topic!",
            });
            return false;
        }
        if(!session_name){
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Please enter a session name!",
            });
            return false;
        }
        const { class_master_id, subject_id, lp_week_id } = lessondata;
        const session_replicate_number = $("#session_replicate_number").val();
        const session_type = $("#session_type").val();

        const newSessionData = {
            class_master_id,
            subject_id,
            lesson_id,
            topic_id,
            session_name,
            session_replicate_number,
            session_type,
            lp_week_id,
        }

        $.ajax({
            url: "<?php echo site_url("academics/Lesson_plan/add_lp_session") ?>",
            type: "POST",
            data: newSessionData,
            success(data) {
                let parsedData = JSON.parse(data);
                if(parsedData){
                    Swal.fire({
                        icon: "success",
                        title: "Session created",
                        text: "New session has been created successufully!",
                    }).then(() => {
                        const currentSessionWeekNo=$("#current-week-name").val();
                        $('#add_session').modal('hide');
    
                        $("#lesson_id").val("");
                        $("#topic_id").val("");
                        $("#session_name").val("");
                        $("#session_replicate_number").val(1);
                        // loadWeeks();
                        // Over here we will get only newly created Sessions
                        loadNewlyAddedSessions(class_master_id,subject_id,lp_week_id,currentSessionWeekNo);
                    });
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Something went wrong!",
                        text: "Failed to create new session!",
                    });
                }
            },
            error: function (error) {
                console.log(error);
                Swal.fire({
                    icon: "error",
                    title: "Something went wrong!",
                    text: "Failed to create new session!",
                });
            }
        })
    }

    async function getNewlyAddedSessionsData(classMasterId,subjectId,lpWeekId){
        let response=[];
        await $.ajax({
            url: "<?php echo site_url("academics/Lesson_plan/get_newly_added_lp_weeks_data") ?>",
            type: "POST",
            data: { subjectId, lpWeekId },
            success(data) {
                response = JSON.parse(data);
            }
        });
        return response;
    }

    function createHtmlForNewlyAddedSessions(classMasterId,subjectId,lpWeekId,lp_program_weeks_data,currentSessionWeekNo){
        // designSessionURL
        let html='';
        if(typeof lp_program_weeks_data=="object" && !lp_program_weeks_data?.length) return html;

        lp_program_weeks_data.forEach(s => {
        if (s.program_week_id !=lpWeekId || s.class_master_id != classMasterId || s.lp_subject_id != subjectId) return;

        hasSession = true;
        const classId = $("#class_id_main").val();
        // const subjectId = $("#subject_id_main").val();
        const origin = window.location.origin;
        const designSessionURL = `${origin}/${origin.split(".").includes("localhost") && "oxygenv2/" || ""}academics/lesson_plan/design_session/${classId}/${subjectId}/${s.lp_lesson_id}/${s.lp_topic_id}/${s.lp_session_id}`;

        const totalWeekIds=window.localStorage.getItem("lpWeekIds");
        const totalWeeksCount=totalWeekIds.split(",")?.length;

        html += ` <div class="outer-card">
            <div class="left-part">
                <a href="${designSessionURL}" target="_blank" class="title-desc" id="title-desc">
                <div class="col-md-10 py-3" style="max-width:30.25rem">
                    <h4 class="mb-2"><i>${s?.lesson_name}</i></h4>
                    <h4 class="mb-2"><i>${s?.topic_name?.sub_topic_name}</i></h4>
                    <h5 class="m-0">${s.session_code}</h5>
                    <br>
                    <h5 class="m-0"><button class="badge badge-primary">${s.session_type}</button></h5>
                </div>
                </a>
            </div>
            <div class="move-session-arrows" id="move-session-arrows" style=;">
                <div class="btn-group btn-group-lg" role="group">
                    <button type="button" style="display:${currentSessionWeekNo == totalWeeksCount && 'none'}" onClick="goToPreviousWeek('${s.id}','${s.program_week_id}',${false},${classMasterId},${subjectId},${currentSessionWeekNo})" class="btn btn-info"><i class="fa fa-arrow-down m-0" aria-hidden="true"></i></button>
                    <button type="button" style="display:${currentSessionWeekNo == 1 && 'none'}" onClick="goToPreviousWeek('${s.id}','${s.program_week_id}',${true},${classMasterId},${subjectId},${currentSessionWeekNo})" class="btn btn-info"><i class="fa fa-arrow-up m-0" aria-hidden="true"></i></button>
                </div>
            </div>
            <div class="right-part" id="right-part">
                <div class="three-dots">

                    <a href="#" tabindex="0"
                        class="btn btn-icon d-flex align-items-center justify-content-center icon-no-margin"
                        id="action-menu-toggle" aria-label="" data-toggle="dropdown" role="button"
                        aria-haspopup="true" aria-expanded="true"
                        aria-controls="action-menu-8-menu">
                        <i class="icon fa fa-ellipsis-v fa-fw " title="Options" role="img"
                            aria-label="Options" id="" style="font-size:19px;"></i>
                    </a>

                    <div class="dropdown-menu menu dropdown-menu-right" id="action-menu-8-menu"
                        data-rel="menu-content" style="font-weight:600"
                        aria-labelledby="action-menu-toggle" role="menu"
                        data-constraint=".course-content"
                        style="will-change: transform; position: absolute; transform: translate3d(-128px, 36px, 0px); top: 0px; left: 0px;"
                        x-placement="bottom-end">
                        
                        <a href="${designSessionURL}" target="_blank"
                            class="dropdown-item editing_duplicate menu-action cm-edit-action"
                            data-action="duplicate" data-sectionreturn="0" role="menuitem"
                            tabindex="-1" aria-labelledby="">
                            <i style="font-weight:600" class="icon fa fa-copy fa-fw "
                                aria-hidden="true"></i>
                            <span class="menu-action-text" id="">Design session</span>
                        </a>

                        <div style="cursor:pointer;" class="dropdown-item editing_update menu-action cm-edit-action"
                        data-toggle="modal" data-target="#edit_session" data-lesson_id="${s.lp_lesson_id}" data-topic_id="${s.lp_topic_id}"
                        data-session_name="${s.session_code}" data-class_master_id="${classMasterId}" data-subject_id=${subjectId} data-lp_week_id=${lpWeekId}
                        data-lp_program_weeks_session_id=${s.id} data-lp_session_id=${s.lp_session_id} data-week-no="${currentSessionWeekNo}"
                            data-action="update" role="menuitem" tabindex="-1"
                            aria-labelledby="">
                            <i style="font-weight:600" class="icon fa fa-cog fa-fw "
                                aria-hidden="true"></i>
                            <span class="menu-action-text" id="">Edit session</span>
                        </div>

                        <div style="cursor:pointer;" class="dropdown-item editing_delete menu-action cm-edit-action"
                        onClick="deleteLPSession('${s.lp_session_id}','${s.session_code}',${classMasterId},${subjectId},${lpWeekId},${currentSessionWeekNo})"
                            data-action="delete" data-sectionreturn="0" role="menuitem"
                            tabindex="-1" aria-labelledby="">
                            <span style="font-weight:600;margin-right: 7px;margin-left: 2px;"
                                class="glyphicon glyphicon-ban-circle"></span>
                            <span class="menu-action-text">Deactivate session</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>`
        });
        return html;
    }

    async function loadNewlyAddedSessions(classMasterId,subjectId,lpWeekId,currentSessionWeekNo){
        $(`#${currentSessionWeekNo}-outer-cards-container`).html('<div class="loader"></div>');

        if(!classMasterId || !subjectId || !lpWeekId){
            // return any msg you require
            return null;
        }

        const weeksData= await getNewlyAddedSessionsData(classMasterId,subjectId,lpWeekId);

        if(!weeksData?.length){
            let html = `
                    <div id="${currentSessionWeekNo}-outer-cards-container" class="content_box">
                        <div class="row m-0">
                            <div class="col-md-10 py-3" style="max-width:30.25rem;">
                                <h3 class="mb-2"><i>No Session Added</i> </h3>
                            </div>
                        </div>
                    </div>`;

            $(`#${currentSessionWeekNo}-outer-cards-container`).html(html);
            return null;
        }
        
        const html = createHtmlForNewlyAddedSessions(classMasterId,subjectId,lpWeekId,weeksData,currentSessionWeekNo);
        $(`#${currentSessionWeekNo}-outer-cards-container`).html(html);
    }

    function editLPSession() {
        const editSession = {
            editLessonId: $("#edit_lesson_id").val(),
            editTopicId: $("#edit_topic_id").val(),
            edit_session_name: $("#edit_session_name").val(),
            lp_program_weeks_session_id: sessionEditData.lp_program_weeks_session_id,
            lp_session_id: sessionEditData.lp_session_id
        }
        if(!editSession.editLessonId && !editSession.editTopicId && !editSession.edit_session_name){
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Please fill all the fields!",
            });
            return false;
        }
        if(!editSession.editLessonId){
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Please select a lesson!",
            });
            return false;
        }
        if(!editSession.editTopicId){
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Please select a topic!",
            });
            return false;
        }
        if(!editSession.edit_session_name){
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Please enter a session name!",
            });
            return false;
        }
        
        $.ajax({
            url: "<?php echo site_url('academics/lesson_plan/edit_lp_session') ?>",
            type: "POST",
            data: editSession,
            success(data) {
                let parsedData = JSON.parse(data);
                if(parsedData){
                    Swal.fire({
                        title: "Success!",
                        html: `Session Updated!`,
                        icon: "success",
                        showConfirmButton: false,
                        timer: 1500
                    }).then((result) => {
                        $('#edit_session').modal('hide');
                        $("#edit_lesson_id").val("");
                        $("#edit_topic_id").val("");
                        $("#edit_session_name").val("");
                        // loadWeeks();
                        const classMasterId=$("#edit-class-master-id").val();
                        const subjectId=$("#edit-subject-id").val();
                        const lpWeekId=$("#edit-lp-week_id").val();
                        const currentSessionWeekNo=$("#edit-current-week-no").val();
                        
                        loadNewlyAddedSessions(classMasterId,subjectId,lpWeekId,currentSessionWeekNo);
                    })
                } else {
                    Swal.fire({
                        title: "Something went wrong!",
                        html: `Session Not Updated!`,
                        icon: "error",
                        showConfirmButton: false,
                        timer: 1500
                    })
                }
            },
            error: function (error) {
                console.log(error);
                Swal.fire({
                    title: "Something went wrong!",
                    html: `Session Not Updated!`,
                    icon: "error",
                    showConfirmButton: false,
                    timer: 1500
                })
            }
        })
    }

    function deleteLPSession(lp_session_id, session_name, class_master_id,subject_id,lp_week_id,currentSessionWeekNo) {
        Swal.fire({
            title: "Are you sure?",
            html: `De-Activate <b>${session_name}</b>!`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes",
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: "<?php echo site_url('academics/lesson_plan/delete_lp_session') ?>",
                    type: "POST",
                    data: { lp_session_id },
                    success(data) {
                        let parsedData = JSON.parse(data);
                        if(parsedData==-1){
                            Swal.fire({
                                icon: "error",
                                title: "Oops...",
                                text: "This session contains some data, Please remove the data to deactivate this session!",
                            });
                            return;
                        }
                        if(parsedData){
                            Swal.fire({
                                title: "Success!",
                                html: `Session <b>${session_name}</b> De-Activated!`,
                                icon: "success",
                                showConfirmButton: false,
                                timer: 1500
                            }).then((result) => {
                                // loadWeeks();
                                loadNewlyAddedSessions(class_master_id,subject_id,lp_week_id,currentSessionWeekNo);
                            })
                        } else {
                            Swal.fire({
                                title: "Something went wrong!",
                                html: `Session <b>${session_name}</b> Not De-Activated!`,
                                icon: "error",
                                showConfirmButton: false,
                                timer: 1500
                            })
                        }
                    },
                    error: function (error) {
                        console.log(error);
                        Swal.fire({
                            title: "Something went wrong!",
                            html: `Session <b>${session_name}</b> Not De-Activated!`,
                            icon: "error",
                            showConfirmButton: false,
                            timer: 1500
                        })
                    }
                })
            }
        });
    }

    function getSemesters() {
        const class_id_main = $("#class_id_main").val();
        $.ajax({
            url: "<?php echo site_url('academics/lesson_plan/get_semesters') ?>",
            type: "POST",
            data: { "class_master_id": class_id_main },
            success(data) {
                const semesters = JSON.parse(data);
                if (!semesters?.length) {
                    $("#select_semester").html("<option value=''>No semesters</option>");
                }

                let options = ``;
                semesters.forEach(s => {
                    options += `
                        <option ${window.localStorage.getItem("manage_semester_id") == s.id && "Selected"} value='${s.id}'>${s.sem_name}</option>
                    `;
                });

                $("#select_semester").html(options);
                getSubjetsList();
            }
        });
    }

    async function getDeactiveLpSessions(lpWeekIds) {
        try {
            const classMasterId = $("#class_id_main").val();
            const subjectId = $("#subject_id_main").val();

            let response;
            await $.ajax({
                url: "<?php echo site_url('academics/lesson_plan/get_deactive_lp_sessions') ?>",
                type: "POST",
                data: { lpWeekIds, classMasterId, subjectId },
                success(res) {
                    response = JSON.parse(res);
                }
            });
            return response;
        } catch (err) {
            throw err;
        }
    }

    const sessionStatus = {
        "In-active": 'Activate session',
        "Active": 'Deactivate session',
    }

    const sessionStatusColor = {
        "In-active": 'primary',
        "Active": 'danger',
    }

    function activateLpSession(lpSessionId, sessionName, status) {
        Swal.fire({
            title: "Are you sure?",
            text: `You want to ${sessionStatus[status]} ${sessionName}!`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: `Yes`,
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                status = status == 'Active' ? 'In-active' : 'Active';
                $.ajax({
                    url: "<?php echo site_url('academics/lesson_plan/active_lp_sessions') ?>",
                    type: "POST",
                    data: { lpSessionId, status },
                    success(res) {
                        let parsedData = JSON.parse(res);
                        if (parsedData) {
                            Swal.fire({
                                title: "Success!",
                                html: `Session <b>${sessionName}</b> ${status}d!`,
                                icon: "success",
                                showConfirmButton: false,
                                timer: 1500
                            }).then((result) => {
                                const allActivateSession = document.querySelectorAll(".activate-sessions");
                                // console.log(allActivateSession)
                                if (allActivateSession?.length > 1) {
                                    showDeActiveSessions();
                                } else {
                                    $("#deactiveSessionModal").modal("hide");
                                }
                                const classMasterId = $("#class_id_main").val();
                                const subjectId = $("#subject_id_main").val();
                                getLPWeeksList({ classMasterId, subjectId });
                            })
                        } else {
                            Swal.fire({
                                title: "Something went wrong!",
                                html: `Failed Session <b>${sessionName}</b> Not ${status}d!`,
                                icon: "error",
                                showConfirmButton: false,
                                timer: 1500
                            })
                        }
                    },
                    error: function (error) {
                        console.log(error);
                        Swal.fire({
                            title: "Something went wrong!",
                            html: `Failed Session <b>${sessionName}</b> Not ${status}d!`,
                            icon: "error",
                            showConfirmButton: false,
                            timer: 1500
                        })
                    }
                });
            }
        });
    }

    async function showDeActiveSessions() {
        // get deactive lp sessions
        const lpWeekIds = window.localStorage.getItem("lpWeekIds").split(",");
        // console.log(lpWeekIds);
        try {
            const deactiveSessions = await getDeactiveLpSessions(lpWeekIds);

            if (!deactiveSessions?.length) {
                // handle empty check
                return Swal.fire({
                    icon: "info",
                    title: "Oops...",
                    text: "No De-Activated Session(s) To Show!",
                });
            }

            let html = `
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Session Name</th>
                            <th>Session Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>`;

            deactiveSessions.forEach((s, i) => {
                html += `
                        <tr class="activate-sessions">
                            <td>${++i}</td>
                            <td>${s.session_name}</td>
                            <td>${s.session_status}</td>
                            <td>
                                <button class="btn btn-${sessionStatusColor[s.session_status]}" name="activate-session" id="session-${s.session_id}" onclick="activateLpSession(${s.session_id},'${s.session_name}','${s.session_status}')">${sessionStatus[s.session_status]}</buton>
                            </td>
                        </tr>
                `;
            })

            html += `</tbody>
                        </table>`;

            $(".deactive-session-container").html(html);

            $("#deactiveSessionModal").modal("show");

        } catch (err) {
            console.log(err.message);
        }
    }

    async function updateFinalApprovalStatus(lpSubjectId, final_approval_status) {
        try {
            let response;
            await $.ajax({
                url: "<?php echo site_url('academics/lesson_plan/update_plan_syllabus_final_approval_status') ?>",
                type: "POST",
                data: { "final_approval_status": final_approval_status, "lp_subjects_id": lpSubjectId },
                success: function (res) {
                    response = JSON.parse(res);
                }
            })
            return response;
        } catch (err) {
            throw err;
        }
    }

    function refreshApprovalBtns() {
        const subjectId = window.localStorage.getItem("manage_subject_id");
        const classMasterId = window.localStorage.getItem("manage_class_master_id");

        getLPWeeksList({ classMasterId, subjectId });
    }

    function enableOrDisablePlanEdit(subjectId, approvalStatus) {
        try {
            const approvalStatusLabel = {
                0: "Enabl",
                2: "Disabl",
            }

            Swal.fire({
                title: "Are you sure?",
                text: `${approvalStatusLabel[approvalStatus]}ing editing plan. You won't be able to revert this!`,
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: `Yes, ${approvalStatusLabel[approvalStatus]}e it`,
                reverseButtons: true,
            }).then(async (result) => {
                if (result.isConfirmed) {
                    const response = await updateFinalApprovalStatus(subjectId, approvalStatus);
                    if (response) {
                        $('.actionsButtons').prop("disabled", true);
                        Swal.fire({
                            title: "Success!",
                            text: `Syllabus Plan has been ${approvalStatus == 0 ? "Enabled" : "Disabled"} successfully!`,
                            icon: "success",
                        }).then(() => {
                            refreshApprovalBtns();
                        });
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Something went wrong!",
                        });
                    }
                }
            });
        } catch (err) {
            console.log(err.message);
        }
    }

    function approveOrRejectPlanEdit(subjectId) {
        try {
            Swal.fire({
                title: "Approve or Reject?",
                showDenyButton: true,
                // showCancelButton: true,
                confirmButtonText: "Approve",
                denyButtonText: "Reject",
                focusConfirm:false,
                focusDeny:false,
                // focusCancel:true,
                reverseButtons: true,
            }).then(async (result) => {
                if(result.isDismissed){
                    return;
                }

                let approvalStatus;
                /* Read more about isConfirmed, isDenied below */
                if (result.isConfirmed) {
                    approvalStatus = 2;
                } else if (result.isDenied) {
                    approvalStatus = 0;
                }
                // need final approval status over here
                const response = await updateFinalApprovalStatus(subjectId, approvalStatus);
                if (response) {
                    $('.actionsButtons').prop("disabled", true);
                    Swal.fire({
                        title: "Success!",
                        text: `Syllabus Plan has been ${approvalStatus == 2 ? "Approved" : "Rejected"} successfully!`,
                        icon: "success",
                    }).then(() => {
                        refreshApprovalBtns();
                    });
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!",
                    });
                }
            });
        } catch (err) {
            console.log(err.message);
        }
    }

    function sendForFinalApproval(subjectId, approvalStatus) {
        Swal.fire({
            title: "Sending for Approval?",
            text: "Are you sure you? You won't be able to edit plan hereafter!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, send it!"
        }).then(async (result) => {
            if (result.isConfirmed) {
                const response = await updateFinalApprovalStatus(subjectId, approvalStatus);
                if (response) {
                    $('.actionsButtons').prop("disabled", true);
                    Swal.fire({
                        title: "Success!",
                        text: "Syllabus Plan has been sent for final approval successfully!",
                        icon: "success",
                    }).then(() => {
                        refreshApprovalBtns();
                    });
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!",
                    });
                }
            }
        });
    }
</script>


<style>
.loader {
    border: 6px solid #f3f3f3;
    border-radius: 50%;
    border-top: 6px solid #3498db;
    width: 40px;
    height: 40px;
    -webkit-animation: spin 2s linear infinite; /* Safari */
    animation: spin 2s linear infinite;
}

/* Safari */
@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.grade-subject {
    color: #3498db;
    font-weight: 700;
}
</style>