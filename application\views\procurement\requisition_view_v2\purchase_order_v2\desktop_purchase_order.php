<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2'); ?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/inventory_controller_v2/indent_widgets'); ?>">Indent Approvals and
            Purchase Orders</a>
    </li>
    <li>Purchase Order v2</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="panel-header"
                style="margin: 0px; background: none; border-bottom: 1px solid lightgray; height: 3.7rem;">
                <h3>
                    <a style="" class="back_anchor"
                        href="<?php echo site_url('procurement/inventory_controller_v2/indent_widgets') ?>"
                        class="control-primary">
                        <span class="fa fa-arrow-left"></span>
                    </a>
                    Purchase Order v2
                    <div class="pull-right dropdown create-new-btn"
                        style="padding: 0;border-radius: 5px;color: #fff;font-size: 1rem;height: 3rem;display: flex;align-items: center;">
                        <button class="btn btn-dark dropdown-toggle" type="button" id="createNewDropdown"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
                            style="border: none; font-size: 1rem; display: flex; align-items: center; padding: 0.5rem 1rem;">
                            <svg style="height: 1.3rem;width: 1.3rem; margin-right: 0.5rem;"
                                xmlns="http://www.w3.org/2000/svg" fill="currentColor" class="bi bi-plus-lg"
                                viewBox="0 0 16 16">
                                <path fill-rule="evenodd"
                                    d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2" />
                            </svg>
                            Create
                        </button>

                        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="createNewDropdown">
                            <a class="dropdown-item"
                                href="<?php echo site_url('procurement/requisition_controller_v2/create_purchase_order_v2'); ?>">New
                                PO</a>
                            <a class="dropdown-item" href="javascript:void(0);" data-type="indent">PO From Indent</a>
                            <a class="dropdown-item" href="javascript:void(0);" data-type="service_contract">PO From
                                Service Contract</a>
                        </div>
                    </div>
                </h3>
            </div>
        </div>
        <div class="col-md-12">
            <div class="purchase-container">
                <div class="row mb-5">
                    <div class="col-md-2 form-group">
                        <label class="control-label">Date Range</label>
                        <div id="reportrange" class="dtrange" style="width: 100%">
                            <span></span>
                            <input type="hidden" id="from_date">
                            <input type="hidden" id="to_date">
                        </div>
                    </div>

                    <div class="col-md-2 form-group">
                        <label class="control-label">Status</label>
                        <select title="All" class="form-control selectpicker" name="poStatus" id="poStatus" multiple>
                            <option value="Draft">Draft</option>
                            <option value="Requested">Requested</option>
                            <option value="Approved">Approved</option>
                            <option value="Request For Modification">Request For Modification</option>
                            <option value="Rejected">Rejected</option>
                            <option value="Sent To Vendor">Sent To Vendor</option>
                            <option value="Delivered">Delivered</option>
                            <option value="Partially Delivered">Partially Delivered</option>
                            <option value="Completed">Completed</option>
                            <option value="Cancelled">Cancelled</option>
                        </select>
                    </div>

                    <div class="col-md-2 form-group">
                        <label class="control-label">Purchase Orders Type</label>
                        <select title="All" class="form-control selectpicker" name="poType" id="poType">
                            <option value="All" selected>All</option>
                            <option value="associatedToMe">Requires Approval by me</option>
                            <option value="createdByMe">Created by me</option>
                        </select>
                    </div>


                    <div class="col-md-2 d-flex align-items-end" style="height: 4.4rem;">
                        <button id="getAllPurchaseBtn" onclick="getPurchaseOrders()" class="btn btn-dark"
                            style="width: 120px;">
                            <i class="fa fa-file-alt"></i> Get Report
                        </button>
                    </div>
                </div>
                <div class="purchase-details-table" style="overflow: auto;">
                    <!-- purchase details table goes here -->
                    <div style="color:red;text-align:center;
                        color: black;
                        border: 2px solid #fffafa;
                        text-align: center;
                        border-radius: 6px;
                        position: relative;
                        margin-left: 14px;
                        padding: 10px;
                        font-size: 14px;
                        margin-top: 14px;
                        background: #ebf3ff;
                        margin-bottom: 1rem;">
                        Loading...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .breadcrumb {
        background-color: #f8f9fa;
        padding: 0.75rem 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
    }

    /* .breadcrumb li a {
        color: #007bff;
        text-decoration: none;
    } */

    /* .breadcrumb li a:hover {
        text-decoration: underline;
    } */

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    .btn-primary {
        background-color: #007bff;
        border-color: #007bff;
    }

    .btn-primary:hover {
        background-color: #0056b3;
        border-color: #004085;
    }

    .purchase-container {
        background-color: #ffffff;
        /* border: 1px solid #dee2e6; */
        border-radius: 5px;
        padding: 1rem;
    }

    .purchase-details-table {
        margin-top: 1rem;
    }

    .purchase-details-table table {
        border-collapse: collapse;
        width: 100%;
    }

    .purchase-details-table th,
    .purchase-details-table td {
        border: 1px solid #dee2e6;
        padding: 0.75rem;
        text-align: left;
    }

    .purchase-details-table th {
        background-color: #f8f9fa;
    }

    .dataTables_wrapper .dt-buttons {
        float: right;
    }

    .dataTables_filter input {
        background-color: #f2f2f2;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-right: 5px;
    }

    .dataTables_wrapper .dataTables_filter {
        float: right;
        text-align: left;
        width: unset;
    }

    .dt-buttons {
        margin-bottom: 5px;
    }

    /* scrollbar area */
    /* Hide the default scrollbar */
    ::-webkit-scrollbar {
        width: 5px;
        height: 9px;
    }

    /* Create a custom scrollbar */
    ::-webkit-scrollbar-track {
        background-color: #f2f2f2;
        border-radius: 10px;
    }

    /* Create a thumb for the scrollbar */
    ::-webkit-scrollbar-thumb {
        /* background-color: #007bff;  */
        border-radius: 100px;
    }

    /* Make the scrollbar visible when hovering over the track */
    ::-webkit-scrollbar-track-piece-over:hover {
        background-color: #ddd;
    }

    /* Make the scrollbar thumb visible when hovering over it */
    ::-webkit-scrollbar-thumb:hover {
        background-color: #C7C8CC;
    }

    .swal-wide {
        max-width: 600px !important;
        /* Set a proper max width for the modal */
        width: 90% !important;
        /* Ensure responsiveness */
        padding: 20px !important;
        /* Add padding for better spacing */
    }

    .daterangepicker .applyBtn {
        background-color: #343a40 !important;
        border-color: #343a40 !important;
        color: #fff !important;
    }
</style>


<link rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/css/bootstrap-select.min.css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.14.0-beta2/js/bootstrap-select.min.js"></script>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript"
    src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script type="text/javascript">
    $(document).ready(function () {
        // fns to run onload
        getPurchaseOrders();
    });

    $('#reportrange').on('show.daterangepicker', function (ev, picker) {
        picker.container.find('.applyBtn').removeClass('btn-success').addClass('btn-dark');
    });

    const end = moment(); // Today
    const start = moment().subtract(6, 'months'); // 6 months ago

    $("#reportrange").daterangepicker({
        maxDate: end,
        ranges: {
            'Today': [end.clone(), end.clone()],
            'Yesterday': [end.clone().subtract(1, 'days'), end.clone().subtract(1, 'days')],
            'Last 7 Days': [end.clone().subtract(6, 'days'), end.clone()],
            'Last 30 Days': [end.clone().subtract(29, 'days'), end.clone()],
            'Last 6 Months': [start.clone(), end.clone()],
            // 'All Time': [moment("2025-01-01"), end.clone()]
        },
        opens: 'right',
        format: 'DD-MM-YYYY',
        startDate: start,
        endDate: end
    }, function (start, end) {
        $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#from_date').val(start.format('DD-MM-YYYY'));
        $('#to_date').val(end.format('DD-MM-YYYY'));
    });

    // Set initial visible values
    $("#reportrange span").html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));

    function generateMessageHelper(msg = "Loading...") {
        msg = `
  <div style="color:red;text-align:center;
    color: black;
    border: 2px solid #fffafa;
    text-align: center;
    border-radius: 6px;
    position: relative;
    margin-left: 14px;
    padding: 10px;
    font-size: 14px;
    margin-top: 14px;
    background: #ebf3ff;
    margin-bottom: 1rem;">
      ${msg}
    </div>
  `;

        return msg;
    }

    function getPurchaseOrders() {
        $("#getAllPurchaseBtn").text("Please wait...").prop("disabled", true);
        $(".purchase-details-table").html(generateMessageHelper("Loading..."));

        const fromDate = $("#from_date").val();
        const toDate = $("#to_date").val();
        const poStatus = $("#poStatus").val();
        const poType = $("#poType").val();

        $.ajax({
            url: '<?php echo site_url('procurement/requisition_controller_v2/getPurchaseOrders'); ?>',
            type: "post",
            data: { fromDate, toDate, poStatus, poType },
            success: function (data) {
                const purchaseDetails = JSON.parse(data);
                if (!purchaseDetails.length) {
                    // show no-data msg
                    $(".purchase-details-table").html(generateMessageHelper("No data found."));
                } else {
                    // got data
                    let html = `<table class="table table-bordered" id="purchase-details" style="white-space: nowrap;">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Request ID</th>
                                        <th>PO Name</th>
                                        <th>PO Type</th>
                                        <th>Requester</th>
                                        <th>Department</th>
                                        <th>Priority</th>
                                        <th>Status</th>
                                        <th>Vendor</th>
                                        <th>Purchase Order Date</th>
                                        <th>Created By</th>
                                        <th>Created On</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>`;

                    purchaseDetails.forEach((purchase, idx) => {
                        html += ` <tr style="cursor: pointer;">
                                        <td>${idx + 1}</td>
                                        <td>${purchase.request_number}</td>
                                        <td>${purchase.po_name || "N/A"}</td>
                                        <td>${purchase.po_type}</td>
                                        <td>${purchase.requester_name}</td>
                                        <td>${purchase.department_name || "-"}</td>
                                        <td>${purchase.priority}</td>
                                        <td>${purchase.status}</td>
                                        <td>${purchase.vendor_name || "-"}</td>
                                        <td>${purchase.purchase_order_date}</td>
                                        <td>${purchase.created_by_name}</td>
                                        <td>${purchase.created_on}</td>
                                        <td>
                                            <a class="btn btn-dark" target="_black" href="<?php echo site_url('procurement/requisition_controller_v2/view_purchase_order/') ?>${purchase.po_master_id}/${purchase.source_type}/${purchase.source_type_id}">
                                                <i class="fa fa-eye"></i>View Details
                                            </a>
                                        </td>
                                    </tr>`
                    });

                    html += `</tbody>
                    </table>`;

                    $(".purchase-details-table").html(html);

                    $('#purchase-details').DataTable({
                        "language": {
                            "search": "",
                            "searchPlaceholder": "Enter Search..."
                        },
                        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                        "pageLength": 10,
                        "order": false,
                        dom: 'lBfrtip',
                        buttons: [
                            {
                                extend: 'excelHtml5',
                                text: 'Excel',
                                filename: "purchase order",
                                className: 'btn btn-dark'
                            },
                            {
                                extend: 'print',
                                text: 'Print',
                                autoPrint: true,
                                filename: "purchase order",
                                className: 'btn btn-dark'
                            },
                            {
                                extend: 'pdfHtml5',
                                text: 'PDF',
                                filename: "purchase order",
                                className: 'btn btn-dark'
                            }
                        ]
                    });

                    // Add event delegation for onclick on each row
                    // $(".purchase-details-table").on("click", "tr", function () {
                    //     const poMasterId = purchaseDetails[$(this).index()].po_master_id;
                    //     if (poMasterId) {
                    //         const url = `<?php echo site_url('procurement/requisition_controller_v2/view_purchase_order/'); ?>${poMasterId}`;
                    //         window.open(url, '_blank');
                    //     }
                    // });
                }

                $("#getAllPurchaseBtn").text("Get Report").prop("disabled", false);
            }
        });
    }

    $(document).on("click", ".dropdown-item[data-type]", function () {
        const type = $(this).data("type");
        if (!type) {
            return;
        }

        if (type === "indent" || type === "service_contract") {
            const apiUrl = type === "indent"
                ? '<?php echo site_url('procurement/requisition_controller_v2/getAvailableIndentsPoV2'); ?>'
                : '<?php echo site_url('procurement/requisition_controller_v2/getAvailableServiceContractsPoV2'); ?>';

            $.ajax({
                url: apiUrl,
                type: 'GET',
                success: function (response) {
                    const data = JSON.parse(response);

                    if (!data.length) {
                        Swal.fire('No Data', `No ${type === "indent" ? "Indents" : "Service Contracts"} available.`, 'info');
                        return;
                    }

                    // Ensure options are properly formatted
                    const options = {};
                    if (type === "indent") {
                        // options[0] = "Select Indent";
                        data.forEach(item => {
                            if (+item.can_show_indent === 0) return;
                            options[item.id] = `${item.name}`;
                        });
                    } else {
                        // options[0] = "Select Service Contract";
                        data.forEach(item => {
                            options[item.id] = item.name;
                        });
                    }

                    Swal.fire({
                        title: `Create Purchase Order from ${type === "indent" ? "Indent" : "Service Contract"}`,
                        input: 'select',
                        inputOptions: options,
                        inputPlaceholder: `Select an  ${type === "indent" ? "Indent" : "Service Contract"}`,
                        showCancelButton: true,
                        confirmButtonText: 'Create',
                        cancelButtonText: 'Cancel',
                        reverseButtons: true,
                        inputValidator: (value) => {
                            return new Promise((resolve) => {
                                if (value) {
                                    resolve();
                                } else {
                                    resolve('You need to select an option!');
                                }
                            });
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            const selectedId = result.value;
                            const apiUrl = '<?php echo site_url('procurement/requisition_controller_v2/create_purchase_order_v2'); ?>';
                            // const typeParam = type === "indent" ? "indent" : "service_contract";

                            window.location.href = `${apiUrl}?type=${type}&id=${selectedId}`;
                        }
                    });
                },
                error: function () {
                    Swal.fire('Error', 'Failed to fetch data.', 'error');
                }
            });
        }
    });
</script>