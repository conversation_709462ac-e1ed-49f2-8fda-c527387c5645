<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('staff/attendance'); ?>">Staff Attendance</a></li>
    <li>Manage Geo-fences</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">

                <div class="col-md-10">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('staff/attendance') ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Manage Geo-fences
                    </h3>
                </div>

                <div class="col-md-2">
                    <div class="new_circleShape"
                        style="background-color:#fe970a;float:right;display: flex;align-items: center;justify-content: center;">
                        <a class="control-primary" style="cursor:pointer;" data-toggle="modal"
                            data-target="#_add_locations_modal_box">
                            <span class="fa fa-plus" style="line-height:3.2rem"></span>
                        </a>
                    </div>
                </div>

            </div>
        </div>

        <?php $this->load->view("stb/_js_library.php"); ?>
        <?php $this->load->view('staff/self_attendance/_add_locations_modal_box') ?>

        <div class="card-body" style="overflow: auto;">
            <div class="row">
                <div class="container-fluid" id="show_locations">

                </div>
            </div>
            <br>
        </div>
    </div>
    <br>
</div>

<?php $this->load->view("staff/self_attendance/_edit_geofence_modal.php"); ?>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js"
    integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous">
    </script>

<script type="text/javascript">
    $("document").ready(function () {
        get_staff_attendance_locations()
    })

    let msg = `
  <div style="color:red;text-align:center;
    color: black;
    border: 2px solid #fffafa;
    text-align: center;
    border-radius: 6px;
    position: relative;
    margin-left: 14px;
    padding: 10px;
    font-size: 14px;
    margin-top: 14px;
    background: #ebf3ff;">
      No Locations To Show
    </div>
  `;

    function get_staff_attendance_locations() {
        $.ajax({
            url: '<?php echo site_url('staff/attendance/get_staff_attendance_locations'); ?>',
            type: 'post',
            data: {},
            success: function (data) {
                data = JSON.parse(data)
                if (data.length) {
                    let attendanceData = `<table class="table table-bordered">
                    <tr class="bg-light">
                            <th>#</th>
                            <th>Geo-fence Name</th>
                            <th>Latitude</th>
                            <th>Longitude</th>
                            <th>Radius (In meter)</th>
                            <th>Created By</th>
                            <th>Created On</th>
                            <th>Action</th>
                            </tr>`

                    data.forEach((location, i) => {
                        attendanceData += `
                    <tr>
                    <td>${++i}</td>
                    <td>${location.geo_fence_name}</td>
                    <td>${location.latitude}</td>
                    <td>${location.longitude}</td>
                    <td>${location.radius}</td>
                    <td>${location.created_by}</td>
                    <td>${location.date}</td>
                    <td>
                        <div>
                            <button class="btn btn-warning" data-toggle="modal" data-target="#edit_geofence" 
                            data-fence_id="${location.id}"
                            data-geo_fence_name="${location.geo_fence_name}"
                            data-latitude="${location.latitude}"
                            data-longitude="${location.longitude}"
                            data-radius="${location.radius}"
                            >Edit</button>
                            <button class="btn btn-danger" onClick="deleteGeofence('${location.id}','${location.geo_fence_name}')">Delete</button>
                        </div>
                    </td>
                    </tr>
                    `
                    })
                    attendanceData += `</table>`
                    $("#show_locations").html(attendanceData)
                } else {
                    $("#show_locations").html(msg)
                }
            }
        });
    }

    function deleteGeofence(fenceId, geofenceName) {
        Swal.fire({
            title: `Delete Geo-fence`,
            html: `<p><br><strong>${geofenceName}</strong> <br> Geo-fence will be removed. <br> Still want to continue?</p>`,
            confirmButtonText: 'Confirm',
            showCancelButton: true,
            showLoaderOnConfirm: true
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: "<?php echo site_url('staff/attendance/delete_geofence') ?>",
                    type: "POST",
                    data: { fenceId },
                    success(data) {
                        get_staff_attendance_locations();
                        // Add SweetAlert for success
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            html: `Removed <b>${geofenceName}</b> Geo-fence successfully`
                        });
                    }
                })
            }
        });
    }
</script>