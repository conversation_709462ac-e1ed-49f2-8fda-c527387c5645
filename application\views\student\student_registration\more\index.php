<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/student_menu');?>">Student Menu</a></li>
    <li><a href="<?php echo site_url('student/Student_controller/index/');?>">Student Index</a></li>
    <li>Student Detail</li>
</ul>

<hr>
<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('student/Student_controller/index/'); ?>">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Details of
                        <?php echo '<strong>' . ($name_to_caps?strtoupper($stdData->stdName):($stdData->stdName)) . '</strong> (Class: ' . $stdData->className . ' / Section: '.$stdData->sectionName. ') (Admission No: '. $stdData->admission_no.')'?>
                    </h3>
                <a style="text-decoration:none; margin-left: 24px;" href="#"
                        onclick="popup_siblings_sw(<?php echo $stdData->id ?>); return false;"><span
                            class="btn btn-info">Siblings</span></a>
                    <ul class="panel-controls" style="float:right">
                        <li>
                            <form enctype="multipart/form-data" method="post" id="searchAdm"
                                action="<?php echo site_url('student/Student_controller/searchby_adm_no/'.$student_uid);?>"
                                data-parsley-validate="" class="form-horizontal">
                                <input id="admission_no" autocomplete="off" placeholder="Search by Admission No"
                                    class="form-control input-md" name="admission_no">
                            </form>
                        </li>
                        <li><a href="<?php echo site_url('student/Student_controller/wander/prev/') . $stdData->id;?>"
                                data-placement="top" data-toggle="tooltip" data-original-title="Previous"
                                class="control-primary"><span class="fa fa-caret-square-o-left"></span></a></li>
                        <li><a href="<?php echo site_url('student/Student_controller/wander/next/') . $stdData->id;?>"
                                data-placement="top" data-toggle="tooltip" data-original-title="Next"
                                class="control-primary"><span class="fa fa-caret-square-o-right"></span></a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body pt-1">
            <?php 
        $data['tile_list'] = $student_data;
        $data['heading'] = 'Manage Student Data';
        echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>

            <?php 
        $data['tile_list'] = $onboarding;
        $data['heading'] = 'Onboarding and Credentials';
        echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>

            <?php 
        $data['tile_list'] = $reports;
        $data['heading'] = 'Reports';
        echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>

            <?php 
        $data['tile_list'] = $others;
        $data['heading'] = 'Others';
        echo $this->load->view('commons/secondary_dashboard_tiles.php', $data, true); 
      ?>



        </div>
    </div>
</div>



<!-- <div class="col-md-12">
<div class="panel panel-default new-panel-style_3" style="border-radius:8px;">
    <div class="panel-heading new-panel-heading" style="border-radius:8px;">
        <h3 class="panel-title">Details of <?php // echo '<strong>' . $stdData->stdName . '</strong> (Class: ' . $stdData->className . ' / Section: '.$stdData->sectionName. ') (Admission No: '. $stdData->admission_no.')'?></h3>
        <ul class="panel-controls" style="float:right">
            <li> 
            <form enctype="multipart/form-data" method="post" id="searchAdm" action="<?php // echo site_url('student/Student_controller/searchby_adm_no/'.$student_uid);?>" data-parsley-validate="" class="form-horizontal">
              <input id="admission_no" autocomplete="off" placeholder="Search by Admission No" class="form-control input-md" name="admission_no">
            </form>
            </li>
            <li><a href="<?php // echo site_url('student/Student_controller/wander/prev/') . $stdData->id;?>" data-placement="top" data-toggle="tooltip" data-original-title="Previous" class="control-primary"><span class="fa fa-caret-square-o-left"></span></a></li>
            <li><a href="<?php // echo site_url('student/Student_controller/wander/next/') . $stdData->id;?>" data-placement="top" data-toggle="tooltip" data-original-title="Next" class="control-primary"><span class="fa fa-caret-square-o-right"></span></a></li>
            <li><a href="<?php // echo site_url('student/Student_controller/index/'.$stdData->csId);?>" data-placement="top" data-toggle="tooltip" data-original-title="Back" class="control-primary"><span class="fa fa-mail-reply"></span></a></li>
        </ul>
    </div>
</div>
</div>
<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="panel-body"> -->

<!-- START WIDGET REGISTRED -->
<!-- <?php if ($permitAddressCRUD) : ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('student/Student_controller/addStudentAddress/'.$student_uid .'/'.$stdData->csId) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/address.png">
                            </div>                             
                            <div class="widget-data">
                                <div class="widget-title">Address</div>
                                <div class="widget-subtitle">Manage all addresses of the student</div>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endif ?>


            <?php if ($permitCommunicationCRUD) : ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('student/Student_controller/edit_communication/'.$student_uid .'/'.$stdData->csId) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                                <div class="widget-item-left">
                                    <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/communication.png">
                                </div>
                                <div class="widget-data">
                                    <div class="widget-title">Phone Numbers</div>
                                    <div class="widget-subtitle">Edit Phone numbers of the student</div>
                                </div>                         
                        </div>
                    </a>
                </div>
            <?php endif ?>


            <?php if ($permitHealthCRUD) : ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('student/Health_controller/addStudentHealth/'.$student_uid .'/'.$stdData->csId) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/health.png">
                            </div>
                            <div class="widget-data">
                                <div class="widget-title">Health</div>
                                <div class="widget-subtitle">Manage Health record</div>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endif ?>

            <?php if ($permitMotherTongueCRUD) : ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('student/Student_controller/addMotherTongue/'.$student_uid .'/'.$stdData->csId) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                                <div class="widget-item-left">
                                    <img class="img-responsive" src="<?php echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/mothertongue.png">
                                </div>
                                <div class="widget-data">
                                    <div class="widget-title">Mother Tongue</div>
                                    <div class="widget-subtitle">Edit mother tongues of the student</div>
                                </div>
                        </div>                            
                    </a>
                </div>
            <?php endif ?>

            <?php if ($permitObservationSummary) : ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('Observation_controller/observations/'.$student_uid) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                                <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/observerep.png">
                            </div>
                            <div class="widget-data">
                                <div class="widget-title">Observations</div>
                                <div class="widget-subtitle">View/Print Student Observations</div>
                            </div>                         
                        </div>                            
                    </a>
                </div>
            <?php endif ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('student/Health_controller/health_report/'.$student_uid .'/'.$stdData->csId) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/health.png">
                            </div>
                            <div class="widget-data">
                                <div class="widget-title">Health Report</div>
                                <div class="widget-subtitle">View Health Report</div>
                            </div>
                        </div>
                    </a>
                </div>
            <?php if ($permitFullEdit) : ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('student/Student_controller/edit_student/'.$student_uid.'/0') ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/updatedata.png">
                            </div>
                            <div class="widget-data">
                                <div class="widget-title">Update Data</div>
                                <div class="widget-subtitle">Edit all Student data</div>
                            </div>                         
                        </div>                            
                    </a>
                </div>
            <?php endif ?>

            <?php if ($permitTransportEdit) : ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('feesv2/fees_transport/transport/'.$student_uid) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/transportreport.png">
                            </div>
                            <div class="widget-data">
                                <div class="widget-title">Transport</div>
                                <div class="widget-subtitle">Enter Transport details for the student</div>
                            </div>                         
                        </div>                            
                    </a>
                </div>
            <?php endif ?>

            <?php if ($permitStudentProfile) : ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('student/Student_profile_controller/index/'.$student_uid) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/profile.png">
                            </div>
                            <div class="widget-data">
                                <div class="widget-title">Profile</div>
                                <div class="widget-subtitle">View complete profile of the student</div>
                            </div>                         
                        </div>                            
                    </a>
                </div>
            <?php endif ?>

            <?php if ($permitGuardianEdit) : ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('student/Student_controller/guardian_data/'.$student_uid) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/profile.png">
                            </div>
                            <div class="widget-data">
                                <div class="widget-title">Guardian Data</div>
                                <div class="widget-subtitle">View/Edit Guardian Data</div>
                            </div>                         
                        </div>                            
                    </a>
                </div>
            <?php endif ?>
         
            <?php if ($permitPrintCert) : ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('student/Certificates_controller/print_certificates/'.$student_uid) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/printcertificate.png">
                            </div>
                            <div class="widget-data">
                                <div class="widget-title">Issue Certificate(s)</div>
                                <div class="widget-subtitle">Issue/print certificates for <?= $stdData->stdName ?></div>
                            </div>
                        </div>                            
                    </a>
                </div>
            <?php endif ?>

            <?php if ($permit_parents_login) : ?>
                <div class="col-md-4">
                    <a  href="<?php // echo site_url('student/Student_controller/changeusername/'.$student_uid) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <span class="fa  fa-user-md" style="font-size:48px;"></span>
                            </div>                             
                            <div class="widget-data">
                                <div class="widget-title">Provision User-login</div>
                                <div class="widget-subtitle">Provision logins for father and mother</div>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endif ?>

            <?php if ($permitAccessNum) : ?>
            <div class="col-md-4">
                <a href="#">
                    <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/profile.png">
                            </div>         
                            <div class="widget-data">
                                <div class="widget-title"> Identification Code</div>
                                <div class="widget-subtitle"><?=$stdData->identification_code ?></div> -->
<!-- <div class="widget-subtitle">C</div> -->
<!--  <a class="btn btn-info" href="<?php // echo site_url('library_controller/generate_student_qr_code_view/'.$stdData->identification_code); ?>">  
                                    <div style="color: #fff" class="widget-subtitle">Capture Image</div>
                                </a> -->
<!-- </div>   
                        </div>                            
                    </a>
                </div>
            <?php endif ?>

            <?php if ($permit_selected_lang) : ?>
            <div class="col-md-4">
                <a href="<?php // echo site_url('student/student_controller/selected_lang/'.$student_uid) ?>">
                    <div class="widget widget-default widget-item-icon new_height">
                        <div class="widget-item-left">
                            <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/lang.png">
                        </div>         
                        <div class="widget-data">
                            <div class="widget-title">Selected Language</div>
                            <div class="widget-subtitle">Select language taken in Primary</div>
                        </div>   
                    </div>                            
                </a>
            </div>
            <?php endif ?>

            <?php if ($permitSmsReport) : ?>
            <div class="col-md-4">
                <a href="<?php // echo site_url('sms/studentSmsReport/'.$student_uid) ?>">
                    <div class="widget widget-default widget-item-icon new_height">
                        <div class="widget-item-left">
                            <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/smsreport.png">
                        </div>         
                        <div class="widget-data">
                            <div class="widget-title">SMS Report</div>
                            <div class="widget-subtitle">View SMS sent for this student</div>
                        </div>   
                    </div>                            
                </a>
            </div>
            <?php endif ?>

            <?php if ($permit_weight_height) : ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('student/student_controller/running_height_weight/'.$student_uid) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/subs.png">
                            </div>         
                            <div class="widget-data">
                                <div class="widget-title">Running height & weight</div>
                                <div class="widget-subtitle">Manage running height and weight of the student</div>
                            </div>   
                        </div>                            
                    </a>
                </div>
            <?php endif ?>

            <?php if ($permit_school_details) : ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('student/student_controller/add_school_details/'.$student_uid) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/subs.png">
                            </div>         
                            <div class="widget-data">
                                <div class="widget-title">Previous Schooling information</div>
                                <div class="widget-subtitle">Student previous school details</div>
                            </div>   
                        </div>                            
                    </a>
                </div>
            <?php endif ?>

             <?php if ($permit_documents) : ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('student/student_controller/upload_documents/'.$student_uid) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/subs.png">
                            </div>         
                            <div class="widget-data">
                                <div class="widget-title">Documents</div>
                                <div class="widget-subtitle">Upload documents</div>
                            </div>   
                        </div>                            
                    </a>
                </div>
            <?php endif ?>

            <?php if ($permit_rfid_mapping) : ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('student/student_controller/map_rfid/'.$student_uid) ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/subs.png">
                            </div>         
                            <div class="widget-data">
                                <div class="widget-title">Map RFID</div>
                                <div class="widget-subtitle">Id: <strong><?=$stdData->rfid_number ?></strong></div>
                            </div>   
                        </div>                            
                    </a>
                </div>
            <?php endif ?>

        <?php if ($permitPromotion) : ?>
            <div class="col-md-4">
                <a href="<?php // echo site_url('student/student_controller/promote_student/'.$student_uid) ?>">
                    <div class="widget widget-default widget-item-icon new_height">
                        <div class="widget-item-left">
                            <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/smsreport.png">
                        </div>
                        <div class="widget-data">
                            <div class="widget-title">Promote Student</div>
                            <div class="widget-subtitle" style="color:#8c8c8c;">
                                Promote student. <br>Current Status: 
                                <?php if (!$canPromote) {
                                    echo '<strong>Promotion Disabled</strong>';
                                } else {
                                    if ($isStudentPromoted) {
                                        echo '<strong>Promoted</strong>';         
                                    } else {
                                        echo '<strong>Studying</strong>';
                                    }
                                }
                                ?>
                            </div>
                        </div>   
                    </div>                            
                </a>
            </div>
            <?php endif ?>

            <?php if ($permit_exit_student) { ?>
                <div class="col-md-4">
                    <a href="<?php // echo site_url('student/student_exit/index/').$student_uid ?>">
                        <div class="widget widget-default widget-item-icon new_height">
                            <div class="widget-item-left">
                                <img class="img-responsive" src="<?php // echo $this->config->item('s3_base_url') ?>/nextelement-common/Staff and Admin icons 48px/exit.png">
                            </div>         
                            <div class="widget-data">
                                <div class="widget-title">Terminate Student</div>
                                <div class="widget-subtitle">Terminate Student</div>
                            </div>   
                        </div>                            
                    </a>
                </div>
            <?php } ?> -->

<!--  </div> //Panel body
    </div>//Panel Default
</div>//Col  -->
<!-- END DATATABLE EXPORT -->
<div class="visible-xs">
    <a href="<?php echo site_url('student/Student_controller/index/');?>" id="backBtn" onclick="loader()"><span
            class="fa fa-mail-reply"></span></a>
</div>


<script type="text/javascript">
function popup_siblings_sw(stdId) {
    $.ajax({
        url: '<?php echo site_url('student/Student_controller/get_siblings_data'); ?>',
        type: 'post',
        data: {
            'stdId': stdId
        },
        success: function(data) {
            var sibling = JSON.parse(data);
            if (data && sibling && sibling.length > 1) {
                var html = '';
                // Find primary account student id
                var primaryStudentId = null;
                for (var i = 0; i < sibling.length; i++) {
                    if (sibling[i].user_id == sibling[i].old_user_id) {
                        primaryStudentId = sibling[i].sa_id;
                        break;
                    }
                }
                // Add the Send Credentials link at the top if found
                if (primaryStudentId) {
                    html += '<div style="text-align:right; margin-bottom:10px;">' +
                        '<a href="<?php echo site_url('parent_activation/student_provisionbyid/') ?>' + primaryStudentId + '" ' +
                        'id="sendCredentialsHeaderLink" style="font-size: 16px; color: #007bff; text-decoration: underline; cursor: pointer;" target="_blank">' +
                        'Send Credentials</a></div>';
                }
                html += sibling_construct_data(sibling);
                Swal.fire({
                    title: 'Siblings',
                    html: html,
                    width: 700,
                    customClass: {
                        popup: 'swal-siblings-popup'
                    },
                    showCloseButton: true,
                    confirmButtonText: 'Close'
                });
            } else {
                Swal.fire({
                    title: 'Siblings',
                    html: '<div style="text-align:center; font-size:16px; color:#888;">No siblings found.</div>',
                    width: 400,
                    showCloseButton: true,
                    confirmButtonText: 'Close'
                });
            }
        }
    });
}

function sibling_construct_data(sibling) {
    var html = '';
    html += '<table class="table table-bordered">';
    html += '<thead style="background:#f5f5f5;">';
    html += '<tr>';
    html += '<td>Student Name</td>';
    html += '<td>Class/Section</td>';
    html += '<td>Account Type</td>';
    html += '</tr>';
    html += '</thead>';
    html += '<tbody>';
    for (var i = 0; i < sibling.length; i++) {
        var url = '<?php echo site_url('student/Student_controller/addMoreStudentInfo/') ?>' + sibling[i].sa_id;
        html += '<tr>';
        html += '<td><a target="_blank" href="' + url + '">' + sibling[i].sName + '</a> </td>';
        if (sibling[i].status_label == 'Alumini' || sibling[i].status_label == 'Not Promoted') {
            html += '<td>' + sibling[i].status_label + ' ( ' + sibling[i].admission_no + ' ) ' + '</td>';
        } else {
            html += '<td>' + sibling[i].csName + '</td>';
        }
        if (sibling[i].user_id == sibling[i].old_user_id) {
            html += '<td> Primary Account </td>';
        } else {
            html += '<td> Linked </td>';
        }
        html += '</tr>';
    }
    html += '</tbody>';
    html += '</table>';
    return html;
}

function exitStudent(stdId) {
    var stdName = '<?php echo ($name_to_caps?strtoupper($stdData->stdName):($stdData->stdName)); ?>';
    bootbox.confirm({
        title: "Info",
        message: "You are making <b>" + stdName + "</b> as Alumni. Are you sure?",
        buttons: {
            confirm: {
                label: 'Yes',
                className: 'btn-success'
            },
            cancel: {
                label: 'No',
                className: 'btn-danger'
            }
        },
        callback: function(result) {
            if (result) {
                $.ajax({
                    url: '<?php echo site_url('student/Student_controller/exit_student'); ?>',
                    data: {
                        'stdId': stdId
                    },
                    type: "post",
                    success: function(data) {
                        if (data == -1) {
                            $(function() {
                                new PNotify({
                                    title: 'Error',
                                    text: 'Something went wrong.',
                                    type: 'error',
                                });
                            });
                        } else if (data == 0) {
                            $(function() {
                                new PNotify({
                                    title: 'Success',
                                    text: 'Student exited successfully.',
                                    type: 'success',
                                });
                            });
                            location.reload();
                        } else if (data == 1) {
                            bootbox.alert({
                                title: "Student Exit",
                                message: "Student has siblings, please contact admin to exit student properly.",
                                buttons: {
                                    ok: {
                                        label: 'Got it',
                                        className: 'btn-success'
                                    }
                                },
                                callback: function() {
                                    location.reload();
                                }
                            });
                        }
                    }
                });
            }
        }
    });
}
</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<style>
.widget {
    box-shadow: 0 0px 8px 1px rgba(136, 136, 136, 0.3);
    border-radius: 8px;

}

.new_height {
    min-height: 40px;
    padding: 10px 6px !important;
}

.widget.widget-item-icon .widget-item-left,
.widget.widget-item-icon .widget-item-right {
    width: 70px;
    padding: 10px 3px;
    text-align: center;
}

.widget .widget-title {
    padding-top: 7px;
}

ul.panel-controls>li>a {
    border-radius: 50%;
}

.swal-siblings-popup .table {
    margin-bottom: 0;
}

.swal-siblings-popup th,
.swal-siblings-popup td {
    padding: 6px 10px;
}

.swal2-close {
    display: none !important;
}
</style>