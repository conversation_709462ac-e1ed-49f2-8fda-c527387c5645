<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('SchoolAdmin_menu');?>">Manage User</a></li>
    <li>Parent Provision Report</li>
</ul>

<div class="col-md-12 col_new_padding">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-9 pl-0">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('SchoolAdmin_menu') ?>"
                            class="control-primary">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Parent Provision Report
                    </h3>
                </div>

            </div>
        </div>

        <div class="card-body pt-1">
            <form id="viewFilterForm" class="form-horizontal">
                <div class="form-group col-md-2">
                    <label for="viewType" class="control-label">View By</label>
                    <select id="viewType" class="form-control input-md" required>
                        <option value="class">Class</option>
                        <option value="class_section">Class-Section</option>
                    </select>
                </div>

                <div class="form-group col-md-2">
                    <label for="parentType" class="control-label">Parent Type</label>
                    <select id="parentType" class="form-control input-md" required>
                        <option value="both">Both Parent</option>
                        <option value="either">Either Parent</option>
                    </select>
                </div>

                <div class="form-group col-md-2 pt-2">
                    <input type="button" value="Get" onclick="loadData()" class="btn btn-primary mt-4" />
                </div>
            </form>
        </div>
        <div class="card-body" style="padding-top: 0rem;">
            <div id="report_data">
                <div class="no-data-display">Select Filter To Get Report</div>
            </div>
            <table class="table table-bordered" id="provision_report_table" style="display: none">
                <thead>
                    <tr>
                        <th>Class</th>
                        <th>Total</th>
                        <th>Activated (Logged In)</th>
                        <th>Activated (Not logged In)</th>
                        <th>Not Activated</th> 
                        <th>Notification token registered</th>
                    </tr>
                </thead>
                <tbody id="provision_report_body"></tbody>
            </table>
        </div>
    </div>
</div>
<script>
    // $(document).ready(function () {
	// 	loadData();
	// });

    function loadData() {
        const classType = $('#viewType').val();
	    const parentType = $('#parentType').val();
		$.ajax({
			url: '<?php echo site_url('user_provisioning_controller/getProvisionReport'); ?>',
			type: 'post',
			dataType: 'json',
            data: {
                classtype: classType,
                parenttype: parentType
            },
			success: function(data) {
				renderConfigTable(data);
			},
			error: function(err) {
				console.error('Error fetching config data:', err);
				$('#list_tab tbody').html('<tr><td colspan="8">Failed to load data.</td></tr>');
			}
		});
	}

    function renderConfigTable(data) {
        const report = data.clsReport;
        const totalReport = data.totalReport;
        let html = '';
        Object.keys(report).forEach(cls => {
            const row = report[cls];
            const total = row.total || 0;
            const notActivated = row.not_active || 0;
            const activated = row.active || 0;
            const loggedIn = row.logged_in_once || 0;
            const token = row.token_registered || 0;

            const activatedNotLoggedIn = activated - loggedIn;

            html += `
                <tr>
                    <td>${cls}</td>
                    <td>${total}</td>
                    <td>${loggedIn}</td>
                    <td>${activatedNotLoggedIn}</td>
                    <td>${notActivated}</td>
                    <td>${token}</td>
                </tr>
            `;
        });

        const grandTotal = totalReport.total || 0;
        const totalNotActive = totalReport.not_active || 0;
        const totalActive = totalReport.active || 0;
        const totalLoggedIn = totalReport.logged_in_once || 0;
        const totalToken = totalReport.token_registered || 0;
        const totalActiveNotLoggedIn = totalActive - totalLoggedIn;

        html += `
            <tr style="font-weight:bold;">
                <td>Total</td>
                <td>${grandTotal}</td>
                <td>${totalLoggedIn} (${((totalLoggedIn / grandTotal) * 100).toFixed(1)}%)</td>
                <td>${totalActiveNotLoggedIn} (${((totalActiveNotLoggedIn / grandTotal) * 100).toFixed(1)}%)</td>
                <td>${totalNotActive} (${((totalNotActive / grandTotal) * 100).toFixed(1)}%)</td>
                <td>${totalToken} (${((totalToken / grandTotal) * 100).toFixed(1)}%)</td>
            </tr>
        `;
        $("#report_data").css('display', 'none');
        $("#provision_report_table").css('display', '');
        $('#provision_report_body').html(html);
    }

</script>