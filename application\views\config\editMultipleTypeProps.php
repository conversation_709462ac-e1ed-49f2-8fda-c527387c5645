<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard') ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('config') ?>">Config Management</a></li>
    <li><a href="#">Module Management</a></li>
</ul>

<div class="col-md-12">
    <div class="card cd_border" style="padding-bottom:2px;">
		<div class="card-header panel_heading_new_style_staff_border_v2">
			<div class="row align-items-center" style="margin: 0px;">
				<div class="col-md-4 d-flex align-items-center">
					<h3 class="card-title panel_title_new_style_staff mb-2 mr-3">
						<a class="back_anchor" href="<?php echo site_url('config');?>">
							<span class="fa fa-arrow-left"></span>
						</a> 
						Edit Options for Config <?= $configName ?>
					</h3>
				</div>

				<div class="col-md-8 text-right d-flex align-items-center justify-content-end">
					<div style="margin-right: 12px;font-size: medium;">
						Login: <b><?php echo ucfirst($name); ?></b>
					</div>
					<button class="btn btn-primary" onclick="logout()">Logout</button>
				</div>
			</div>
		</div>

        <div class="card-body">
            <form enctype="multipart/form-data" method="post" id="demo-form" action="<?= site_url('config/append'); ?>" data-parsley-validate class="form-horizontal">
                <div class="card shadow-sm mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Edit Options for Config: <strong><?= $configName ?></strong></h5>
                    </div>
                    <div class="card-body">
                        <input type='hidden' name='name' value='<?= $configName ?>'>
                        <input type='hidden' name='person_name' value='<?= $name ?>'>
                        <input type='hidden' name='email' value='<?= $email ?>'>
                        <div class="row">
                            <div class="col-md-5">
                                <label for="multi_d" class="form-label fw-bold">Available Modules</label>
                                <select id="multi_d" class="form-control select2" size="10" multiple>
                                    <?php foreach ($options as $mod): ?>
                                        <option value="<?= $mod ?>"><?= $mod ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-2 d-flex flex-column justify-content-center align-items-center">
                                <button type="button" id="multi_d_rightAll" class="btn btn-outline-secondary mb-2" title="Select All">
                                    <i class="fa fa-angle-double-right"></i>
                                </button>
                                <button type="button" id="multi_d_rightSelected" class="btn btn-outline-secondary mb-2" title="Select">
                                    <i class="fa fa-angle-right"></i>
                                </button>
                                <button type="button" id="multi_d_leftSelected" class="btn btn-outline-secondary mb-2" title="Deselect">
                                    <i class="fa fa-angle-left"></i>
                                </button>
                                <button type="button" id="multi_d_leftAll" class="btn btn-outline-secondary mb-2" title="Remove All">
                                    <i class="fa fa-angle-double-left"></i>
                                </button>
                            </div>

                            <div class="col-md-5">
                                <label for="multi_users_to_2" class="form-label fw-bold">Enabled Modules</label>
                                <select name="new_modules[]" id="multi_users_to_2" class="form-control" size="10" multiple data-parsley-mincheck="1" data-parsley-mincheck-message="Please select at least one enabled module.">
                                    <?php if (!empty($enabledOptions)): ?>
                                        <?php foreach ($enabledOptions as $value): ?>
                                            <option value="<?= $value ?>" selected><?= $value ?></option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel-footer text-center">
                    <button type="submit" class="btn btn-primary">Submit</button>
                    <a class="btn btn-danger" href="<?= site_url('config') ?>">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(function () {
        $('#multi_d').multiselect({
            right: '#multi_d_to, #multi_users_to_2',
            rightSelected: '#multi_d_rightSelected',
            leftSelected: '#multi_d_leftSelected',
            rightAll: '#multi_d_rightAll',
            leftAll: '#multi_d_leftAll',
            moveToRight: function (Multiselect, options, event, silent, skipStack) {
                var button = $(event.currentTarget).attr('id');
                if (button == 'multi_d_rightSelected') {
                    var left_options = Multiselect.left.find('option:selected');
                    Multiselect.right.eq(0).append(left_options);
                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.right.eq(0).find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.right.eq(0));
                    }
                } else if (button == 'multi_d_rightAll') {
                    var left_options = Multiselect.left.find('option');
                    Multiselect.right.eq(0).append(left_options);

                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.right.eq(0).find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.right.eq(0));
                    }
                }
            },

            moveToLeft: function (Multiselect, options, event, silent, skipStack) {
                var button = $(event.currentTarget).attr('id');

                if (button == 'multi_d_leftSelected') {
                    var right_options = Multiselect.right.eq(0).find('option:selected');
                    Multiselect.left.append(right_options);

                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.left.find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.left);
                    }
                } else if (button == 'multi_d_leftAll') {
                    var right_options = Multiselect.right.eq(0).find('option');
                    Multiselect.left.append(right_options);

                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.left.find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.left);
                    }
                }
            }
        });
    });
</script>

<script type="text/javascript">
    $(function () {
        $('#multi_permission').multiselect({
            right: '#multi_d_to, #multi_permission_to_2',
            rightSelected: '#multi_pem_rightSelected',
            leftSelected: '#multi_perm_leftSelected',
            rightAll: '#multi_perm_rightAll',
            leftAll: '#multi_perm_leftAll',
            moveToRight: function (Multiselect, options, event, silent, skipStack) {
                var button = $(event.currentTarget).attr('id');
                if (button == 'multi_pem_rightSelected') {
                    var left_options = Multiselect.left.find('option:selected');
                    Multiselect.right.eq(0).append(left_options);
                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.right.eq(0).find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.right.eq(0));
                    }
                } else if (button == 'multi_perm_rightAll') {
                    var left_options = Multiselect.left.find('option');
                    Multiselect.right.eq(0).append(left_options);

                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.right.eq(0).find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.right.eq(0));
                    }
                }
            },

            moveToLeft: function (Multiselect, options, event, silent, skipStack) {
                var button = $(event.currentTarget).attr('id');

                if (button == 'multi_perm_leftSelected') {
                    var right_options = Multiselect.right.eq(0).find('option:selected');
                    Multiselect.left.append(right_options);

                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.left.find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.left);
                    }
                } else if (button == 'multi_perm_leftAll') {
                    var right_options = Multiselect.right.eq(0).find('option');
                    Multiselect.left.append(right_options);

                    if (typeof Multiselect.callbacks.sort == 'function' && !silent) {
                        Multiselect.left.find('option').sort(Multiselect.callbacks.sort).appendTo(Multiselect.left);
                    }
                }
            }
        });
    });
</script>

<style type="text/css">
    .btn.btn-xs,
    .btn-group-xs>.btn {
        padding: 0px 8px;
    }
</style>