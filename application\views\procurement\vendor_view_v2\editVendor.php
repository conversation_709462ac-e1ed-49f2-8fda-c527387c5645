<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('procurement/requisition_controller_v2');?>">Procurement</a></li>
    <li><a href="<?php echo site_url('procurement/vendor_controller_v2/vendor_master');?>">Vendor Master</a></li>
    <li><a href="<?php echo site_url('procurement/vendor_controller_v2/vendor_details/').$vendor_id;?>">Vendor Details</a></li>
    <li>Edit Vendor</li>
</ul>
<hr>

<div class="col-md-12">
    <div class="card cd_border">
      <div class="card-header panel_heading_new_style_staff_border">
          <div class="row" style="margin: 0px">
          <h3>
            <a href="<?php echo site_url('procurement/vendor_controller_v2/vendor_details/').$vendor_id;?>" class="control-primary"><span class="fa fa-arrow-left"></span></a>
            Edit Vendor
          </h3>
          </div>
      </div>
      <form  id ="demo-form" action="<?php echo site_url('procurement/vendor_controller_v2/updateVendor/').$vendorData->id; ?>" class="form-horizontal"  data-parsley-validate method="POST" enctype="multipart/form-data">
        <div class="panel-body">

          <div class="col-md-6">
            <div class="card cd_border">
                <div class="card-header panel_heading_new_style_staff_border">
                    <div class="row" style="margin: 0px">
                      <h4>Company Details</h4>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="form-group ">
                        <label style="" class="col-md-3 control-label">Name  <font color="red">*</font></label>
                        <div class="col-md-9">
                        <div class="input-group">
            <span class="input-group-addon">
                <span class="fa fa-pencil"></span>
            </span>
            <!--  -->
                        <input  placeholder="Enter Vendor Name" name="vendor_name" value="<?php echo $vendorData->vendor_name; ?>" type="text" class="form-control input-md" data-parsley-error-message="Cannot be empty" data-parsley-pattern="^[a-zA-Z0-9 ]+$"  data-parsley-minlength="1" required="">
                        </div></div>
                    </div>
                    <div class="form-group">
                        <label  style="" class="col-md-3 control-label">GST No.  </label>
                        <div class="col-md-9">
                        <div class="input-group">
            <span class="input-group-addon">
                <span class="fa fa-pencil"></span>
            </span>
            <!--  -->
                        <input  placeholder="Enter GST No." name="gst_no" value="<?php echo $vendorData->gst_no; ?>" type="text" class="form-control input-md" >
                        </div></div>
                    </div>
                    <div class="form-group">
                        <label  style="" class="col-md-3 control-label">Email  </label>
                        <div class="col-md-9">
                        <div class="input-group">
            <span class="input-group-addon">
                <span class="fa fa-pencil"></span>
            </span>
            <!--  -->
                        <input  placeholder="Enter Vendor Email" name="vendor_email" value="<?php echo $vendorData->vendor_email; ?>" type="email" class="form-control input-md" data-parsley-error-message="Enter valid email">
                        </div></div>
                    </div>
                    <div class="form-group">
                        <label  style="" class="col-md-3 control-label">Website  </label>
                        <div class="col-md-9">
                        <div class="input-group">
            <span class="input-group-addon">
                <span class="fa fa-pencil"></span>
            </span>
            <!--  -->
                        <input  placeholder="Enter Vendor Website" name="vendor_website" value="<?php echo $vendorData->vendor_website; ?>" type="url" class="form-control input-md" data-parsley-error-message="Enter valid url">
                        </div></div>
                    </div>
                    <div class="form-group">
                        <label  style="" class="col-md-3 control-label">Phone Number  <font color="red">*</font></label>
                        <div class="col-md-9">
                        <div class="input-group">
            <span class="input-group-addon">
                <span class="fa fa-phone"></span>
            </span>
            <!--  -->
                        <input  placeholder="Enter Vendor Number" name="contact_no" value="<?php echo $vendorData->contact_number; ?>" type="text" class="form-control input-md" data-parsley-error-message="Enter valid phone number" data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]" required="">
                        </div></div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">PAN Number <font color="red">*</font></label>
                        <div class="col-md-9">
                            <div class="input-group">
                                <span class="input-group-addon">
                                    <span class="fa fa-id-card"></span>
                                </span>
                                <input placeholder="Enter PAN Number" name="pan_number" type="text"
                                    value="<?php echo $vendorData->vendor_pan_number; ?>" class="form-control input-md"
                            data-parsley-error-message="Enter valid PAN number" data-parsley-pattern="^[A-Z]{5}[0-9]{4}[A-Z]{1}$"
                            data-parsley-length="[10, 10]" required="">
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="card cd_border">
                <div class="card-header panel_heading_new_style_staff_border">
                    <div class="row" style="margin: 0px">
                      <h4>Contact Person Details</h4>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="form-group ">
                        <label  style="" class="col-md-3 control-label">First Name  <font color="red">*</font></label>
                        <div class="col-md-9">
                        <div class="input-group">
            <span class="input-group-addon">
                <span class="fa fa-pencil"></span>
            </span>
            <!--  -->
                        <input  placeholder="Enter First Name" name="contact_first_name" value="<?php echo $vendorData->contact_first_name; ?>" type="text" class="form-control input-md" data-parsley-error-message="Cannot be empty" data-parsley-pattern="^[a-zA-Z0-9 ]+$"  data-parsley-minlength="1" required="">
                        </div></div>
                    </div>
                    <div class="form-group">
                        <label  style="" class="col-md-3 control-label">Last Name </label>
                        <div class="col-md-9">
                        <div class="input-group">
            <span class="input-group-addon">
                <span class="fa fa-pencil"></span>
            </span>
            <!--  -->
                        <input  placeholder="Enter Last Name" name="contact_last_name" value="<?php echo $vendorData->contact_last_name; ?>" type="text" class="form-control input-md">
                        </div></div>
                    </div>
                    <div class="form-group">
                        <label  style="" class="col-md-3 control-label">Phone Number  <font color="red">*</font></label>
                        <div class="col-md-9">
                        <div class="input-group">
            <span class="input-group-addon">
                <span class="fa fa-phone"></span>
            </span>
            <!--  -->
                        <input  placeholder="Enter Contact Number" name="customer_service_no" value="<?php echo $vendorData->customer_service_number; ?>" type="text" class="form-control input-md" data-parsley-error-message="Enter valid phone number" data-parsley-pattern="^[0-9 -()+]+$" data-parsley-length="[8, 20]" required="">
                        </div></div>
                    </div>
                    <div class="form-group">
                        <label  style="" class="col-md-3 control-label">Email  </label>
                        <div class="col-md-9">
                        <div class="input-group">
            <span class="input-group-addon">
                <span class="fa fa-pencil"></span>
            </span>
            <!--  -->
                        <input  placeholder="Enter Email" name="email" value="<?php echo $vendorData->email; ?>" type="email" class="form-control input-md" data-parsley-error-message="Enter valid email">
                        </div></div>
                    </div>
                    <div class="form-group">
                        <label  style="" class="control-label col-md-3" for="date">On Board </label>
                        <div class="col-md-9"> 
                            <div class="input-group date" id="date_picker"> 
                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                                <input autocomplete="off" type="text" class="form-control" placeholder="On board date" id="dateId" name="on_board" value="<?php echo date('d-m-Y', strtotime($vendorData->on_board)); ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
          </div>
          
        </div>
        <center>
          <div class="" style="margin: 10px 0;">
            <input type="submit" class="btn btn-primary" value="Update Vendor Details">
            <a class="btn btn-warning" href="<?php echo site_url('procurement/vendor_controller_v2/vendor_details/').$vendor_id;?>">Cancel</a>
          </div>
        </center>
        
      </form>
    </div>
</div>
  
<script type="text/javascript">
    $(document).ready(function(){
        $('#date_picker').datepicker({
            format: 'dd-mm-yyyy',
            "autoclose": true
        });
    });
</script>