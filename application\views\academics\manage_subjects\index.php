<ul class="breadcrumb">
    <li><a href="<?php echo base_url('avatars');?>">Dashboard</a></li>
    <li><a href="<?php echo base_url('academics/academics_menu');?>">Academics</a></li>
    <li>Manage Syllabus</li>
</ul>

<div class="col-md-12 col_new_padding">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="d-flex justify-content-between" style="width: 100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('academics/academics_menu') ?>"
                            class="control-primary">
                            <span class="fa fa-arrow-left"></span>
                        </a>
                        Manage Syllabus
                    </h3>
                    <div class="action-container" style="">
                        <div class="actionBtns" style="display: flex;">
                            <button type="button" style="display: none;" onclick="getAvailableSubjects()" id="addSubjectDisabled" disabled="true" class="btn btn-warning" data-toggle="modal" data-target="#addSubjectModal">Add Subjects</button>
                            <button type="button" onclick="showClonePreviousClass()" style="margin-left:1rem;display:none;" id="cloneClass" disabled="true" class="btn btn-primary">Clone subjects for single class </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label class="col-md-12" for="gradeView" style="font-size: 14px;">Select Class</label>
                    <div class="col-md-12">
                        <select class="form-control select2Filter" onchange="<?php echo $is_semester_scheme == '1' ? 'getSemesters()' : 'callGetSubjects()' ?>" name="class_master_id" id="class_master_id">
                            <option value="0">Select Class</option>
                            <?php foreach($classes as $cs){
                                echo "<option class-id='$cs->class_id' value='$cs->class_master_id'>$cs->class_name</option>";
                            } ?>
                        </select>
                        <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                    </div>
                </div>
                <?php if($is_semester_scheme == '1') { ?>
                    <div class="col-md-3">
                        <label class="col-md-12" for="gradeView" style="font-size: 14px;">Select Semester</label>
                        <div class="col-md-12">
                            <select class="form-control" onchange="callGetSubjects()" name="semester_main_screen_id" id="semester_main_screen_id">
                                <option value="0">Select Semester</option>
                            </select>
                            <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                                <i class="fa fa-caret-down"></i>
                            </div>
                        </div>
                    </div>
                <?php } ?>
            </div>
        </div>
        <hr>
        <div id="loader" class="loaderclass" style="display:none;"></div>
        <div class="row m-0 col-md-12">
            <div class="col-md-4" style="padding: 0;">
                <div class="list-group" style="height:41rem; overflow-y: scroll;" id="tasks-data">
                    <div><h4 style="color:#888;" class="pull-left pl-2"><center>Select The Filters To Get The Subject(s)</center></h4></div>
                </div>
            </div>
            <div class="col-md-8 right_side_bar">
                <input type="hidden" name="subject_id_hidden" id="subject_id_hidden">

                <div id="syll_teach_tabs" style="display: none; margin-bottom: 10px;" class="tab" role="tab"
                    style="margin-bottom: 10px;">
                    <span onclick="actionTabs(0)" id="syll" class="label label-default label-form">
                        <i id="faAction1" class="fa fa-angle-down"></i> Syllabus Plan
                    </span>
                    <a id="teach_a" onclick="actionTabs(1)">
                        <span id="teach" class="label label-default label-form active">
                            <i id="faAction2" class="fa fa-angle-up"></i> Access Control
                        </span>
                    </a>
                </div>
                <div class="panel-body pt-0" id="information" style="height: 37rem;overflow: auto;">
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="addSubjectModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="margin-top: 8%;border-radius: .75rem;">
            <div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
                <h4 class="modal-title" id="exampleModalLabel">Add Subject for Class : <span id="classNamePopup" style="color: red"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="card-body">
                    <div class="col-md-12 pb-3">
                        <div class="form-group">
                            <label class="col-md-3 text-right">Select Subject <font style="color:red;">*</font></label>
                            <div class="col-md-9">
                                <select class="form-control" name="subject_master_id" id="subject_master_id">
                                    <option value="">Select Subject</option>
                                </select>
                                <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                                    <i class="fa fa-caret-down"></i>
                                </div>
                            </div>
                        </div>
                        <?php if($is_semester_scheme == '1') { ?>
                            <br>
                            <br>
                            <div class="form-group">
                                <label class="col-md-2">Semester</label>
                                <div class="col-md-10">
                                    <select class="form-control" name="semester_id" id="semester_id">
                                        <option value="0">Select Semester</option>
                                    </select>
                                    <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                                        <i class="fa fa-caret-down"></i>
                                    </div>
                                </div>
                            </div>
                        <?php } else  { ?>
                            <input type="hidden" name="semester_id" id="semester_id" value="0">
                        <?php } ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
                <button type="submit" id="add_subject" onclick="submitSubjects()" class="btn btn-primary mt-0">Submit</button>
            </div>
        </div>
    </div>
</div>

<!---Lesson Modal--->
<div class="modal fade" id="add_edit_lesson" role="dialog" tabindex="-1" data-backdrop="static">
    <!-- Modal content-->
    <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content" style="width: 80%;margin-top:5% !important;margin:auto;">
            <div class="modal-header" style="border-bottom: 2px solid #ccc;">
                <h4 class="modal-title" id="modalHeader"><span id="ael_heading">Add Lesson</span></h4>
                <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;"
                    type="button" class="close" data-dismiss="modal" onclick="resetForm('Lesson')">&times;</button>
            </div>
            <input type="hidden" id="subject_name" name="subject_name">
            <div class="modal-body">
                <div class="col-md-12">
                    <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal"
                        id="form_addLesson">
                        <input type="hidden" id="ael_lesson_id" name="lesson_id">
                        <input type="hidden" id="ael_subject_id" name="subject_id">
                        <input type="hidden" id="ael_mode" name="mode">
                        <div class="form-group">
                            <label class="col-md-4 control-label">Name of the Lesson <font style="color:red;">*</font></label>
                            <div class="col-md-6">
                                <input name="lesson_name" autocomplete="off" class="form-control" type="text" id="ael_lesson_name" placeholder="Enter Lesson Name" required title="3 characters minimum" />
                            </div>

                        </div>
                    </form>
                </div>
            </div>

            <div class="modal-footer">
              <button type="button" class="btn btn-danger" data-dismiss="modal" onclick="resetForm('Lesson')">Cancel</button>
              <button style="margin-right:5px" type="button" onclick="addLesson()" class="btn btn-primary" id="addNewLesson"><span id="ael_button_text">Add</span></button>
            </div>
        </div>

    </div>
</div>

<!---Topic Modal--->
<div class="modal fade" id="addSubTopic" role="dialog" data-backdrop="static">
    <div class="modal-dialog modal-dialog-scrollable">
        <!-- Modal content-->
        <div class="modal-content" style="margin-top: 5% !important; margin: auto;">
            <div class="modal-header">
                <h4 class="modal-title"><span id="s_heading">Add Topic</span></h4>
                <button type="button" class="close" data-dismiss="modal" onclick="resetForm('Topic')">&times;</button>
            </div>
            <input type="hidden" id="subject_name" name="subject_name">
            <div class="modal-body">
                <div class="col-md-12">
                    <form enctype="multipart/form-data" method="post" data-parsley-validate="" class="form-horizontal"
                        id="form_addSubTopic">
                        <input type="hidden" id="s_subject_id" name="subject_id">
                        <input type="hidden" class="form-control" id="s_lesson_id" name="lesson_id" value="">
                        <input type="hidden" class="form-control" id="s_mode" name="mode" value="">
                        <input type="hidden" class="form-control" id="s_topic_id" name="topic_id" value="">
                        <div class="form-group">
                            <label class="col-md-4 control-label">Name of the Topic <font style="color:red;">*</font></label>
                            <div class="col-md-6">
                                <input class="form-control" name="topic_name" class="form-control" type="text" id="s_topic_name" required='' placeholder="Enter Topic Name" />
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal" onclick="resetForm('Topic')">Close</button>
                <button style="margin-right:5px" type="button" onclick="addSubTopic()" class="btn btn-primary" id="addNewSubTopic"><span id="s_button_name">Add Topic</span></button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="addSubjectSemester" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width: 52%;margin: auto;margin-top: 8%;border-radius: .75rem;">
            <div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
                <h4 class="modal-title" id="exampleModalLabel">Edit/Add Semester : <span id="semester-for"></span></h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="card-body">
                    <div class="col-md-12 pb-3">
                        <div class="form-group">
                            <label class="col-md-2">Semester</label>
                            <div class="col-md-10">
                                <select class="form-control" name="subject-semester-id" id="subject-semester-id">
                                    <option value="0">Select Semester</option>
                                </select>
                                <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                                    <i class="fa fa-caret-down"></i>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="semester_subject_id" id="semester_subject_id" value="0">
                        <br>
                        <br>
                        <br>

                        <center>
                          <button type="button" class="btn btn-danger" style="width: 9rem; border-radius: .45rem;" data-dismiss="modal">Close</button>
                          <button type="submit" id="update-semester" onclick="updateSubjectSemester()" style="width: 9rem; border-radius: .45rem;" class="btn btn-primary">Update</button>
                        </center>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- teacher modal -->
<div class="modal fade" id="add_teacher_modal" tabindex="-1" role="dialog" data-backdrop="static" aria-labelledby="resource-uploader-label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content modal-dialog" style="width: 80%;margin-top: 2% !important;margin: auto;">
            <div class="modal-header" style="border-bottom: 2px solid #ccc;">
                <h4 class="modal-title" id="teacher_modal_header">Need to implement</h4>
                <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;"type="button" class="close" data-dismiss="modal" onclick="resetForm('Staff')">&times;</button>
            </div>
            <form id="myform">
                <input type="hidden" id="teacher_subject_id" value="">
                <input type="hidden" id="class_section_id" value="0">
                <input type="hidden" id="teacher_type" value="">
                <div class="modal-body">
                    <div class="col-md-12">
                        <!-- Staff types filter: Make default Selected All-->
                        <!-- staffTypes -->
                        <div class="form-group" id="">
                            <label for="name" class="col-md-5 col-xs-12 control-label">Staff Type <font color="red">*</font>
                                </label>
                            <div class="col-md-7 col-xs-12">
                                <div class="input-group">
                                    <select name="staff_type_id" id="staff_type_id" class="form-control" required="" onchange="getStaffOnstaffType()">
                                        <?php if(is_array($staff_types) && !empty($staff_types)){
                                            foreach($staff_types as $key => $val){
                                                echo '<option value='.$key.'>'.$val.'</option>';
                                            }
                                        }else{
                                            echo '<option value="-1">All</option>';
                                        } ?>
                                    </select>
                                    <div style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%);z-index:5;">
                                        <i class="fa fa-caret-down"></i>
                                    </div>
                                </div>
                                <div class="help-block">Select Staff Type Here</div>
                            </div>
                        </div>
                        <div class="form-group" id="teacher_div">
                            <label for="name" class="col-md-5 col-xs-12 control-label">Teacher Name <font color="red">*
                                </font></label>
                            <div class="col-md-7 col-xs-12">
                                <div class="input-group">
                                    <select name="" id="teacher_name" class="form-control select3Filter" onchange="check_staff_validity()" required=""></select>
                                </div>
                                <div class="help-block">Select Teacher Name Here</div>
                            </div>
                        </div>
                        <div class="form-group" id="teacher_access_div">
                            <label for="name" class="col-md-5 col-xs-12 control-label">Teacher Accessibility <font
                                    color="red">*</font></label>
                            <div class="col-md-7 col-xs-12">
                                <div class="input-group">
                                    <select name="" id="teacher_accessibility" class="form-control">
                                        <option value="write">Write</option>
                                        <option value="read_only">Read Only</option>
                                    </select>
                                    <div style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%);z-index:5;">
                                        <i class="fa fa-caret-down"></i>
                                    </div>
                                </div>
                                <div class="help-block">Select Accessibility</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-dismiss="modal" onclick="resetForm('Staff')">Close</button>
                    <button type="button" class="btn btn-primary mt-0" onclick="assign_teacher_to_the_subject()">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>
<div id="flash_storage" style="display: none;"></div>

<!-- clone previous syllabus -->
<div class="modal fade" id="ShowClonePreviousSyllabusModal" tabindex="-1" role="dialog"
    aria-labelledby="ShowClonePreviousSyllabusModal" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ShowClonePreviousSyllabusModal">Clone Whole Syllabus</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="container container-fluid" id="">
                    <!-- acad year filter -->
                    <div class="" style="margin-bottom: 1rem;">
                        <label class="control-label" for="category">From Academic Year <font style="color:red;">*</font></label>
                        <select class="form-control select2 col-md-12" name="clone_syllabus-acad_year_id" id="clone_syllabus-acad_year_id" title="From Academic year">
                            <option value="" selected>Select year</option>
                            <?php if (!empty($acad_years_for_subject_clone)) {
                                foreach ($acad_years_for_subject_clone as $val) {
                                if($val->id!=$currentYearId){
                                    echo '<option value="' . $val->id . '">' . $val->acad_year . '</option>';
                                }
                                }
                            } else {
                                echo '<option value"">No year to show / Syllabus does not exists in previous years</option>';
                            } ?>
                        </select>
                        <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                    </div>

                    <div class="" style="margin-bottom: 1rem;">
                        <label class="control-label" for="category">To Academic Year <font style="color:red;">*</font></label>
                        <input class="form-control" type="text" disabled id="to_syllabus_acad_year">
                    </div>

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="clonePreviousSyllabus()">Clone</button>
            </div>
        </div>
    </div>
</div>

<!-- clone syllabus class wise -->
<div class="modal fade" id="ShowClonePreviousClassModal" tabindex="-1" role="dialog" aria-labelledby="ShowClonePreviousClassModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="margin-top: 8%;">
            <div class="modal-header">
                <h5 class="modal-title" id="ShowClonePreviousClassModalLabel">Clone Subjects For Single Class</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="class_clone_modal_loader" style="display: none;">Loading...</div>
                <div class="container container-fluid" id="class_clone_modal">
                    <!-- acad year filter -->
                    <div class="">
                        <label class="control-label" for="category">From Academic Year <font style="color:red;">*</font></label>
                        <select class="form-control select2 col-md-12" name="clone_class-acad_year_id"
                            id="clone_class-acad_year_id" title="From Academic year"
                            onchange="getClassesForClassClone()">
                            <option value="0" selected>Select year</option>
                            <?php if (!empty($acad_years_for_subject_clone)) {
                                foreach ($acad_years_for_subject_clone as $val) {
                                    echo '<option value="' . $val->id . '">' . $val->acad_year . '</option>';
                                }
                            } else {
                                echo '<option value"">No year to show / Syllabus does not exists in previous years</option>';
                            } ?>
                        </select>
                        <div style="position: absolute; right: 37px; top: 20%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                    </div>

                    <!-- subject list filter -->
                    <div class="">
                        <label class="control-label" for="category">From Class <font style="color:red;">*</font></label>
                        <select class="form-control select2 col-md-12" name="clone_class_id" id="clone_class_id" title="Classes" onchange="getSemestersForCloneSubjects()">
                            <option value="">Select class</option>
                        </select>
                        <div style="position: absolute; right: 37px; top: 44%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                    </div>

                    <?php if ($is_semester_scheme == '1') { ?>
                        <div class="">
                            <label class="control-label" for="from_class_semester_id">From Semester<font style="color:red;">*</font></label>
                            <select class="form-control" name="from_class_semester_id" id="from_class_semester_id">
                                <option value="0">Select Semester</option>
                            </select>
                            <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                                <i class="fa fa-caret-down"></i>
                            </div>
                        </div>
                    <?php } ?>

                    <div class="">
                        <label class="control-label" for="category">To Academic Year <font style="color:red;">*</font></label>
                        <input class="form-control" type="text" disabled id="to_class_acad_year">
                    </div>

                    <div class="">
                        <label class="control-label" for="category">To Class <font style="color:red;">*</font></label>
                        <input class="form-control" type="text" disabled id="to_class">
                    </div>

                    <?php if ($is_semester_scheme == '1') { ?>
                    <div class="">
                        <label class="control-label" for="clone_class_to_semester">To semester <font style="color:red;">*</font></label>
                        <input class="form-control" type="text" disabled id="clone_class_to_semester">
                    </div>
                    <?php } ?>

                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="clonePreviuosClass()">Clone</button>
            </div>
        </div>
    </div>
</div>

<!-- clone syllabus subject wise -->
<div class="modal fade" id="ShowCloneIndividualSubjectModal" tabindex="-1" role="dialog"
    aria-labelledby="ShowCloneIndividualSubjectModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="margin-top: 5% !important; margin: auto;">
            <div class="modal-header">
                <h5 class="modal-title" id="ShowCloneIndividualSubjectModalLabel">Clone Syllabus Plan</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- add loader -->
                <div id="subject_clone_modal_loader" style="display: none;">Loading...</div>
                <div class="container container-fluid" id="subject_clone_modal">
                    <!-- acad year filter -->
                    <div class="">
                        <label class="control-label" for="category">From Academic Year <font style="color:red;">*</font></label>
                        <select class="form-control select2 col-md-12" name="clone-subject_acad_year_id" id="clone-subject_acad_year_id" title="From Academic year"
                            onchange="getClassesForSubjectClone()">
                            <option value="0" selected>Select year</option>
                            <?php if(!empty($acad_years_for_subject_clone)){
                                foreach($acad_years_for_subject_clone as $val){
                                    echo '<option value="'.$val->id.'">'.$val->acad_year.'</option>';
                                }
                            } else{
                                echo '<option value"">No year to show / Syllabus does not exists in previous years</option>';
                            } ?>
                        </select>
                        <div style="position: absolute; right: 37px; top: 14%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                    </div>

                    <div class="">
                        <label class="control-label" for="from_class">From class <font style="color:red;">*</font></label>
                        <select class="form-control select2Filter"
                            onchange="<?php echo $is_semester_scheme == '1' ? 'getSemestersForCloneSingleSubject()' : 'callGetSubjectsForClone()' ?>"
                            name="clone_class_master_id" id="clone_class_master_id">
                            <option value="">Select Class</option>
                        </select>
                        <div style="position: absolute; right: 37px; top: 30%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                    </div>

                    <?php if ($is_semester_scheme == '1') { ?>
                        <div class="">
                            <label class="control-label" for="from_semester_id">From Semester<font style="color:red;">*</font></label>
                            <select class="form-control" onchange="callGetSubjectsForClone()" name="from_semester_id"
                                id="from_semester_id">
                                <option value="">Select Semester</option>
                            </select>
                            <div style="position: absolute; right: 25px; top: 50%; transform: translateY(-50%);">
                                <i class="fa fa-caret-down"></i>
                            </div>
                        </div>
                    <?php } ?>

                    <div class="">
                        <label class="control-label" for="category">From Subject <font style="color:red;">*</font></label>
                        <select class="form-control select2 col-md-12" name="clone-subject_id" id="clone-subject_id" title="Subjects">
                            <option value="">Select subject</option>
                        </select>
                        <div style="position: absolute; right: 37px; top: 45%; transform: translateY(-50%);">
                            <i class="fa fa-caret-down"></i>
                        </div>
                    </div>

                    <div class="">
                        <label class="control-label" for="category">To Academic Year <font style="color:red;">*</font></label>
                        <input class="form-control" type="text" disabled id="to_acad_year">
                    </div>

                    <div class="">
                        <label class="control-label" for="to_class">To class <font style="color:red;">*</font></label>
                        <input class="form-control" type="text" disabled id="clone_to_class">
                    </div>

                    <?php if ($is_semester_scheme == '1') { ?>
                        <div class="">
                            <label class="control-label" for="clone_to_semester">To semester <font style="color:red;">*</font></label>
                            <input class="form-control" type="text" disabled id="clone_to_semester">
                        </div>
                    <?php } ?>

                    <div class="">
                        <label class="control-label" for="category">To Subject <font style="color:red;">*</font></label>
                        <input class="form-control" type="text" disabled id="to_subject">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="cloneIndividualSubject()">Clone</button>
            </div>
        </div>
    </div>
</div>

<style type="text/css">
#dataTable_filter{
  width: 51% !important;
}
.modal-dialog {
    width: 50%;
    margin: auto;
}

.select2-container,
.select2 {
    width: 100% !important;
}

.new_circleShape_res {
    padding: 5px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 17px;
    height: 2.3rem !important;
    width: 2.3rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 0rem !important;
}

.new_circleShape_res1 {
    padding: 5px 8px;
    border-radius: 50% !important;
    font-size: 16px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 0rem !important;
}

.loaderclass {
    border: 8px solid #eee;
    border-top: 8px solid #7193be;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    position: fixed;
    z-index: 1;
    animation: spin 2s linear infinite;
    margin-top: 30%;
    margin-left: 40%;
    position: absolute;
    z-index: 99999;
}

.btn-info {
    background-color: #1caf9a;
    border-color: #1caf9a;
}

.active1 {
    background: #6893ca;
}

.discard {
    background: #C82333;
}

.loader-background {
    width: 100%;
    height: 100%;
    position: absolute;
    display: none;
    top: 0;
    left: 0;
    opacity: 0.8;
    z-index: 10;
    background-color: #fff;
    border-radius: 8px;
}

.label {
    border-radius: .45em;
}

.list-group-item {
    margin-bottom: 1px;
    border: none;
    margin-right: 1rem;
}

.label-default,
.label-success,
.label-danger {
    cursor: pointer;
}

.list-group-item.active1 {
    background-color: #ebf3f9;
    border-color: #ebf3f9;
    color: #737373;
}

.list-group-item.active1,
.list-group-item.active1:hover,
.list-group-item.active1:focus {
    background: #ebf3f9;
    color: #737373;
}

.list-group-item {
    border: none;
}

.medium {
    width: 450px;
    margin: auto;
}

.select2-container {
    z-index: 1051 !important;
}
</style>


<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    function resetForm(form) {
        if(form == 'Topic'){
            var form = $('#form_addSubTopic');
            form[0].reset();
            form.parsley().reset();
        } else if(form == 'Lesson'){
            var form = $('#form_addLesson');
            form[0].reset();
            form.parsley().reset();
        } else if(form == 'Staff'){
            var form = $('#myform');
            form[0].reset();
            form.parsley().reset();
        }
    }

$(document).ready(function() {
    $('#add_teacher_modal').on('shown.bs.modal', function() {
        if ($.fn.select2 && $('#teacher_name').hasClass('select2-hidden-accessible')) {
            $('#teacher_name').select2('destroy');
        }
        $('#teacher_name').select2({
            dropdownParent: $('#add_teacher_modal')
        });
    });
    if (localStorage.getItem("currentClassId")) {
        $("#class_master_id").val(localStorage.getItem("currentClassId"));
    }
    if (localStorage.getItem("currentSemesterId")) {
        $("#semester_main_screen_id").val(localStorage.getItem("currentSemesterId"));
    }
    callGetSubjects();
});

async function getSemestersForCloneSubjects() {
    // const classMasterId=$("#clone_class_master_id").val();
    if (+is_semester_scheme === 1) {
        const class_master_id = $("#clone_class_id").val();
        const semestersOptions = await getSemestersForCloning(class_master_id);

        if (!semestersOptions) {
            // handle empty check
        }

        $("#from_class_semester_id").html(semestersOptions);
    }
}

async function getSemestersForCloneSingleSubject() {
    if (+is_semester_scheme === 1) {
        const class_master_id = $("#clone_class_master_id").val();
        const semestersOptions = await getSemestersForCloning(class_master_id);

        if (!semestersOptions) {
            // handle empty check
        }

        $("#from_semester_id").html(semestersOptions);
    } else {
        callGetSubjectsForClone()
    }
}


var staffTypes = [];
var is_semester_scheme = 0;
var is_add_subjects_permission_enabled = 0;
var is_clone_option_permission_enabled = 0;
var is_delete_subjects_permission_enabled = 0;

is_semester_scheme = <?php echo $is_semester_scheme; ?>;
is_add_subjects_permission_enabled = <?php echo $is_add_subjects_permission_enabled; ?>;
is_clone_option_permission_enabled = <?php echo $is_clone_option_permission_enabled; ?>;
is_delete_subjects_permission_enabled = <?php echo $is_delete_subjects_permission_enabled; ?>;

staffTypes = <?php echo json_encode($staff_types); ?>;

if (+is_add_subjects_permission_enabled) {
    $("#addSubjectDisabled").show();
}

if (+is_clone_option_permission_enabled) {
    $("#cloneSyllabus").show();
    $("#cloneClass").show();
}

async function getClassesForCloning(acadYearId) {
    let options = "";
    await $.ajax({
        url: "<?php echo site_url('academics/ManageSubjects/getClassesForLmsClone') ?>",
        type: "POST",
        data: {
            acadYearId
        },
        success: function(res) {
            const classes = JSON.parse(res);
            if (!classes?.length) {
                options = `<option value="-1">Found no classes</option>`;
            } else {
                options = `<option value="">Select Class</option>`;
                classes.forEach(cl => {
                    options +=
                    `<option value="${cl.class_master_id}">${cl.class_name}</option>`;
                });
            }
        }
    });

    return options;
}

async function getClassesForClassClone() {
    const acadYearId = $("#clone_class-acad_year_id").val();
    const classesOptions = await getClassesForCloning(acadYearId);

    if (!classesOptions) {
        // handle empty check
    }

    $("#clone_class_id").html(classesOptions);

    if (+is_semester_scheme === 1) {
        const class_master_id = $("#clone_class_id").val();
        const semestersOptions = await getSemestersForCloning(class_master_id);

        if (!semestersOptions) {
            // handle empty check
        }

        $("#from_class_semester_id").html(semestersOptions);
    }
}

async function getClassesForSubjectClone() {
    const acadYearId = $("#clone-subject_acad_year_id").val();
    const classesOptions = await getClassesForCloning(acadYearId);
    if (!classesOptions) {
        return;
    }
    $("#clone_class_master_id").html(classesOptions);
    if (+is_semester_scheme === 1) {
        const class_master_id = $("#clone_class_master_id").val();
        const semestersOptions = await getSemestersForCloning(class_master_id);
        if (!semestersOptions) {
            return;
        }
        $("#from_semester_id").html(semestersOptions);
    } else {
        callGetSubjectsForClone()
    }
}

async function getSemestersForCloning(class_master_id) {
    // console.log(class_master_id)
    let options = "";

    await $.ajax({
        url: '<?php echo site_url('academics/ManageSubjects/get_class_semesters') ?>',
        type: 'post',
        data: {
            'class_master_id': class_master_id
        },
        success: function(data) {
            data = $.parseJSON(data);
            var semesters = data.semesters;
            options = '<option value="">Select Semester</option>';
            for (var i = 0; i < semesters.length; i++) {
                options += `<option value="${semesters[i].id}">${semesters[i].sem_name}</option>`;
            }
        }
    });
    return options;
}

//  clone indicidual subject logic starts
function callGetSubjectsForClone() {
    const classMasterId = $("#clone_class_master_id").val();
    const acadYearId = $("#clone-subject_acad_year_id").val();
    const semesterId = $("#from_semester_id").val() || 0;

    $.ajax({
        url: "<?php echo site_url('academics/ManageSubjects/callGetSubjectsForClone') ?>",
        type: "POST",
        data: {
            'class_master_id': classMasterId,
            'semester_main_screen_id': semesterId,
            acadYearId
        },
        success: function(res) {
            const {
                viewSubjects
            } = JSON.parse(res);

            let options = ``;
            if (!viewSubjects?.length) {
                options = `<option value="-1">Found no subjects</option>`;
            } else {
                options = `<option value="">Select Subject</option>`;
                viewSubjects.forEach(s => {
                    options += `<option value="${s.id}">${s.subject_name}</option>`;
                });
            }

            $("#clone-subject_id").html(options);
        }
    })
}

function ShowCloneIndividualSubject() {
    $("#clone-subject_acad_year_id").val(0);
    getClassesForSubjectClone();

    $("#subject_clone_modal_loader").show();
    $("#subject_clone_modal").hide();

    const acad_year = <?php echo json_encode($acad_year); ?>;
    if (!acad_year?.length) {
        return Swal.fire({
            icon: "info",
            title: "Info",
            text: "Syllabus doesnot exists in previous years!",
        });
    }

    const currentClassName = $("#class_master_id option:selected").text();
    const currentSemName = $("#semester_main_screen_id option:selected").text();
    const currentSubjectId = window.localStorage.getItem("currentSubjectId");
    let subjectName;
    try {
        subjectName = document.querySelector(`#subject-${currentSubjectId}`).querySelector("span").textContent;
    } catch (err) {
        return Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "Please select subject to clone!",
        });
    }
    const currentYearId = "<?php echo $currentYearId ?>";

    $("#to_acad_year").val(currentYearId);
    $("#to_subject").val(subjectName);
    $("#clone_to_class").val(currentClassName);
    $("#clone_to_semester").val(currentSemName);

    $("#subject_clone_modal").show();
    $("#subject_clone_modal_loader").hide();

    $("#ShowCloneIndividualSubjectModal").modal("show");
}

function cloneIndividualSubject() {
    let cloneSubjectAcarYearId = $('#clone-subject_acad_year_id option:selected').val();
    if(cloneSubjectAcarYearId == 0){
        return Swal.fire({
            icon: "error",
            title: "Oops...",
            html: "Required fields cannot be empty! <b>From Academic Year</b>",
        });
    }
    let cloneClassMaster = $('#clone_class_master_id option:selected').val();
    if(cloneClassMaster == 0){
        return Swal.fire({
            icon: "error",
            title: "Oops...",
            html: "Required fields cannot be empty! <b>From Class</b>",
        });
    }
    <?php if ($is_semester_scheme == '1') { ?>
        let cloneSemesterId = $('#from_semester_id option:selected').val();
        if(cloneSemesterId == 0){
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                html: "Required fields cannot be empty! <b>From Semester</b>",
            });
        }
    <?php } ?>
    const oldLpSubjectId = $("#clone-subject_id").val();
    if (oldLpSubjectId == -1 || oldLpSubjectId == '') {
        return Swal.fire({
            icon: "error",
            title: "Oops...",
            html: "Required fields cannot be empty! <b>From Subject</b>",
        });
    }
    const newLpSubjectId = window.localStorage.getItem("currentSubjectId");
    
    $.ajax({
        url: "<?php echo site_url('academics/ManageSubjects/clone_individual_subject') ?>",
        type: "POST",
        data: {
            newLpSubjectId,
            oldLpSubjectId
        },
        success: function(res) {
            $("#ShowCloneIndividualSubjectModal").modal("hide");

            const currentSubjectId = window.localStorage.getItem("currentSubjectId");
            $(`#subject-${currentSubjectId}`).trigger("click");

            if (+res >= 1) {
                return Swal.fire({
                    icon: "success",
                    title: "Cloned",
                    text: "Subject cloned successfully!",
                });
            }

            if (+res == -1) {
                return Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: "Subject cannot be cloned as data is already present, Please try again after removing data!",
                });
            }

            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Something went wrong!",
            });
        }
    })
}

// clone individual subject logic ends

// clone previous class logic starts

function clonePreviuosClass() {
    // 1. need old class master id
    const oldClassMasterId = $("#clone_class_id").val();
    // 1. need new class master id
    const newClassMasterId = $("#class_master_id").val();
    // 2. need old acad year id
    const oldAcadYearId = $("#clone_class-acad_year_id").val();
    // 2. need new acad year id
    const newAcadYearId = $("#to_class_acad_year").val();

    const gradeName = $("class_master_id :selected").text();

    const fromSemesterId = $("#from_class_semester_id").val() || 0;
    const toSemesterId = $("#semester_main_screen_id").val() || 0;
    if(oldAcadYearId == 0){
        Swal.fire({
            icon: "error",
            title: "Oops...",
            html: "Required fields cannot be empty! <b>From Academic Year</b>",
        });
        return false;
    }
    if(oldClassMasterId == '-1'){
        Swal.fire({
            icon: "error",
            title: "Oops...",
            html: "Required fields cannot be empty! <b>From Class</b>",
        });
        return false;
    }
    if (!oldAcadYearId?.length || !oldClassMasterId?.length) {
        return Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "Required fields cannot be empty!",
        });
    }

    $.ajax({
        url: "<?php echo site_url('academics/ManageSubjects/clone_previous_class') ?>",
        type: "POST",
        data: {
            oldAcadYearId,
            newAcadYearId,
            oldClassMasterId,
            newClassMasterId,
            fromSemesterId,
            toSemesterId
        },
        success: function(res) {
            $("#ShowClonePreviousClassModal").modal("hide");
            let parsedData = JSON.parse(res);
            // console.log(parsedData);
            if (+res >= 1) {
                callGetSubjects();
                return Swal.fire({
                    icon: "success",
                    title: "Cloned",
                    text: "Class subjects cloned successfully!",
                });
            }
            if(+res == -2){
                return Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: `Class subjects cannot be cloned, as the class subjects does not exists in previous class!`,
                });
            }
            if (+res == -1) {
                return Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: `Class subjects cannot be cloned, as the class subjects already present in grade ${gradeName}. Please remove all subjects to clone!`,
                });
            }
            return Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Something went wrong!",
            });
        },
        error: function(err) {
            console.log(err);
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Something went wrong!",
            });
        }
    })
}

function showClonePreviousClass() {
    $("#clone_class-acad_year_id").val(0);
    getClassesForClassClone();

    $("#class_clone_modal_loader").show();
    $("#class_clone_modal").hide();

    const acad_year = <?php echo json_encode($acad_year); ?>;
    if (!acad_year?.length) {
        return Swal.fire({
            icon: "info",
            title: "Info",
            text: "Syllabus does not exists in previous years!",
        });
    }

    const currentYearId = "<?php echo $currentYearId ?>";
    $("#to_class_acad_year").val(currentYearId);

    const currentClassName = $("#class_master_id :selected").text();
    $("#to_class").val(currentClassName);

    const currentSemName = $("#semester_main_screen_id option:selected").text();
    $("#clone_class_to_semester").val(currentSemName);

    $("#class_clone_modal_loader").hide();
    $("#class_clone_modal").show();

    $("#ShowClonePreviousClassModal").modal("show");
}
// clone previous class logic ends


function actionTabs(n) {
    window.sessionStorage.setItem("currentTab", n);
    if (n == 0) {
        $('#teach_tab').hide();
        $('#teach').css('background', 'gray');
        $('#teach').removeClass('active');

        $('#syll_tab').show();
        $('#syll').addClass('active');
        $('#syll').css('background', 'skyblue');

        $('#faAction1').addClass('fa-angle-down');
        $('#faAction2').removeClass('fa-angle-down');
        $('#faAction2').addClass('fa-angle-up');
    } else if (n == 1) {
        $('#syll_tab').hide();
        $('#syll').css('background', 'gray');
        $('#syll').removeClass('active');

        $('#teach_tab').show();
        $('#teach').addClass('active');
        $('#teach').css('background', 'skyblue');

        $('#faAction2').addClass('fa-angle-down');
        $('#faAction1').removeClass('fa-angle-down');
        $('#faAction1').addClass('fa-angle-up');
    }
}

function ShowClonePreviousSyllabusModal() {
    const acad_year = <?php echo json_encode($acad_year); ?>;
    if (!acad_year?.length) {
        return Swal.fire({
            icon: "info",
            title: "Info",
            text: "Syllabus does not exists in previous years!",
        });
    }
    const currentYearId = "<?php echo $currentYearId ?>";
    $("#to_syllabus_acad_year").val(currentYearId);

    $("#ShowClonePreviousSyllabusModal").modal("show");
}

function clonePreviousSyllabus() {
    const fromAcadYearId = $("#clone_syllabus-acad_year_id").val();

    const fromAcadYearName = $("#clone_syllabus-acad_year_id :selected").text();

    if (!fromAcadYearId?.length) {
        return Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "From Academic year cannot be empty!",
        });
    }
    // logic for cloning previous Syllabus
    Swal.fire({
        title: "Are you sure?",
        html: "You want to clone Syllabus <br> You won't be able to revert this!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, clone it!"
    }).then((result) => {
        if (result.isConfirmed) {
            // const toAcadYearId = $("#to_syllabus_acad_year").val();
            // const class_master_id = $("#class_master_id").val();
            const semester_main_screen_id = $("#semester_main_screen_id").val() || '';

            $.ajax({
                url: '<?php echo site_url('academics/ManageSubjects/clone_previous_syllabus') ?>',
                type: 'post',
                data: {
                    semester_main_screen_id,
                    fromAcadYearId
                },
                success: function(res) {
                    if (+res >= 1) {
                        return Swal.fire({
                            icon: "success",
                            title: "Cloned",
                            text: "Syllabus cloned successfully, Please select Class or Semester to view syllabus!",
                        }).then(e => {
                            $("#ShowClonePreviousSyllabusModal").modal("hide");

                            const selectedClassMasterId = $("#class_master_id").val();
                            if (selectedClassMasterId == 0) {
                                window.location.reload();
                            } else {
                                const currentOpenedSemester = $("#semester_main_screen_id")
                                    .val();
                                if (currentOpenedSemester <= 0) {
                                    if (is_semester_scheme == 1) {
                                        const semestersFirstOption = document.querySelector("#semester_main_screen_id").querySelectorAll("option")[1]?.value;
                                        if (semestersFirstOption > 0) {
                                            $("#semester_main_screen_id").val(semestersFirstOption);
                                        }
                                    }
                                }
                                callGetSubjects();
                            }
                        })
                    }

                    if (+res == -1) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: `Syllabus cannot be cloned, as the syllabus already present in the current academic year!`,
                        }).then(e => {
                            $("#ShowClonePreviousSyllabusModal").modal("hide");
                        })
                    }

                    if (+res == 0) {
                        return Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: `Syllabus cannot be cloned beacause, the syllabus does not exists in the ${fromAcadYearName} academic year!`,
                        }).then(e => {
                            $("#ShowClonePreviousSyllabusModal").modal("hide");
                        })
                    }

                    return Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!",
                    }).then(e => {
                        $("#ShowClonePreviousSyllabusModal").modal("hide");
                    })

                }
            });
        }
    });
}

function getAvailableSubjects() {
    var class_master_id = $("#class_master_id").val();
    var semester_main_screen_id = $("#semester_main_screen_id").val() || '';
    $.ajax({
        url: '<?php echo site_url('academics/ManageSubjects/getNotAddedSubjects') ?>',
        type: 'post',
        data: {
            'class_master_id': class_master_id,
            'semester_main_screen_id': semester_main_screen_id
        },
        success: function(data) {
            var data = $.parseJSON(data);
            var subjects = data.subjects;
            var options = '<option value="">Select Subject</option>';
            for (var i = 0; i < subjects.length; i++) {
                options += `<option value="${subjects[i].id}">${subjects[i].subject_name}</option>`;
            }
            $("#subject_master_id").html(options);
            if (is_semester_scheme == '1') {
                var semesters = data.semesters;
                var options = '<option value="0">Select Semester</option>';
                for (var i = 0; i < semesters.length; i++) {
                    options += `<option value="${semesters[i].id}">${semesters[i].sem_name}</option>`;
                }
                $("#semester_id").html(options);
            }
        }
    });
}

function getSemesters() {
    var class_master_id = $("#class_master_id").val();
    if (class_master_id == 0) {
        return false;
    }
    $.ajax({
        url: '<?php echo site_url('academics/ManageSubjects/get_class_semesters') ?>',
        type: 'post',
        data: {
            'class_master_id': class_master_id
        },
        success: function(data) {
            var data = $.parseJSON(data);
            var semesters = data.semesters;
            var options = '<option value="0">Select Semester</option>';
            for (var i = 0; i < semesters.length; i++) {
                options += `<option value="${semesters[i].id}">${semesters[i].sem_name}</option>`;
            }
            $("#semester_main_screen_id").html(options);
        }
    });
}

function submitSubjects() {
    var class_master_id = $("#class_master_id").val();
    var class_name = $("#class_master_id option:selected").text();
    var subject_master_id = $("#subject_master_id").val();
    if (!subject_master_id) {
        Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "Please select subject!",
        });
        return false;
    }
    var semester_id = $("#semester_id").val();
    if (is_semester_scheme == '1' && !semester_id) {
        Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "Please select semester!",
        });
        return false;
    }

    $("#add_subject").prop('disabled', true).html('Please wait...');
    $('#addSubjectModal').modal('hide');
    $.ajax({
        url: '<?php echo site_url('academics/ManageSubjects/submitSubjects') ?>',
        type: 'post',
        data: {
            'class_master_id': class_master_id,
            'class_name': class_name,
            'subject_master_id': subject_master_id,
            'semester_id': semester_id
        },
        success: function(data) {
            $("#add_subject").prop('disabled', false).html('Submit');
            let parsedData = JSON.parse(data);
            if (parsedData == 1) {
                Swal.fire({
                    icon: "success",
                    title: "Success",
                    text: "Subject added successfully!",
                }).then(e => {
                    callGetSubjects();
                });
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Oops...",
                    text: "Something went wrong!",
                });
            }
        },
        error: function(err) {
            console.log(err);
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Something went wrong!",
            });
        }
    });
}

function delete_subject(subject_id, subjectName) {
    Swal.fire({
        title: `Are you sure?`,
        html: `<div style="font-size: 18px;">Delete Subject <strong>${subjectName}</strong>?</div>`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
        reverseButtons: true,
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo site_url('academics/ManageSubjects/delete_subject') ?>',
                type: 'post',
                data: {
                    'subject_id': subject_id
                },
                success: function(data) {
                    var data = $.parseJSON(data);
                    switch (data) {
                        case -1:
                            Swal.fire({
                                icon: "error",
                                title: "Oops...",
                                text: "Cannot delete the subject as it is used in E-Library or it has topics!",
                            });
                            break;
                        case -2:
                            Swal.fire({
                                icon: "error",
                                title: "Oops...",
                                text: "Cannot delete the subject as it is used in Assessment!",
                            });
                            break;
                        case -3:
                            Swal.fire({
                                icon: "error",
                                title: "Oops...",
                                text: "Cannot delete the subject as it is used in Lesson Plan!",
                            });
                            break;
                        case 1:
                            Swal.fire({
                                icon: "success",
                                title: "Success",
                                text: "Subject deleted successfully!",
                            });
                            // hide right side content if that is being deleted
                            const currentClickedSubjectId = window.localStorage.getItem("currentSubjectId");
                            if (subject_id == currentClickedSubjectId) {
                                $(".right_side_bar").hide();
                            } 
                            // else {
                            //     $(`#subject-${currentClickedSubjectId}`).trigger("click");
                            // }
                            break;
                        default:
                            Swal.fire({
                                icon: "error",
                                title: "Oops...",
                                text: "Something went wrong!",
                            });
                            break;
                    }
                },
                error: function(err) {
                    console.log(err);
                },
                complete: function() {
                    callGetSubjects();
                }
            });
        }
    });
}

function callGetSubjects() {
    var class_master_id = $("#class_master_id").val();
    var semester_main_screen_id = $("#semester_main_screen_id").val() || '';
    var class_name = $("#class_master_id option:selected").text();
    $('#classNamePopup').html(class_name);
    if (class_master_id == 0) {
        $("#tasks-data").html('<div><h4 style="color:#888;" class="pull-left pl-2"><center>Please Select A Class</center></h4></div>');
        $("#information").html('');
        $("#options").html('');
        $('#addSubjectDisabled').prop('disabled', true);
        $('#cloneSyllabus').prop('disabled', true);
        $('#cloneClass').prop('disabled', true);
        $('#syll').hide();
        $('#teach_a').hide();
        return false;
    }
    if (is_semester_scheme == 1) {
        if (+semester_main_screen_id === 0) {
            $('#cloneClass').hide();
        } else {
            $('#cloneClass').show();
        }
    }
    localStorage.setItem("currentClassId", class_master_id);
    localStorage.setItem("currentSemesterId", semester_main_screen_id);
    $.ajax({
        url: '<?php echo site_url('academics/ManageSubjects/get_subjects') ?>',
        type: 'post',
        data: {
            'class_master_id': class_master_id,
            'semester_main_screen_id': semester_main_screen_id
        },
        beforeSend: function() {
            $('#opacity').css('opacity', '0.5');
            $('#loader').show();
            $('#addSubjectDisabled').prop('disabled', false);
            $('#cloneSyllabus').prop('disabled', false);
            $('#cloneClass').prop('disabled', false);
        },
        success: function(data) {
            var data = $.parseJSON(data);
            var viewSubjects = data.viewSubjects;

            var html = '';
            if (viewSubjects.length == '') {
                $("#syll_teach_tabs").hide();

                $("#tasks-data").html('<div><h4 style="color:#888;" class="pull-left pl-2"><center>No Data Found</center></h4></div>');
                $("#information").html('');
                $("#options").html('');
                // $('#addSubjectDisabled').prop('disabled',true);
            } else {
                // $("#syll_teach_tabs").show();
                // $("#cloneSyllabus").prop("disabled",false);
                $(".action-container").show();
                $('#syll').show();
                $('#teach_a').show();
                $('#tasks-data').html(construct_classwise_sublist(viewSubjects));
                $(".datatable").DataTable({
                    order: [],
                    "dom": '<"wrapper"flipt>',
                    "bPaginate": false
                });
                const currentClickedSubjectId = window.localStorage.getItem("currentSubjectId");
                $(`#subject-${currentClickedSubjectId}`).trigger("click")
            }
        },
        complete: function() {
            $('#loader').hide();
            $('#opacity').css('opacity', '');
        }
    });
}

function construct_classwise_sublist(viewSubjects) {
    var output = '';
    var m = 1;
    output += '<table class="table table-bordered datatable" id="dataTable">';
    output += '<thead>';
    output += '<tr>';
    output += `<th>Subjects</th>`;
    output += '</tr>';
    output += '</thead>';
    output += '<tbody>';
    for (var i = 0; i < viewSubjects.length; i++) {
        if (m == 1) {
            window.localStorage.setItem("currentSubjectId", viewSubjects[i].subject_id);
            var active1 = 'active1';
        } else {
            var active1 = '';
        }
        output += '<tr>';
        output += `
            <td style="padding: 0px;">
                <div class="col-md-8" style="padding-left: 0px;padding-right: 0px; cursor: pointer;" id="click-subject-${viewSubjects[i].subject_id}">
                <div data-subject_name="${viewSubjects[i].subject_name}" data-semester_name="${viewSubjects[i].sem_name}" data-semester_id="${viewSubjects[i].semester_id}" id="subject-${viewSubjects[i].subject_id}" style="display: inline-block;width: 100%;height: 100%;margin-right: 0px;" onclick="callGetLessons_list(${viewSubjects[i].subject_id}, '0', '${viewSubjects[i].subject_name}')" class="list-group-item '${active1}' tab_class">
                <span style="float:left; font-weight:800; text-align:left;">${viewSubjects[i].subject_name}</span>`;
        if (is_semester_scheme == '1') {
            output += `<br><span class="text-muted"><b>Semester: </b>`;
            if (viewSubjects[i].sem_name) {
                output += `${viewSubjects[i].sem_name}`;
            } else {
                output += `Not Added`;
            }
            output += `</span>`;
        }
        // staff semi details
        output +=
            `<br><span><span style="font-weight: bold;">Primary Teacher:</span> ${viewSubjects[i].staff_name}</span>`;
        output +=
            `<br><span><span style="font-weight: bold;">Assistant Teacher:</span> ${viewSubjects[i].assistant_staff_name}</span>`;
        output += `
                </div>
                </div>
                <div class="col-md-3" style="padding-left: 0px;padding-top: 10px;">
                    <span style="float: right;"><b id="lessonCount_${viewSubjects[i].subject_id}">${viewSubjects[i].TotalLessons}</b> Lesson(s) </span>
                </div>`;
        if (+is_delete_subjects_permission_enabled) {
            onClick = '';
            disabled = 'opacity: 0.3; cursor: not-allowed;';
            if (viewSubjects[i].TotalLessons == 0) {
                onClick = `onclick="delete_subject(${viewSubjects[i].subject_id},'${viewSubjects[i].subject_name}')"`;
                disabled = 'cursor:pointer;';
            }
            output += `
                <div class="col-md-1" style="padding-left: 0px;padding-right: 0px;">
                    <i title="Delete Subject" id="deleteSubjectBtn${viewSubjects[i].subject_id}" ${onClick} class='fa fa-trash-o' style="color:red;font-size:15px;padding-top:10px;${disabled}"></i>
                </div>`;
        }
        output += `</td>`;
        output += '</tr>';
        m++;
    }
    output += '</tbody>';
    output += '</table>';
    return output;
}

function edit_semester(semester_id, subject_id) {
    var subject = $("#subject-" + subject_id);
    var subject_name = subject.data('subject_name');
    $("#subject-semester-id").html('');
    var class_master_id = $("#class_master_id").val();
    $("#addSubjectSemester").modal('show');
    $("#semester-for").html(subject_name);
    $("#semester_subject_id").val(subject_id);
    $.ajax({
        url: '<?php echo site_url('academics/ManageSubjects/get_class_semesters') ?>',
        type: 'post',
        data: {
            'class_master_id': class_master_id
        },
        success: function(data) {
            var data = $.parseJSON(data);
            var semesters = data.semesters;
            var options = '<option value="0">Select Semester</option>';
            for (var i = 0; i < semesters.length; i++) {
                options +=
                    `<option ${semester_id==semesters[i].id?'selected':''} value="${semesters[i].id}">${semesters[i].sem_name}</option>`;
            }
            $("#subject-semester-id").html(options);
        },
        error: function(err) {
            console.log(err);
        }
    });
}

function updateSubjectSemester() {
    $("#update-semester").prop('disabled', true).html('Please Wait...');
    var subject_id = $("#semester_subject_id").val();
    var semester_id = $("#subject-semester-id").val();
    $.ajax({
        url: '<?php echo site_url('academics/ManageSubjects/update_subject_semester') ?>',
        type: 'post',
        data: {
            'subject_id': subject_id,
            'semester_id': semester_id
        },
        success: function(data) {
            $("#addSubjectSemester").modal('hide');
            $("#update-semester").prop('disabled', false).html('Update');
            let parsedData = JSON.parse(data);
            if (data == 1) {
                Swal.fire({
                    icon: 'success',
                    title: 'Semester updated successfully',
                    showConfirmButton: false,
                    timer: 1500
                }).then(() => {
                    callGetSubjects();
                    $("#subject-" + subject_id).click();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Something went wrong!',
                    showConfirmButton: false,
                    timer: 1500
                });
            }
        },
        error: function(err) {
            $("#update-semester").prop('disabled', false).html('Update');
            console.log(err);
            Swal.fire({
                icon: 'error',
                title: 'Something went wrong!',
                showConfirmButton: false,
                timer: 1500
            });
        }
    });
}

function callGetLessons_list(subject_id, check_first, subject_name) {
    $(".right_side_bar").show();
    window.localStorage.setItem("currentSubjectId", subject_id);
    // planScheduleData.subject_id=subject_id;
    planScheduleData.class_master_id = $("#class_master_id").val();

    $(".tab_class").css('background', 'none');
    $("#subject-" + subject_id).css('background', 'skyblue');
    $.ajax({
        url: '<?php echo site_url('academics/ManageSubjects/view_lesson') ?>',
        type: 'post',
        data: {
            'subject_id': subject_id
        },
        beforeSend: function() {
            $('#opacity').css('opacity', '0.5');
            $('#loader').show();
        },
        success: function(data) {
            var data = $.parseJSON(data);
            var viewLessons = data.viewLessons;
            $("#syll_teach_tabs").show();
            $("#syll").css('background', 'skyblue');
            $("#teach").css('background', 'gray');
            $('#information').html(construct_lesson_details(viewLessons, subject_id, subject_name));
        },
        complete: function() {
            const currentTab = window.sessionStorage.getItem("currentTab");
            actionTabs(currentTab);
            $('#loader').hide();
            $('#opacity').css('opacity', '');
        }

    });
}

function construct_lesson_details(viewLessons, subject_id, subject_name) {
    var class_master_id = $("#class_master_id").val();
    $.ajax({
        url: '<?php echo site_url('academics/ManageSubjects/get_sections_and_subject_teachers') ?>',
        type: 'post',
        data: {
            'class_master_id': class_master_id,
            "lp_subject_id": subject_id
        },
        success: function(data) {
            var data = $.parseJSON(data);
            var s = data.secs;
            $("#information").html(lesson_details_table(s, viewLessons, subject_id, subject_name));
            repare_if_possible(subject_id);
        }
    });


}

function repare_if_possible(subject_id) {
    $.ajax({
        url: '<?php echo site_url('academics/ManageSubjects/get_details_if_exist') ?>',
        type: 'post',
        data: {
            'subject_id': subject_id
        },
        success: function(data) {
            var data = $.parseJSON(data).teacher;
            for (var index in data) { // section_access_
                var access = data[index].access_level;
                access = access.charAt(0).toUpperCase() + access.slice(1);
                if (access == 'Read_only') {
                    access = access.replace('_', ' ');
                }

                const deleteBtnIcon =
                    `<i style="color: #d80403;font-size:14px;cursor:pointer" onclick="removeTeacherAssignedToSection(${data[index].lp_subjects_section_staff_id},'${data[index].staff_name}','${data[index].staff_type}',false)" class="fa fa-times"></i>`;

                $(`.${data[index].staff_type}_staff_${data[index].class_section_id}`).html(
                    `<span style="margin-right: 1rem;">${deleteBtnIcon}</span>${data[index].staff_name}`
                    );
                $(`.${data[index].staff_type}_access_${data[index].class_section_id}`).html(`${access}`);
                $(`.${data[index].staff_type}_button_${data[index].class_section_id}`).html(
                    `<span class="fa fa-edit"></span>`);

                $("#flash_storage").append(
                    ` <input type="hidden" value="${data[index].staff_id}" class="_${data[index].staff_type}_staff_${data[index].class_section_id}" /> <input type="hidden" value="${data[index].access_level}" class="_${data[index].staff_type}_access_${data[index].class_section_id}" /> `
                );
            }

        }
    });
}

function getStaffOnstaffType() {
    const type = window.sessionStorage.getItem("type");
    const subject_id = window.sessionStorage.getItem("subject_id");
    const sec_name_id = window.sessionStorage.getItem("sec_name_id");
    const sec_id = window.sessionStorage.getItem("sec_id");

    teacher_assign_modal(type, subject_id, sec_name_id, sec_id);
}

function teacher_assign_modal(type, subject_id, sec_name_id, sec_id = 0) {
    window.sessionStorage.setItem("type", type);
    window.sessionStorage.setItem("subject_id", subject_id);
    window.sessionStorage.setItem("sec_name_id", sec_name_id);
    window.sessionStorage.setItem("sec_id", sec_id);

    var curr_name = '';
    var curr_access = '';
    const staff_type_id = $("#staff_type_id").val();

    $("#class_section_id").val('0');
    // prepare modal before show
    $("#teacher_subject_id").val(subject_id);
    if (type == 'primary') {
        curr_name = $(`._primary_staff_0`).eq(-1).val(); // pre value setting for edit
        curr_access = $(`._primary_access_0`).eq(-1).val(); // pre value setting for edit
        $("#teacher_modal_header").html(`Add / update primary teacher `);
        $("#teacher_type").val('primary');
    } else if (type == 'assistant') {
        curr_name = $(`._assistant_staff_0`).eq(-1).val(); // pre value setting for edit
        curr_access = $(`._assistant_access_0`).eq(-1).val(); // pre value setting for edit
        $("#teacher_modal_header").html(`Add / update assistant teacher `);
        $("#teacher_type").val('assistant');
    } else {
        var sec = sec_name_id.split('__');
        curr_name = $(`._section_staff_${sec[1]}`).eq(-1).val(); // pre value setting for edit
        curr_access = $(`._section_access_${sec[1]}`).eq(-1).val(); // pre value setting for edit
        $("#teacher_modal_header").html(`Add section - ${sec[0]} teacher `);
        $("#class_section_id").val(sec[1]);
        $("#teacher_type").val('section');
    }

    // get staffs
    $.ajax({
        url: '<?php echo site_url('academics/ManageSubjects/get_subject_staffs') ?>',
        type: 'post',
        data: {
            sec_id,
            subject_id,
            staff_type_id
        },
        success: function(data) {
            var data = $.parseJSON(data);
            var html = `<option value="">Select Staff</option>`;
            for (var val of data) {
                html += `<option value="${val.id}">${val.staff_name}</option>`;
            }
            $("#teacher_name").html(html);

            $(`#teacher_name option[value="${curr_name || ''}"]`).prop("selected", true);
            $(`#teacher_accessibility option[value="${curr_access || 'write'}"]`).prop("selected", true);

        }

    });
    $("#flash_storage").html();
    $("#add_teacher_modal").modal('show');
}

function assign_teacher_to_the_subject() {
    var loggedIn_staff_id = '<?php echo $loggedIn_staff_id; ?>';
    var sub_id = $("#teacher_subject_id").val();
    var type = $("#teacher_type").val();
    var sec_id = $("#class_section_id").val();
    var staff_id = $("#teacher_name").val();
    var accessibility = $("#teacher_accessibility").val();
    if (staff_id.trim() == '' || staff_id == null || staff_id == undefined) {
        $("#teacher_name ").parent().children('font').remove();
        $("#teacher_name ").parent().append(`<font color="red">This value is required</font>`);
    } else {
        $.ajax({
            url: '<?php echo site_url('academics/ManageSubjects/assign_teacher_to_the_subject') ?>',
            type: 'post',
            data: {
                'loggedIn_staff_id': loggedIn_staff_id,
                'sub_id': sub_id,
                'type': type,
                'sec_id': sec_id,
                'staff_id': staff_id,
                'accessibility': accessibility
            },
            success: function(data) {
                $("#add_teacher_modal").modal('hide');
                // callGetSubjects();
                let parsedData = JSON.parse(data);
                if (data) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Teacher assigned successfully',
                        showConfirmButton: false,
                        timer: 1500
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Something went wrong',
                        showConfirmButton: false,
                        timer: 1500
                    });
                }
                const currentClickedSubjectId = window.localStorage.getItem("currentSubjectId");
                $(`#subject-${currentClickedSubjectId}`).trigger("click")
                resetForm('Staff')
            },
            error: function(data) {
                $("#add_teacher_modal").modal('hide');
                Swal.fire({
                    icon: 'error',
                    title: 'Something went wrong',
                    showConfirmButton: false,
                    timer: 1500
                });
            }
        });
    }

}

function check_staff_validity() {
    var val = $("#teacher_name ").val();
    if (val) {
        $("#teacher_name ").parent().children('font').remove();
        return;
    } else {
        $("#teacher_name ").parent().children('font').remove();
        $("#teacher_name ").parent().append(`<font color="red">This value is required</font>`);
        return;
    }
}

    function lesson_details_table(sections, viewLessons, subject_id, subject_name) {
        const tab = window.sessionStorage.getItem("currentTab");
        const currentActiveTab = tab != "null" ? tab : 0;

        actionTabs(currentActiveTab);

        var html = '';
        var subject = $("#subject-" + subject_id);
        var subject_name = subject.data('subject_name');
        var semester_id = subject.data('semester_id');
        var semester_name = subject.data('semester_name');
        html += '<div class="d-flex justify-content-between align-items-center mb-3">';
        html += `<h4 class="mb-0">${subject_name}</h4>`;
        html += '<span></span>';
        html += '<div>';
        if (is_semester_scheme == '1') {
            var sem_name = 'Not Added';
            if (semester_name) {
                sem_name = semester_name;
            }
            html +=
                `<span style="cursor: pointer;" onclick="edit_semester(${semester_id}, ${subject_id})"><b>Semester: </b>${sem_name} <i class="fa fa-pencil"></i></span>`;
        }
        html += '</div>';
        html += '</div>';

        html +=
            `<div class="staff_inf_class" id="teach_tab" style="display: ${currentActiveTab==0 && 'none'};"><div style="margin-bottom: 0;" class="modal-header h3">Access Control</div><table class="table table-bordered">`;
        html += '<thead>';
        html += '<tr>';
        html += '<th width="5%">#</th>';
        html += '<th width="11%">Staff Type</th>';
        html += '<th width="39%">Name</th>';
        html += '<th width="15%">Section Name</th>';
        html += `<th width="20%">Access</th>`;
        html += '<th width="10%">Action</th>';
        html += '</tr>';
        html += '</thead>';
        html += '<tbody>';

        html += '<tr>';
        html += '<td>1</td>';
        html += '<td>Primary</td>';
        html += `<td class="primary_staff_0">Not Assigned</td>`;
        html += `<td>NA</td>`;
        html += `<td class="primary_access_0">NA</td>`;
        html +=
            `<td class="text-center"> <button onclick="teacher_assign_modal('primary', ${subject_id}, '')" type="button" class="new_circleShape_res primary_button_0" style="background-color: #fe970a; float: right;"><span class="fa fa-plus"></span></button> </td>`;
        html += '</tr>';
        html += '<tr>';
        html += '<td>2</td>';
        html += '<td>Assistant</td>';
        html += `<td class="assistant_staff_0">Not Assigned</td>`;
        html += `<td>NA</td>`;
        html += `<td class="assistant_access_0">NA</td>`;
        html +=
            `<td class="text-center"> <button onclick="teacher_assign_modal('assistant', ${subject_id}, '')" type="button" class="new_circleShape_res assistant_button_0" style="background-color: #fe970a; float: right;"><span class="fa fa-plus"></span></button> </td>`;
        html += '</tr>';
        sections.forEach((section, i) => {
            if (section.sectionAssigned?.length) {
                section.sectionAssigned.forEach((s, i) => {
                    html += `
                    <tr>
                      <td>${++i}</td>
                      <td>Section</td>
                      <td class="">
                        <i style="color: #d80403;font-size:14px;cursor:pointer;margin-right: 1rem;" onclick="removeTeacherAssignedToSection('${s.id}','${s.staff_name}','${section.section_name}')" class="fa fa-times"></i>${s.staff_name}
                      </td>
                      <td>${section.class_name} ${section.section_name}</td>
                      <td class="section_access_${section.section_id}">NA</td>
                      <td class="text-center">
                        <button onclick="teacher_assign_modal('section', ${subject_id}, '${section.section_name}__${section.section_id}','${section.section_id}')" type="button" class="new_circleShape_res section_button_${section.section_id}" style="background-color: #fe970a; float: right;"><span class="fa fa-plus"></span>
                        </button>
                      </td>
                    <tr>
                  `;
                })
            } else {
                html += `
                  <tr>
                    <td>${++i}</td>
                    <td>Section</td>
                    <td class="section_staff_${section.section_id}">Not Assigned</td>
                    <td>${section.class_name} ${section.section_name}</td>
                    <td class="section_access_${section.section_id}">NA</td>
                    <td class="text-center">
                      <button onclick="teacher_assign_modal('section', ${subject_id}, '${section.section_name}__${section.section_id}','${section.section_id}')" type="button" class="new_circleShape_res section_button_${section.section_id}" style="background-color: #fe970a; float: right;"><span class="fa fa-plus"></span>
                      </button>
                    </td>
                  <tr>
            `;
            }
        })

        html += '</tbody>';
        html += `</table></div>`;

        html += `<div class="syll_inf_class" id="syll_tab" style="display: ${currentActiveTab==1 && 'none'};"><div style="margin-bottom: 0;" class="modal-header h3">Syllabus Plan 
                <div style="direction: rtl;">
                    <button class="new_circleShape_res1" style="background-color: #fe970a; color: white;margin-left: 5px;" onclick="showLessonModal('${subject_id}', '', '', 'add', '${subject_name}')" type="button" class="btn btn-info ml-3" id="addNewLesson${subject_id}"><span class="fa fa-plus"></span>
                    </button>`;
        if (+is_clone_option_permission_enabled) {
            html +=
                `<button class="btn btn-primary" id="cloneSubject" onClick="ShowCloneIndividualSubject()">Clone syllabus plan</button>`;
        }
        html += `</div>
                    </div>
                    <table class="table table-bordered">`;
        html += '<thead>';
        html += '<tr>';
        html += '<th width="3%">#</th>';
        html += '<th width="27%">Lesson</th>';
        html += '<th width="50%">Topic</th>';
        html += '<th width="20%">Action</th>';
        html += '</tr>';
        html += '</thead>';
        html += '<tbody>';
        if (!viewLessons.length) {
            html += `<tr><td colspan="4" class="text-center">No Data Found</td></tr></tbody></table></div>`;
            return html;
        }
        var k = 0;
        for (var i = 0; i < viewLessons.length; i++) {
            var edit_lesson =
                `<i style="color:#131318;font-size:14px;cursor:pointer"  onclick="showLessonModal('${subject_id}','${viewLessons[i].lesson_id}', '${viewLessons[i].lesson_name}', 'edit', '${subject_name}')" class="fa fa-pencil"></i>`;
            html += `
            <tr>
            <td>${(k+1)}</td>
            <td>${edit_lesson} ${(k+1)}- ${viewLessons[i].lesson_name}</td>
            <td>`;

            for (var j = 0; j < viewLessons[i].sub_topic_arr.length; j++) {
                st = viewLessons[i].sub_topic_arr[j];
                var delete_subtopic = '';
                if (st.sessions_count == 0) {
                    //No sessions. Hence, can delete
                    delete_subtopic =
                        `<i style="color:red;font-size:15px;cursor:pointer;"  onclick="delete_subtopic_by_id('${st.sub_topic_id}','${subject_id}','${st.sub_topic_name}', '${viewLessons[i].lesson_name}', '${subject_name}')" class="fa fa-times"></i>`;
                } else {
                    delete_subtopic =
                        `<i style="color:red;opacity: 0.3; cursor: not-allowed;font-size:15px" class="fa fa-times"></i>`;
                }
                var edit_subtopic =
                    `<i style="color:#131318;font-size:14px;cursor:pointer"  onclick="showSubTopicModal('${subject_id}', '${viewLessons[i].lesson_id}', '${st.sub_topic_id}', '${st.sub_topic_name}', 'edit', '${subject_name}', '${viewLessons[i].lesson_name}')" class="fa fa-pencil"></i>`;

                html += `${edit_subtopic} ${delete_subtopic} ${(k+1)}.${(j+1)}- ${st.sub_topic_name} <br>`;
            }
            html += `
            <button style="margin-top:1rem" onclick="showSubTopicModal('${subject_id}','${viewLessons[i].lesson_id}', '', '', 'add', '${subject_name}', '${viewLessons[i].lesson_name}')"  type="button" class="btn btn-info btn-xs">Add Topic</button>
            </td>`;
            var disabledCss = 'style="opacity: 0.3; cursor: not-allowed;"';
            var onclick = '';
            if (viewLessons[i].sub_topic_arr.length == 0) {
                disabledCss = '';
                const lessonName = viewLessons[i].lesson_name.replace(/'/g, "\\'");
                onclick = `onclick="delete_lesson_plan(${subject_id}, ${viewLessons[i].lesson_id}, '${lessonName}', '${subject_name}')"`;
            }

            var baseurl = "<?php echo base_url() ?>";
            //  <a href="${baseurl}/academics/lesson_plan/session_plan_console2/${viewLessons[i].lesson_id}" target="_blank"><span class="label label-success label-form">Goto Session Plans</span></a>
            html += `
            <td>
                <div style="display: flex;justify-content: center;align-items: center;">
                    <a ${onclick} href="javascript:void(0)"><span ${disabledCss}  class="label label-danger label-form">Delete</span></a>
                    <button class="btn btn-primary" onclick="planSchedule('${subject_id}','${viewLessons[i].lesson_id}')" style="margin: 3px 10px;">Plan schedule</button>
                    <button class="btn btn-primary" onclick="designSession('${subject_id}','${viewLessons[i].lesson_id}')" style="margin: 3px 10px;">Design session</button>
                </div>
            </td>`;
            k++;
            html += '<tr>';
        }
        html += '</tbody>';
        html += '</table></div>';
        return html;
    }

function removeTeacherAssignedToSection(assignedSectionStaffId, teacherName, sectionName, isSection = true) {
  if (!+assignedSectionStaffId) {
        Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "Something went wrong!",
        });
        return;
    }
    Swal.fire({
        title: "Are you sure?",
        text: `Removing ${teacherName} from ${isSection ? "section" : ""} ${sectionName}!`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes",
        reverseButtons: true,
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: "<?php echo site_url('academics/ManageSubjects/remove_teacher_assigned_to_section') ?>",
                type: "POST",
                data: {
                    assignedSectionStaffId
                },
                success: function(res) {
                    let parsedData = JSON.parse(res);
                    if(parsedData){
                        Swal.fire({
                            icon: "success",
                            title: "Success",
                            text: "Teacher removed successfully!",
                        }).then(() => {
                            const currentClickedSubjectId = window.localStorage.getItem("currentSubjectId");
                            $(`#subject-${currentClickedSubjectId}`).trigger("click")
                        });
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Oops...",
                            text: "Something went wrong!",
                        });
                    }
                },
                error: function(err) {
                    console.log(err);
                    Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong!",
                    });
                }
            })
        }
    });
}

const planScheduleData = {};

const URLOrigin = window.location.origin;

function planSchedule(subjectId, lessonId) {
    const URL =
        `${URLOrigin}/${URLOrigin.split(".").includes("localhost") && "oxygenv2/" || ""}academics/lesson_plan/manage_session/${planScheduleData.class_master_id}/${subjectId}`;
    window.open(URL, "_blank");

}

function designSession(subjectId, lessonId) {
    const URL =
        `${URLOrigin}/${URLOrigin.split(".").includes("localhost") && "oxygenv2/" || ""}academics/lesson_plan/design_session/${planScheduleData.class_master_id}/${subjectId}/${lessonId}`;
    window.open(URL, "_blank");
}

function getLessons() {
    var subject_id = $("#subject_id_hidden").val();
    $.ajax({
        url: '<?php echo site_url('academics/ManageSubjects/get_lessons'); ?>',
        data: {
            'subject_id': subject_id
        },
        type: 'post',
        success: function(data) {
            var data = $.parseJSON(data);
            var options = '';
            options += '<option value="">Select Lesson</option>';
            for (var i = 0; i < data.length; i++) {
                options += '<option value="' + data[i].id + '">' + data[i].lesson_name + '</option>';
            }
            $("#lesson").html(options);
        }
    });
}

function getSubTopics() {
    var lesson = $("#lesson").val();
    $.ajax({
        url: '<?php echo site_url('academics/ManageSubjects/get_sub_topics'); ?>',
        data: {
            'lesson_id': lesson
        },
        type: 'post',
        success: function(data) {
            var data = $.parseJSON(data);
            var options = '';
            options += '<option vallue="">Select Sub Topic</option>';
            for (var i = 0; i < data.length; i++) {
                options += '<option value="' + data[i].id + '">' + data[i].sub_topic_name + '</option>';
            }
            $("#sub_topic").html(options);
        }
    });
}

function showLessonModal(subject_id, lesson_id, lesson_name, mode, subject_name) {
    $('#ael_subject_id').val(subject_id);
    $('#ael_mode').val(mode);
    $('#add_edit_lesson #subject_name').val(subject_name);
    if (mode == 'add') {
        $('#ael_heading').html(`Add Lesson For <b>Subject ${subject_name}</b>`);
        $('#ael_lesson_name').html('');
        $('#ael_button_text').html('Add');
    } else {
        $('#ael_heading').html(`Edit Lesson For <b>Subject ${subject_name}</b>`);
        $('#ael_lesson_id').val(lesson_id);
        $('#ael_lesson_name').val(lesson_name);
        $('#ael_button_text').html('Edit');
    }

    $('#add_edit_lesson').modal('show');
}

function showSubTopicModal(subject_id, lessonId, topicId, topic_name, mode, subject_name, lesson_name) {
    $('#s_topic_name').val("")
    $('#s_subject_id').val(subject_id);
    $('#s_lesson_id').val(lessonId);
    $('#s_mode').val(mode);
    $('#addSubTopic #subject_name').val(subject_name);

    if (mode == 'edit') {
        $('#s_topic_id').val(topicId);
        $('#s_topic_name').val(topic_name);
        $('#s_button_name').html('Edit Topic');
        $('#s_heading').html(`Edit Topic For <b>Lesson ${lesson_name}</b>`);
    } else {
        $('#s_button_name').html('Add Topic');
        $('#s_heading').html(`Add Topic For <b>Lesson ${lesson_name}</b>`);
    }
    $('#addSubTopic').modal('show');
}


function addLesson() {
    // var lesson_name = $('#addLesson').modal('show');
    var lesson_name = $('#ael_lesson_name').val();
    var subject_id = $("#ael_subject_id").val();
    var subject_name = $('#addSubTopic #subject_name').val();
    var $form = $('#form_addLesson');
    if ($form.parsley().validate()) {
        $("#add_edit_lesson").modal('hide');
        var form = $('#form_addLesson')[0];
        var formData = new FormData(form);
        $.ajax({
            url: '<?php echo site_url('academics/ManageSubjects/add_edit_lesson'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                if (data) {
                    var data = JSON.parse(data);
                    if (data == -1) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Lesson name already exists!',
                        });
                    } else if (data == 0) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Something went wrong!',
                        });
                    } else {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: 'Lesson added Successfully',
                        }).then(() => {
                            let subjectDeleteButton = $(`#deleteSubjectBtn${subject_id}`);
                            subjectDeleteButton.off('click'); // removes any attached click handler
                            subjectDeleteButton.css({
                                'cursor': 'not-allowed',
                                'opacity': '0.5',
                                'pointer-events': 'none'
                            });
                            $('#form_addLesson')[0].reset();
                            callGetLessons_list(subject_id, '0', subject_name);
                            let lessonCount = $(`#lessonCount_${subject_id}`).html();
                            lessonCount = +lessonCount + 1;
                            $(`#lessonCount_${subject_id}`).html(lessonCount);
                            resetForm('Lesson')
                        });
                    }
                }
            },
            error: function(data) {
                console.log(data);
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Something went wrong!',
                });
            }
        });
    } else {
        return false;
    }
}

function addSubTopic() {
    var $form = $('#form_addSubTopic');
    var subject_id = $("#s_subject_id").val();
    var subject_name = $('#addSubTopic #subject_name').val();

    if ($form.parsley().validate()) {
        $("#addSubTopic").modal('hide');
        var form = $('#form_addSubTopic')[0];
        var formData = new FormData(form);
        $.ajax({
            url: '<?php echo site_url('academics/ManageSubjects/add_edit_topic'); ?>',
            type: 'post',
            data: formData,
            processData: false,
            contentType: false,
            success: function(data) {
                data = JSON.parse(data);
                if (data == -1) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Topic already exists'
                    });
                } else if (data == 0) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Something went wrong!'
                    });
                } else {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: 'Topic Added/Edited'
                    }).then(()=>{
                        $('#subtopic_name_modal').val('');
                        callGetLessons_list(subject_id, '0', subject_name);
                        resetForm('Topic')
                    });
                }
            },
            error: function(err){
                console.log(err);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Something went wrong!'
                });
            }
        });
    }
}

function delete_subtopic_by_id(sub_topic_id, subject_id, topic_name, lesson_name, subject_name) {
    Swal.fire({
        title: 'Are you sure?',
        html: `<div style="font-size: 18px;">Delete Topic <strong>${topic_name}</strong> From Lesson <strong>${lesson_name}</strong>?</div>`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo site_url('academics/ManageSubjects/delete_sub_topic_by_id'); ?>',
                data: {
                    'sub_topic_id': sub_topic_id
                },
                type: 'post',
                success: function(data) {
                    let parsedData = JSON.parse(data);
                    if(parsedData){
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: 'Topic Deleted',
                        }).then(() => {
                            callGetLessons_list(subject_id, '0', subject_name);
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Something went wrong!',
                        })
                    }
                },
                error: function(data) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            });
        }
    })
}

function delete_lesson_plan(subject_id, lesson_id, lesson_name, subject_name) {
    Swal.fire({
        title: 'Are you sure?',
        html: `<div style="font-size: 18px;">Delete Lesson <strong>${lesson_name}</strong> From Subject <strong>${subject_name}</strong>?</div>`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo site_url('academics/ManageSubjects/delete_lesson_by_id'); ?>',
                data: {
                    'lesson_id': lesson_id
                },
                type: 'post',
                success: function(data) {
                    let parsedData = JSON.parse(data);
                    if(parsedData){
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: 'Lesson Deleted',
                        }).then(() => {
                            let lessonCount = $(`#lessonCount_${subject_id}`).html();
                            lessonCount = +lessonCount - 1;
                            $(`#lessonCount_${subject_id}`).html(lessonCount);
                            if(lessonCount == 0){
                                let subjectDeleteButton = $(`#deleteSubjectBtn${subject_id}`);
                                subjectDeleteButton.on('click', function() {
                                    delete_subject(subject_id, subject_name);
                                });
                                subjectDeleteButton.css({
                                    'cursor': 'pointer',
                                    'opacity': '1',
                                    'pointer-events': 'auto'
                                });
                            }
                            callGetLessons_list(subject_id, '0', subject_name);
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Something went wrong!',
                        })
                    }
                },
                error: function(data) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong!',
                    })
                }
            });
        }
    })
}
</script>