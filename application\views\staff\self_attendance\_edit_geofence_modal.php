<div class="modal fade" id="edit_geofence" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal_title">Edit Geo-fence</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form enctype="multipart/form-data" data-parsley-validate="" method="post" class="form-horizontal"
                    id="edit_locations_form">
                    <div class="modal-body" style="">
                        <section class="first_half" style="">
                            <div class="form-group">
                                <div class="">
                                    <label class="control-label" for="edit_geo_fence_name">Enter Geo-fence Name <span
                                        style="color:red;">*</span></label>
                                    <input id="edit_geo_fence_name" name="edit_geo_fence_name" type="text"
                                        class="form-control" required="" placeholder="Enter Geo-fence Name">
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="">
                                    <label class="control-label" for="edit_latitude">Enter Latitude <span
                                        style="color:red;">*</span></label>
                                    <input id="edit_latitude" name="edit_latitude" type="number" step="0.25"
                                        class="form-control" required="" placeholder="Enter Latitude">
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="">
                                    <label class="control-label" for="edit_longitude">Enter Longitude <span
                                        style="color:red;">*</span></label>
                                    <input id="edit_longitude" name="edit_longitude" type="number" step="0.25"
                                        class="form-control" required="" placeholder="Enter Longitude">
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="">
                                    <label class="control-label" for="edit_radius">Enter Radius(In meter) <span
                                        style="color:red;">*</span></label>
                                    <input id="edit_radius" name="edit_radius" type="number" step="0.25"
                                        class="form-control" required="" placeholder="Enter Radius">
                                </div>
                            </div>
                        </section>
                    </div>
                    <div class="bg-light" style="text-align:center;">
                        <button type="button" class="btn btn-primary m-2" id="create_btn"
                            onclick="editGeofence()">Update</button>
                        <button type="button" class="btn btn-secondary" id="close_btn"
                            data-dismiss="modal">Close</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    let fenceId, geoFenceName, latitude, longitude, radius;
    $("#edit_geofence").on("shown.bs.modal", e => {
        const dataSet = e.relatedTarget.dataset;
        fenceId = dataSet.fence_id;
        geoFenceName = dataSet.geo_fence_name;
        latitude = dataSet.latitude;
        longitude = dataSet.longitude;
        radius = dataSet.radius;

        $("#edit_geo_fence_name").val(geoFenceName);
        $("#edit_latitude").val(latitude);
        $("#edit_longitude").val(longitude);
        $("#edit_radius").val(radius);
    })

    function editGeofence() {
        const geofence = {
            fenceId,
            geoFenceName: $("#edit_geo_fence_name").val(),
            latitude: $("#edit_latitude").val(),
            longitude: $("#edit_longitude").val(),
            radius: $("#edit_radius").val()
        }

        let errors = [];

        if (geofence.geoFenceName === "") {
            errors.push("Geo-fence Name is required.");
        } else if (geofence.geoFenceName.length > 50) {
            errors.push("Geo-fence Name must be at most 50 characters.");
        }
        if (isNaN(geofence.latitude) || geofence.latitude=="" || geofence.latitude < -90 || geofence.latitude > 90) {
            errors.push("Latitude must be between -90 and 90.");
        }
        if (isNaN(geofence.longitude) || geofence.longitude=="" || geofence.longitude < -180 || geofence.longitude > 180) {
            errors.push("Longitude must be between -180 and 180.");
        }
        if (isNaN(geofence.radius) || geofence.radius=="" || geofence.radius < 1 || geofence.radius > 10000) {
            errors.push("Radius must be between 1 and 10000 meters.");
        }

        if (errors.length > 0) {
            // Add SweetAlert for alert
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                html: errors.join('<br>')
            });
            return;
        }

        let forms = $('#edit_locations_form');
        if (forms.parsley().validate()) {
            $.ajax({
                url: "<?php echo site_url('staff/attendance/edit_geofence') ?>",
                type: "POST",
                data: geofence,
                success(data) {
                    get_staff_attendance_locations();
                    $("#edit_geofence").trigger("click");

                    if (data) {
                        // Add SweetAlert for success
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            html: `Updated <b>${geofence.geoFenceName}</b> Geo-fence successfully`
                        });
                    }
                }
            })
        }
    }
</script>