<?php

class Dashboard extends CI_Controller {
  private $avatar_type;

  public $columnList = [    
    [
      'displayName'=>'Date of Birth',
      'columnNameWithTable' => 'DATE_FORMAT(sm.dob, "%d-%m-%Y")',
      'columnName'=>'dob',
      'varName'=>'sDOB',
      'table'=>'staff_master',
      'index'=>'2',
      'displayType'=>'text',
      'dataType'=>'date'
    ],
    [
      'displayName'=>'Designation',
      'columnNameWithTable'=>'sm.designation',
      'columnName'=>'designation',
      'varName'=>'designation',
      'table'=>'staff_master',
      'index'=>'3',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Department',
      'columnNameWithTable'=>'sm.department',
      'columnName'=>'department',
      'varName'=>'department',
      'table'=>'staff_master',
      'index'=>'4',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Father Name',
      'columnNameWithTable'=>'concat(ifnull(sm.father_first_name,""), " ", ifnull(sm.father_last_name,""))',
      'columnName'=>'father_name',
      'varName'=>'sFatherName',
      'table'=>'staff_master',
      'index'=>'5',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Mother Name',
      'columnNameWithTable'=>'concat(ifnull(sm.mother_first_name,""), " ", ifnull(sm.mother_last_name,""))',
      'columnName'=>'mother_name',
      'varName'=>'smotherName',
      'table'=>'staff_master',
      'index'=>'6',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Martial Status',
      'columnNameWithTable'=>'sm.marital_status',
      'columnName'=>'marital_status',
      'varName'=>'marital_status',
      'table'=>'staff_master',
      'index'=>'7',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
     [
      'displayName'=>'Spouse Name',
      'columnNameWithTable'=>'sm.spouse_name',
      'columnName'=>'spouse_name',
      'varName'=>'spouseName',
      'table'=>'staff_master',
      'index'=>'8',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Nationality',
      'columnNameWithTable'=>'sm.nationality',
      'columnName'=>'nationality',
      'varName'=>'nationality',
      'table'=>'staff_master',
      'index'=>'9',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Gender',
      'columnNameWithTable'=>'sm.gender',
      'columnName'=>'gender',
      'varName'=>'gender',
      'table'=>'staff_master',
      'index'=>'10',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

     [
      'displayName'=>'Aadhar Number',
      'columnNameWithTable'=>'sm.aadhar_number',
      'columnName'=>'aadhar_number',
      'varName'=>'AdNumber',
      'table'=>'staff_master',
      'index'=>'11',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

     [
      'displayName'=>'Qualification',
      'columnNameWithTable'=>'sm.qualification',
      'columnName'=>'qualification',
      'varName'=>'qualification',
      'table'=>'staff_master',
      'index'=>'12',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

     [
      'displayName'=>'Subject Specialization',
      'columnNameWithTable'=>'sm.subject_specialization',
      'columnName'=>'subject_specialization',
      'varName'=>'subject_specialization',
      'table'=>'staff_master',
      'index'=>'13',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Total Experience',
      'columnNameWithTable'=>'sm.total_experience',
      'columnName'=>'total_experience',
      'varName'=>'total_experience',
      'table'=>'staff_master',
      'index'=>'14',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Total Education Experience',
      'columnNameWithTable'=>'sm.total_education_experience',
      'columnName'=>'total_education_experience',
      'varName'=>'total_eductation_experience',
      'table'=>'staff_master',
      'index'=>'15',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
   
    [
      'displayName'=>'Alternative Number',
      'columnNameWithTable'=>'sm.alternative_number',
      'columnName'=>'alternative_number',
      'varName'=>'alternative_number',
      'table'=>'staff_master',
      'index'=>'16',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Spouse Contact Number',
      'columnNameWithTable'=>'sm.spouse_contact_no',
      'columnName'=>'spouse_contact_no',
      'varName'=>'spouse_contact_number',
      'table'=>'staff_master',
      'index'=>'17',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Date of Joining',
      'columnNameWithTable' => 'DATE_FORMAT(sm.joining_date, "%d-%m-%Y")',
      'columnName'=>'joining_date',
      'varName'=>'staffDOJ',
      'table'=>'staff_master',
      'index'=>'18',
      'displayType'=>'text',
      'dataType'=>'date'
    ],

     [
      'displayName'=>'User Name',
      'columnNameWithTable' => 'u.username',
      'columnName'=>'username',
      'varName'=>'uName',
      'table'=>'users',
      'index'=>'19',
      'displayType'=>'text',
      'dataType'=>'string'
    ],

    [
      'displayName'=>'Email',
      'columnNameWithTable' => 'u.email',
      'columnName'=>'email',
      'varName'=>'email',
      'table'=>'users',
      'index'=>'20',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName' => ' Personal Email',
      'columnNameWithTable' => 'sm.personal_mail_id',
      'columnName' => 'personal_mail_id',
      'varName' => 'personal_mail_id',
      'table' => 'staff_master',
      'index' => '70',
      'displayType' => 'text',
      'dataType' => 'string'
    ],
     [
      'displayName'=>'Profile Confirmed',
      'columnNameWithTable' => 'profile_confirmed',
      'columnName'=>'profile_confirmed',
      'varName'=>'profile_confirmed',
      'table'=>'staff_master',
      'index'=>'21',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'ESI Number',
      'columnNameWithTable' => 'npm.esi_number',
      'columnName'=>'esi_number',
      'varName'=>'esi_number',
      'table'=>'new_payroll_master npm',
      'index'=>'22',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'UAN Number',
      'columnNameWithTable' => 'npm.uan_number',
      'columnName'=>'uan_number',
      'varName'=>'uan_number',
      'table'=>'new_payroll_master npm',
      'index'=>'23',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
     [
      'displayName'=>'PF Number',
      'columnNameWithTable' => 'npm.pf_number',
      'columnName'=>'pf_number',
      'varName'=>'pf_number',
      'table'=>'new_payroll_master npm',
      'index'=>'24',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'PAN Details',
      'columnNameWithTable' => 'npm.pan_number',
      'columnName'=>'pan_number',
      'varName'=>'pan_number',
      'table'=>'new_payroll_master npm',
      'index'=>'29',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Blood Group',
      'columnNameWithTable'=>'sm.blood_group',
      'columnName'=>'blood_group',
      'varName'=>'blood_group',
      'table'=>'staff_master',
      'index'=>'30',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Staff Type',
      'columnNameWithTable' => 'staff_type',
      'columnName'=>'staff_type',
      'varName'=>'staff_type',
      'table'=>'staff_master',
      'index'=>'31',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    //new
    [
      'displayName'=>'Emergency Info',
      'columnNameWithTable' => 'emergency_info',
      'columnName'=>'emergency_info',
      'varName'=>'emergency_info',
      'table'=>'staff_master',
      'index'=>'32',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Math high grade',
      'columnNameWithTable' => 'math_high_grade',
      'columnName'=>'math_high_grade',
      'varName'=>'math_high_grade',
      'table'=>'staff_master',
      'index'=>'33',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'English high grade',
      'columnNameWithTable' => 'english_high_grade',
      'columnName'=>'english_high_grade',
      'varName'=>'english_high_grade',
      'table'=>'staff_master',
      'index'=>'34',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Social high grade',
      'columnNameWithTable' => 'social_high_grade',
      'columnName'=>'social_high_grade',
      'varName'=>'social_high_grade',
      'table'=>'staff_master',
      'index'=>'35',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Trained to teach',
      'columnNameWithTable' => 'trained_to_teach',
      'columnName'=>'trained_to_teach',
      'varName'=>'trained_to_teach',
      'table'=>'staff_master',
      'index'=>'36',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
     [
      'displayName'=>'Appointed subject',
      'columnNameWithTable' => 'appointed_subject',
      'columnName'=>'appointed_subject',
      'varName'=>'appointed_subject',
      'table'=>'staff_master',
      'index'=>'37',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
     [
      'displayName'=>'Classes taught',
      'columnNameWithTable' => 'classes_taught',
      'columnName'=>'classes_taught',
      'varName'=>'classes_taught',
      'table'=>'staff_master',
      'index'=>'38',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
     [
      'displayName'=>'Main sub taught',
      'columnNameWithTable' => 'main_sub_taught',
      'columnName'=>'main_sub_taught',
      'varName'=>'main_sub_taught',
      'table'=>'staff_master',
      'index'=>'39',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Add sub taught',
      'columnNameWithTable' => 'add_sub_taught',
      'columnName'=>'add_sub_taught',
      'varName'=>'add_sub_taught',
      'table'=>'staff_master',
      'index'=>'40',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Boarding',
      'columnNameWithTable' => 'boarding',
      'columnName'=>'boarding',
      'varName'=>'boarding',
      'table'=>'staff_master',
      'index'=>'41',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Last working day',
      'columnNameWithTable' => 'DATE_FORMAT(sm.last_working_day, "%d-%m-%Y")',
      'columnName'=>'last_working_day',
      'varName'=>'last_working_day',
      'table'=>'staff_master',
      'index'=>'43',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Voter id',
      'columnNameWithTable' => 'voter_id',
      'columnName'=>'voter_id',
      'varName'=>'voter_id',
      'table'=>'staff_master',
      'index'=>'44',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Height',
      'columnNameWithTable' => 'height',
      'columnName'=>'height',
      'varName'=>'height',
      'table'=>'staff_master',
      'index'=>'45',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Weight',
      'columnNameWithTable' => 'weight',
      'columnName'=>'weight',
      'varName'=>'weight',
      'table'=>'staff_master',
      'index'=>'46',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Allergies',
      'columnNameWithTable' => 'allergies',
      'columnName'=>'allergies',
      'varName'=>'allergies',
      'table'=>'staff_master',
      'index'=>'47',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Medical issues',
      'columnNameWithTable' => 'medical_issues',
      'columnName'=>'medical_issues',
      'varName'=>'medical_issues',
      'table'=>'staff_master',
      'index'=>'48',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Identification mark',
      'columnNameWithTable' => 'identification_mark',
      'columnName'=>'identification_mark',
      'varName'=>'identification_mark',
      'table'=>'staff_master',
      'index'=>'49',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Person with disability',
      'columnNameWithTable' => 'person_with_disability',
      'columnName'=>'person_with_disability',
      'varName'=>'person_with_disability',
      'table'=>'staff_master',
      'index'=>'50',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Passport number',
      'columnNameWithTable' => 'passport_number',
      'columnName'=>'passport_number',
      'varName'=>'passport_number',
      'table'=>'staff_master',
      'index'=>'51',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Passport place of issue',
      'columnNameWithTable' => 'passport_place_of_issue',
      'columnName'=>'passport_place_of_issue',
      'varName'=>'passport_place_of_issue',
      'table'=>'staff_master',
      'index'=>'52',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Passport date of issue',
      'columnNameWithTable' => 'DATE_FORMAT(sm.passport_date_of_issue, "%d-%m-%Y")',
      'columnName'=>'passport_date_of_issue',
      'varName'=>'passport_date_of_issue',
      'table'=>'staff_master',
      'index'=>'53',
      'displayType'=>'text',
      'dataType'=>'date'
    ],
    [
      'displayName'=>'Passport expiry date',
      'columnNameWithTable' => 'DATE_FORMAT(sm.passport_expiry_date, "%d-%m-%Y")',
      'columnName'=>'passport_expiry_date',
      'varName'=>'passport_expiry_date',
      'table'=>'staff_master',
      'index'=>'61',
      'displayType'=>'text',
      'dataType'=>'date'
    ],
    [
      'displayName'=>'Visa details',
      'columnNameWithTable' => 'visa_details',
      'columnName'=>'visa_details',
      'varName'=>'visa_details',
      'table'=>'staff_master',
      'index'=>'57',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Religion',
      'columnNameWithTable' => 'religion',
      'columnName'=>'religion',
      'varName'=>'religion',
      'table'=>'staff_master',
      'index'=>'58',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Nature of appointment',
      'columnNameWithTable' => 'nature_of_appointment',
      'columnName'=>'nature_of_appointment',
      'varName'=>'nature_of_appointment',
      'table'=>'staff_master',
      'index'=>'59',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
    [
      'displayName'=>'Has completed any bed',
      'columnNameWithTable' => 'has_completed_any_bed',
      'columnName'=>'has_completed_any_bed',
      'varName'=>'has_completed_any_bed',
      'table'=>'staff_master',
      'index'=>'60',
      'displayType'=>'text',
      'dataType'=>'string'
    ],
  ];

  function __construct(){
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('MSM')) {
      redirect('dashboard');
    }
    $this->avatar_type = $this->authorization->getAvatarType();
    //if not staf or super admin
    if($this->avatar_type != 3 && $this->avatar_type != 4) {
        redirect('dashboard');
    }
		$this->load->model('msm/msm_model','msm_model');
    $this->load->library('filemanager');
  }

  public function index() {
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');

    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');

    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_landing_page';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_landing_page';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  public function todays_feed(){
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');

    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');

    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_todays_feed';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_todays_feed';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  public function admission(){
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());

    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_admission';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_admission';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  public function fees(){
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());

    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_fees';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_fees';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  public function student(){
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());

    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_student';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_student';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  public function staff(){
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());

    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_staff';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_staff';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  public function parent(){
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());

    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_parent_ticketing';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_parent_ticketing';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  public function infirmary(){
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());

    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_infirmary';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_infirmary';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }
  
  public function internal(){
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());

    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_internal';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_internal';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  public function boarding(){
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());

    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_boarding';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_boarding';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  public function staff_report(){
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $this->columnList = array_merge($this->columnList,$this->__prepareAddressFields());
    $this->columnList = array_merge($this->columnList,$this->__prepareStaffCustomFields());

    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    $data['columnList_json'] = json_encode($this->columnList);
    $data['columnList'] = $this->columnList;
    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_staff_report';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_staff_report';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  public function staff_transfer(){
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $this->columnList = array_merge($this->columnList,$this->__prepareAddressFields());
    $this->columnList = array_merge($this->columnList,$this->__prepareStaffCustomFields());

    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    $data['columnList_json'] = json_encode($this->columnList);
    $data['columnList'] = $this->columnList;
    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_staff_transfer';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_staff_transfer';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  public function institution(){
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());

    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    $data['columnList_json'] = json_encode($this->columnList);
    $data['columnList'] = $this->columnList;
    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_landing_institution';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_landing_institution';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  public function report(){
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());


    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    $data['columnList_json'] = json_encode($this->columnList);
    $data['columnList'] = $this->columnList;
    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_landing_report';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_landing_report';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  public function staff_leaves_data() {
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['staff_ids_for_schools'] = json_encode($this->msm_model->get_staff_id_from_userId($this->authorization->getUserId()));
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data["leave_status_list"]=["Pending","Approved","Auto Approved","Rejected","Cancelled"];
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    $data['isLeaveAdmin'] = (int)$this->authorization->isAuthorized('LEAVE.STAFF_LEAVE_ADMIN');
    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');

    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/_staff_leave_approve';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/_staff_leave_approve';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  public function manage_idcard_orders() {

    // Get filter parameters
    $orderStatus = $this->input->get('order_status') !== null ? $this->input->get('order_status') : 'all';
    $paymentStatus = $this->input->get('payment_status') !== null ? $this->input->get('payment_status') : 'all';
    $idCardType = $this->input->get('id_card_type') !== null ? $this->input->get('id_card_type') : 'all';
    $search = $this->input->get('search') !== null ? $this->input->get('search') : '';

    $filters = [
        'order_status' => $orderStatus,
        'payment_status' => $paymentStatus,
        'id_card_type' => $idCardType,
        'search' => $search
    ];

    // Get summary counts for dashboard
    $data['summary'] = $this->msm_model->get_idcard_admin_summary_counts();

    // Set filter values for the view
    $data['filters'] = $filters;

    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list('all_data'));
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    $data['school_logo'] = $this->settings->getSetting('school_logo');

    if ($this->mobile_detect->isTablet()) {
        $data['main_content']    = 'msm_v3/desktop/manage_idcard_orders_view';
    } else if ($this->mobile_detect->isMobile()) {
        $data['main_content']    = 'msm_v3/desktop/no_page';
    } else {
        $data['main_content']    = 'msm_v3/desktop/manage_idcard_orders_view';
    }
    $this->load->view('msm_v3/inc/template', $data);
  }

  /**
   * Get orders via AJAX
   */
  public function get_idcard_orders() {
    // Get filter parameters
    $orderStatus = $this->input->get('order_status') !== null ? $this->input->get('order_status') : 'all';
    $paymentStatus = $this->input->get('payment_status') !== null ? $this->input->get('payment_status') : 'all';
    $idCardType = $this->input->get('id_card_type') !== null ? $this->input->get('id_card_type') : 'all';
    $search = $this->input->get('search') !== null ? $this->input->get('search') : '';

    // Get pagination parameters
    $page = $this->input->get('page') !== null ? $this->input->get('page') : 1;
    $limit = $this->input->get('limit') !== null ? $this->input->get('limit') : 10;

    $filters = [
        'order_status' => $orderStatus,
        'payment_status' => $paymentStatus,
        'id_card_type' => $idCardType,
        'search' => $search
    ];

    // Get orders with filters
    $orders = $this->msm_model->get_idcard_admin_orders($filters);

    // Get summary counts for dashboard
    $summary = $this->msm_model->get_idcard_admin_summary_counts();

    // Return JSON response
    $response = [
        'success' => true,
        'orders' => $orders,
        'summary' => $summary,
        'total' => count($orders),
        'page' => (int)$page,
        'limit' => (int)$limit,
        'total_pages' => ceil(count($orders) / (int)$limit)
    ];

    echo json_encode($response);
}


  /**
   * View order details
   */
  public function view_idcard_order($id, $msm_school_list_id = null) {

    // If msm_school_list_id is provided, get school details
    if ($msm_school_list_id !== null) {
        $data['school_details'] = $this->msm_model->get_school_details($msm_school_list_id);
    }

    $school_code = $data['school_details']->school_code;
    $school_domain = $data['school_details']->school_domain;
    // Check if we need to fetch from a specific school
    if (!empty($school_code)) {
        // Prepare data for bridge call
        $bridgeData = [
            'school_code' => $school_code,
            'school_domain' => $school_domain,
            'order_id' => $id,
            'api' => 'get_idcard_order_details'
        ];
        // Call bridge_idcards to get order details
        $bridgeResponse = $this->bridge_idcards($bridgeData);
        if (isset($bridgeResponse['status'])) {
            // Parse the response JSON string
            $responseStr = $bridgeResponse['response'];

            // Remove any leading/trailing whitespace
            $responseStr = trim($responseStr);

            // Parse the response
            $orderData = json_decode($responseStr, true);

            if ($orderData) {
                // Create an object to hold the order data
                $data['order'] = (object)$orderData;

                // Add school info to the order
                $data['order']->school_code = $school_code;
                $data['order']->school_domain = $school_domain;

                // Get status counts (we'll fetch this separately)
                $data['status_counts'] = [
                    'total' => isset($orderData['quantity']) ? intval($orderData['quantity']) : 0,
                    'in_review' => 0,
                    'approved' => 0,
                    'modify' => 0,
                    'removed' => 0
                ];
                $data['order_msm'] = $this->msm_model->get_idcard_admin_order_by_id($id);
            } else {
                // If bridge call failed, redirect back with error
                $this->session->set_flashdata('error', 'Failed to parse order details');
                redirect('msm_v3/Dashboard/manage_idcard_orders');
            }
        } else {
            // If bridge call failed, redirect back with error
            $this->session->set_flashdata('error', 'Failed to connect to school: ' . $bridgeResponse['message']);
            redirect('msm_v3/Dashboard/manage_idcard_orders');
        }
    } else {
        // Get order details from local database
        $data['order'] = $this->msm_model->get_idcard_admin_order_by_id($id);

        if (!$data['order']) {
            $this->session->set_flashdata('error', 'Order not found');
            redirect('msm_v3/Dashboard/manage_idcard_orders');
        }

        // Get status counts
        $this->load->model('idcards/Idcards_model');
        $data['status_counts'] = $this->Idcards_model->get_order_status_counts($data['order']->order_id);
    }

    // Common data
    $data['school_list'] = $this->msm_model->get_restricted_school_list('all_data');
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['acad_year'] = $this->settings->getSetting('academic_year_id');
    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    $data['school_logo'] = $this->settings->getSetting('school_logo');

    // Load view
    $data['main_content'] = 'idcard/admin_order_details_view';
    $this->load->view('msm_v3/inc/template', $data);
  }

  /**
   * Upload invoice for an order
   */
  public function upload_idcard_invoice() {
    // Check if request is POST
    if ($this->input->server('REQUEST_METHOD') !== 'POST') {
        redirect('msm_v3/Dashboard/manage_idcard_orders');
    }
    // Get order ID
    $orderId = $this->input->post('order_id');
    $orderId_school = $this->input->post('order_id_school');
    // Check if order exists
    $order = $this->msm_model->get_idcard_admin_order_by_id($orderId_school);
    if (!$order) {
        $this->session->set_flashdata('error', 'Order not found');
        redirect('msm_v3/Dashboard/manage_idcard_orders');
    }

    // Upload invoice file
    $config['upload_path'] = './uploads/idcards/invoices/';
    $config['allowed_types'] = 'pdf|jpg|jpeg|png';
    $config['max_size'] = 5120; // 5MB
    $config['encrypt_name'] = TRUE;

    // Create directory if it doesn't exist
    if (!is_dir($config['upload_path'])) {
        mkdir($config['upload_path'], 0777, TRUE);
    }

    $this->load->library('upload', $config);

    // if (!$this->upload->do_upload('invoice_file')) {
    //     $this->session->set_flashdata('error', $this->upload->display_errors());
    //     redirect('msm_v3/Dashboard/view_idcard_order/' . $orderId);
    // }

    // Get uploaded file info
    $uploadData = $this->upload->data();
    $filePath = 'uploads/idcards/invoices/' . $uploadData['file_name'];

    // Prepare invoice data
    $invoiceData = [
        'invoice_number' => $this->input->post('invoice_number'),
        'invoice_amount' => $this->input->post('invoice_amount'),
        'invoice_cgst' => $this->input->post('invoice_cgst'),
        'invoice_sgst' => $this->input->post('invoice_sgst'),
        'invoice_total' => $this->input->post('invoice_total'),
        'invoice_file_path' => $filePath
    ];

    // Update order with invoice data
    $result = $this->msm_model->upload_idcard_admin_invoice($orderId, $invoiceData);

    if ($result) {
        $msm_school_list_id = $this->input->post('msm_school_list_id');
        if ($msm_school_list_id !== null) {
            $data['school_details'] = $this->msm_model->get_school_details($msm_school_list_id);
        }
    
        $school_code = $data['school_details']->school_code;
        $school_domain = $data['school_details']->school_domain;
        $bridgeData = [
          'school_code' => $school_code,
          'school_domain' => $school_domain,
          'payment_status' =>'invoice_sent',
          'order_id'=>$orderId_school,
          'api' => 'update_invoice_payment_status'
        ];
        $this->bridge_idcards($bridgeData);
        // $this->session->set_flashdata('success', 'Invoice uploaded successfully');

    } else {
        $this->session->set_flashdata('error', 'Failed to upload invoice');
    }

    // Get school code and domain from POST if available
    $schoolCode = $this->input->post('school_code');

    if (!empty($schoolCode)) {
        redirect('msm_v3/Dashboard/view_idcard_order/' . $orderId_school . '/' . $schoolCode);
    } else {
        redirect('msm_v3/Dashboard/view_idcard_order/' . $orderId_school);
    }
  }

  /**
   * Mark payment as complete for an order
   */
  public function mark_idcard_payment_complete($id,$school_id) {
    // Check if order exists
    $order = $this->msm_model->get_idcard_admin_order_by_id($id);
    if (!$order) {
        $this->session->set_flashdata('error', 'Order not found');
        redirect('msm_v3/Dashboard/manage_idcard_orders');
    }

    // Check if invoice has been uploaded
    if ($order->payment_status !== 'invoice_sent') {
        $this->session->set_flashdata('error', 'Invoice must be uploaded before marking payment as complete');
        redirect('msm_v3/Dashboard/view_idcard_order/' . $id);
    }

    // Mark payment as complete
    $result = $this->msm_model->mark_idcard_admin_payment_complete($id);

    if ($result) {
        if ($school_id !== null) {
          $data['school_details'] = $this->msm_model->get_school_details($school_id);
      }
      $school_code = $data['school_details']->school_code;
      $school_domain = $data['school_details']->school_domain;
      $bridgeData = [
        'school_code' => $school_code,
        'school_domain' => $school_domain,
        'payment_status' =>'payment_complete',
        'order_id'=>$id,
        'api' => 'update_invoice_payment_status'
      ];
      $this->bridge_idcards($bridgeData);
      $this->session->set_flashdata('success', 'Payment marked as complete');

    } else {
        $this->session->set_flashdata('error', 'Failed to mark payment as complete');
    }

    redirect('msm_v3/Dashboard/view_idcard_order/' . $id.'/'.$school_id);
  }

  /**
   * Download PDF for printing
   */
  public function download_idcard_print_pdf($id) {

    // Check if order exists
    $order = $this->msm_model->get_idcard_admin_order_by_id($id);
    if (!$order) {
        $this->session->set_flashdata('error', 'Order not found');
        redirect('msm_v3/Dashboard/manage_idcard_orders');
    }

    // Load Idcards model
    $this->load->model('idcards/Idcards_model');

    // Check if PDF exists
    $pdfInfo = $this->Idcards_model->check_idcard_pdf_status($order->order_id, 'front');

    if (!$pdfInfo || !$pdfInfo->front_page_pdf_path) {
        $this->session->set_flashdata('error', 'PDF not found or not generated yet');
        redirect('msm_v3/Dashboard/view_idcard_order/' . $id);
    }

    // Download the PDF
    $pdfPath = $pdfInfo->front_page_pdf_path;

    // If the PDF is stored in S3, get the file from S3
    if (strpos($pdfPath, 'http') === 0) {
        // Redirect to the S3 URL
        redirect($pdfPath);
    } else {
        // Local file, force download
        $this->load->helper('download');
        force_download($pdfPath, NULL);
    }
  }

  /**
   * Send order for printing
   */
  public function send_idcard_for_printing() {
    // Get order ID
    $orderId = $this->input->post('order_id');
    $orderIdSchool = $this->input->post('order_id_school');
    $estimated_print_date= $this->input->post('estimated_print_date');
    // Check if order exists
    $order = $this->msm_model->get_idcard_admin_order_by_id($orderIdSchool);
    if (!$order) {
        $this->session->set_flashdata('error', 'Order not found');
        redirect('msm_v3/Dashboard/manage_idcard_orders');
    }

    // Prepare printing data
    $printingData = [
        'print_remarks' => $this->input->post('print_remarks'),
        'estimated_datetime_to_complete_print' => $this->input->post('estimated_print_date'),
        'print_file_path' => null
    ];

    // Update order status to In-Printing
    $result = $this->msm_model->send_idcard_admin_for_printing($orderId, $printingData);

    if ($result) {
      $msm_school_list_id = $this->input->post('msm_school_list_id');

        if ($msm_school_list_id !== null) {
            $data['school_details'] = $this->msm_model->get_school_details($msm_school_list_id);
        }
    
        $school_code = $data['school_details']->school_code;
        $school_domain = $data['school_details']->school_domain;
        $bridgeData = [
          'school_code' => $school_code,
          'school_domain' => $school_domain,
          'status' =>'In Printing',
          'order_id'=>$orderIdSchool,
          'estimated_time_of_completion_for_printing' =>$estimated_print_date,
          'api' => 'update_in_printing_status'
        ];
        $this->bridge_idcards($bridgeData);
        $this->session->set_flashdata('success', 'Order sent for printing');
    } else {
        $this->session->set_flashdata('error', 'Failed to send order for printing');
    }
    $school_code=$this->input->post('school_code');
    redirect('msm_v3/Dashboard/view_idcard_order/' . $orderIdSchool.'/'.$school_code);
  }

  /**
   * Send order for delivery
   */
  public function send_idcard_for_delivery() {

    // Get order ID
    $orderId = $this->input->post('order_id');
    $order_id_School = $this->input->post('order_id_School');
    $estimated_delivery_date = $this->input->post('estimated_delivery_date');

    // Check if order exists
    $order = $this->msm_model->get_idcard_admin_order_by_id($order_id_School);
    if (!$order) {
        $this->session->set_flashdata('error', 'Order not found');
        redirect('msm_v3/Dashboard/manage_idcard_orders');
    }

    // Check if order is in printing status
    if ($order->order_status !== 'In-Printing') {
        $this->session->set_flashdata('error', 'Order must be in printing status before sending for delivery');
        redirect('msm_v3/Dashboard/view_idcard_order/' . $orderId);
    }

    // Prepare delivery data
    $deliveryData = [
        'delivery_remarks' => $this->input->post('delivery_remarks'),
        'estimated_datetime_to_complete_delivery' => $this->input->post('estimated_delivery_date')
    ];

    // Update order status to In-Delivery
    $result = $this->msm_model->send_idcard_admin_for_delivery($orderId, $deliveryData);

    if ($result) {
      $msm_school_list_id = $this->input->post('msm_school_list_id');

        if ($msm_school_list_id !== null) {
            $data['school_details'] = $this->msm_model->get_school_details($msm_school_list_id);
        }
    
        $school_code = $data['school_details']->school_code;
        $school_domain = $data['school_details']->school_domain;
        $bridgeData = [
          'school_code' => $school_code,
          'school_domain' => $school_domain,
          'status' =>'In Delivery',
          'order_id'=>$order_id_School,
          'estimated_time_of_completion_for_delivery' =>$estimated_delivery_date,
          'api' => 'update_in_delivery_status'
        ];
        $this->bridge_idcards($bridgeData);
        $this->session->set_flashdata('success', 'Order sent for delivery');
    } else {
        $this->session->set_flashdata('error', 'Failed to send order for delivery');
    }
    $school_id=$this->input->post('school_code');
    redirect('msm_v3/Dashboard/view_idcard_order/' . $order_id_School.'/'.$school_id);
  }

  /**
   * Mark delivery as complete
   *
   * @param int $id Order ID
   */
  public function mark_idcard_delivery_complete($id,$school_id,$order_id_school) {

    // Check if order exists
    $order = $this->msm_model->get_idcard_admin_order_by_id($order_id_school);
    if (!$order) {
        $this->session->set_flashdata('error', 'Order not found');
        redirect('msm_v3/Dashboard/manage_idcard_orders');
    }

    // Check if order is in delivery status
    if ($order->order_status !== 'In-Delivery') {
        $this->session->set_flashdata('error', 'Order must be in delivery status before marking as delivered');
        redirect('msm_v3/Dashboard/view_idcard_order/' . $order_id_school .'/'.$school_id);
    }

    // Mark delivery as complete
    $result = $this->msm_model->mark_idcard_admin_delivery_complete($id);

    if ($result) {
      if ($school_id !== null) {
            $data['school_details'] = $this->msm_model->get_school_details($school_id);
        }
        $school_code = $data['school_details']->school_code;
        $school_domain = $data['school_details']->school_domain;
        $bridgeData = [
          'school_code' => $school_code,
          'school_domain' => $school_domain,
          'status' =>'delivered',
          'order_id'=>$order_id_school,
          'api' => 'update_delivered_status'
        ];
        $this->bridge_idcards($bridgeData);
        $this->session->set_flashdata('success', 'Delivery marked as complete');
    } else {
        $this->session->set_flashdata('error', 'Failed to mark delivery as complete');
    }

    redirect('msm_v3/Dashboard/view_idcard_order/' . $order_id_school .'/'.$school_id);
  }

  /**
   * Get ID card entities via AJAX
   */
  public function get_idcard_entities() {
    // Check if user is authorized to access this page
    if (!$this->authorization->isSuperAdmin()) {
        echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
        return;
    }

    // Get parameters
    $orderId = $this->input->get('order_id');
    $idCardType = $this->input->get('id_card_type');

    if (empty($orderId) || empty($idCardType)) {
        echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
        return;
    }

    // Load Idcards model
    $this->load->model('idcards/Idcards_model');

    // Get entities for this order
    $entities = $this->Idcards_model->getAllEntitiesForTheSpecificOrder($orderId, $idCardType);

    // Get status counts
    $statusCounts = $this->Idcards_model->get_order_status_counts($orderId);

    // Return JSON response
    $response = [
        'success' => true,
        'entities' => $entities,
        'status_counts' => $statusCounts,
        'total' => count($entities)
    ];

    echo json_encode($response);
  }

  public function individual_school_dashboard() {
    $data['json_school_list'] = json_encode($this->msm_model->get_restricted_school_list());
    $data['color_theme'] = $this->settings->getSetting('msm_graph_theme');
    if ($this->mobile_detect->isTablet()) {
      $data['main_content']   = 'msm_individual_dashboard/tablet/individual_school_dashboard';
    } else if ($this->mobile_detect->isMobile()){
      $data['main_content']    = 'msm_individual_dashboard/mobile/individual_school_dashboard';
    } else {
      $data['main_content']    = 'msm_individual_dashboard/desktop/individual_school_dashboard';
    }
    $data['school_code'] = $this->input->get('school_code');
    $data['school_domain'] = $this->input->get('school_domain');
    $this->load->view('msm/inc/template', $data);
  }

  private function __call_api($data, $end_point) {
    // print_r(json_encode($data));die();
    $curl_request = [
        CURLOPT_URL => $end_point,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POST => 1,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_SSL_VERIFYPEER => 0,
        CURLOPT_HTTPHEADER => [
            "content-type: application/json"
        ]
    ];

    $curl = curl_init();
    curl_setopt_array($curl, $curl_request);
    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);
    return $response;
  }

  public function bridge() {
    if(empty($input)) {
      $data = $_POST;
    } else {
      $data = $input;
    }    

    $end_point = "https://" . $data['school_code'] . ".localhost.in/oxygenv2/msm_v3/api/" . $data['api'];
    // $end_point = "https://" . $data['school_code'] . "." . $data['school_domain'] . ".in/msm_v3/api/" . $data['api'];
    $data['response'] = $this->__call_api($data, $end_point);
    $data['status'] = 1;

    if(empty($input)) {
      echo json_encode($data);
    } else {
      return $data;
    }
  }

  public function bridge_dashboard() {
    $data = $_POST;

    $end_point = "https://" . $data['school_code'] . ".localhost.in/oxygenv2/msm/api/" . $data['api'];
    // $end_point = "https://" . $data['school_code'] . "." . $data['school_domain'] . ".in/msm/api/" . $data['api'];

    $response = $this->__call_api($data, $end_point);
    $response_data = json_decode($response);

    // echo '<pre>';print_r($response_data);
    $data['student_count_obj'] = $response_data->student_count_obj->statistic_value;
    $data['staff_count_obj'] = $response_data->staff_count_obj->statistic_value;
    $data['staff_attendance_obj'] = $response_data->staff_attendance_obj->statistic_value;
    $data['status'] = '1';

    if(empty($input)) {
      echo json_encode($data);
    } else {
      return $data;
    }
  }

  public function bridge_report() {
    $data = $_POST;
    // $end_point = "https://" . $data['school_code'] . ".localhost.in/oxygenv2/msm/api/" . $data['api'];
    $end_point = "https://" . $data['school_code'] . "." . $data['school_domain'] . ".in/msm/api/" . $data['api'];
    
    $data['response'] = $this->__call_api($data, $end_point);
    $data['status'] = '1';
    echo json_encode($data);
  }

  private function __prepareAddressFields() {
    $addresses = array('0' => 'Present Address', '1'=>'Permanent Address');
  
    $addArr = array();
    $index = count($this->columnList);
    foreach ($addresses as $addKey => $add) {
        $obj = array();
        $obj['displayName'] = $add;
        $obj['varName'] = $addKey .'_' . 'address';
        $obj['table'] = 'address_info';
        $obj['index'] = ++$index;
        $obj['displayType'] = 'text';
        $obj['dataType'] = 'string';
        $obj['addressType'] = $addKey;
        switch ($addKey) {
          case '0':
          $obj['addressOf'] = 'present_address';
            break;
          case '1':
          $obj['addressOf'] = 'permanent_address';
            break;
        }
        $addArr[] = $obj;    
    }
    return $addArr;
  }

  private function __prepareStaffCustomFields(){
    $custom_fields =$this->settings->getSetting('staff_custom_fields');
    $customFIelds = array();
    if($custom_fields){
      $indexNumber = 200;
      foreach ($custom_fields as $displayName => $columnName) {
        $obj = array();
        $obj['displayName'] = $displayName;
        $obj['columnNameWithTable'] = $columnName;
        $obj['varName'] = $columnName;
        $obj['columnName'] = $columnName;
        $obj['table'] = 'staff_master';
        $obj['index'] = $indexNumber++;
        $obj['displayType'] = 'text';
        $obj['dataType'] = 'string';
        $customFIelds[] = $obj;
      }
    }
    return $customFIelds;
  }

  public function bridge_idcards($data) {
    // $end_point = "https://" . $data['school_code'] . ".localhost.in/oxygenv2/msm_v3/api/" . $data['api'];
    $end_point = "https://" . $data['school_code'] . "." . $data['school_domain'] . ".in/msm_v3/api/" . $data['api'];
    $data['response'] = $this->__call_api($data, $end_point);
    $data['status'] = '1';
    return $data;
  }

  public function update_admin_idcard_table(){
		$this->load->model('idcards/Idcards_model','idcards_model');
    $data = $this->idcards_model->update_admin_idcard_table();
    echo 0;
  }

  public function update_idcard_orders(){
    $result= $this->msm_model->update_idcard_orders();
    echo $result ;
  }

}