<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/student_menu');?>">Student Menu</a></li>
    <li><a href="<?php echo site_url('student/Student_controller/index/');?>">Student Index</a></li>
    <li><a href="<?php echo site_url('student/Student_controller/addMoreStudentInfo/'.$student_uid);?>">Student Detail</a></li>
    <li>Provision Parent Credentials</li>
</ul>
<hr>
<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('student/Student_controller/addMoreStudentInfo/'.$student_uid);?>">
                            <span class="fa fa-arrow-left"></span>
                        </a> 
                        <?php if (!empty($stdData)) { ?>
                            Provision Parent Credentials Details of <?php echo '<strong>' . strtoupper($stdData->stdName) . '</strong> (Class/Sections: ' . $stdData->classSection . ') (Admission No: '. $stdData->admission_no.')'?>
                        <?php } else { ?>
                            Provision Parent Credentials Details
                        <?php } ?>
                    </h3> 
                </div>
            </div>
            <div class="card-body">
                <?php if ($smsConfigured == 0 || $emailConfigured == 0) { ?>
                    <div class="col-md-12 px-0">
                        <p class="text-danger" style="font-size: 14px;">
                            <?php if($smsConfigured == 0 && $emailConfigured == 0) { ?>
                                <strong>Note: </strong>
                                <strong>SMS and Email are not configured. To use either of these features, please contact the School Administrator or IT Team for further assistance.</strong>
                            <?php } else if($emailConfigured == 0) { ?>
                                <strong class="text-success">Currently, only SMS is in use.</strong>
                                <br>
                                <strong>Note: </strong>
                                <strong>Please contact the Administrator or IT Team to enable the Email feature if needed.</strong>
                            <?php } else if($smsConfigured == 0) { ?>
                                <strong class="text-success">Currently, only Email is in use.</strong>
                                <br>
                                <strong>Note: </strong>
                                <strong>Please contact the Administrator or IT Team to enable the SMS feature if needed.</strong>
                            <?php } ?>
                        </p>
                    </div>
                <?php } else if($smsConfigured == 1 && $emailConfigured == 1) { ?>
                    <div class="col-md-12 px-0">
                        <p class="text-success" style="font-size: 14px;"> 
                            <strong>Note: </strong>
                            <strong>SMS and Email Templates are configured</strong>
                        </p>
                    </div>
                <?php }?>
                <div class="stdudentData tableFixHead col-md-12 px-0">
                    <div class="no-data-display"><i class="fa fa-spin fa-spinner"></i> Loading...</div>
                </div>
                <div id="summary" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-scrollable" role="document">
                        <div class="modal-content" style="width: 80%;margin-top: 1% !important;margin:auto;">
                            <div class="modal-header">
                                <h4 class="modal-title">Review the number(s) / email(s) and provide confirmation.</h4>
                                <button type="button" class="close" data-dismiss="modal" onclick="resetFrom()">&times;</button>
                            </div>
                            <form enctype="multipart/form-data" method="post" id="send_provision_credentials" action="" class="form-horizontal" >
                                <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;height:500px;">
                                    <div>
                                        <h4 id="warnMsg"></h4>
                                    </div>
                                    <div id="modal-loader" style="display: none; text-align: center;">
                                        <div class="no-data-display">Please wait while the data is being prepared...</div>
                                        <img src="<?php echo base_url('assets/img/ajax-loader.gif'); ?>" style="width:400px; height:400px;">
                                    </div>
                                    <table class="table" id="dynamic-content" width="100%"></table>
                                </div>
                            </form>
                            <div class="modal-footer">
                                <span id="credit-err" class="text-danger">Not enough credits to send sms</span>
                                <button type="button" id="cancelModal" class="btn btn-secondary" data-dismiss="modal" onclick="resetFrom()">Cancel</button>
                                <button type="button" id="confirmBtn" onclick="form_submit()" class="btn btn-secondary mt-0">Confirm</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
.tableFixHead {
    overflow-y: auto;
    height: 35rem;
}
.tableFixHead thead th {
    position: sticky;
    top: -2px;
}
table {
    border-collapse: collapse;
    width: 100%;
}
th,
td {
    padding: 8px 16px;
    border: 1px solid #ccc;
}
th {
    background: #eee;
}
.medium {
    width: 40%;
    margin: auto;
}

.statusColumn button{
    cursor: default !important;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
let student_id = '<?php echo $student_uid; ?>';
function check_all(check, value) {
    if (value == 'send') {
        isCheckedBySend(check);
    } else if (value == 're_send') {
        isCheckedByReSend(check);
    } else if (value == 'deactivate') {
        isCheckedByDeactivate(check);
    } else if (value == 'reset') {
        isCheckedByReset(check);
    }
}
function isCheckedBySend(check) {
    if ($(check).is(':checked')) {
        $('.sendCheck').prop('checked', true);
        $('#sendButton').prop('disabled', false);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll, .reset, #resetButton, .resetCheck').prop({
            disabled: true,
            checked: false
        });
    } else {
        $('.sendCheck').prop('checked', false);
        $('#sendButton').prop('disabled', true);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll, .reset, #resetButton, .resetCheck').prop('disabled', false);
    }
}
function isCheckedByReSend(check) {
    if ($(check).is(':checked')) {
        $('.re_sendCheck').prop('checked', true);
        $('#re-sendButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll, .reset, #resetButton, .resetCheck').prop({
            disabled: true,
            checked: false
        });
    } else {
        $('.re_sendCheck').prop('checked', false);
        $('#re-sendButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll, .reset, #resetButton, .resetCheck').prop('disabled', false);
    }
}
function isCheckedByDeactivate(check) {
    if ($(check).is(':checked')) {
        $('.deactivate_sendCheck').prop('checked', true);
        $('#deactivateButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, .reset, #resetButton, .resetCheck').prop({
            disabled: true,
            checked: false
        });
    } else {
        $('.deactivate_sendCheck').prop('checked', false);
        $('#deactivateButton').prop('disabled', true);
        <?php if($smsConfigured == 0 && $emailConfigured == 0) { ?>
            $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, .reset, #resetButton, .resetCheck').prop('disabled', true);
        <?php } else { ?>
            $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, .reset, #resetButton, .resetCheck').prop('disabled', false);
        <?php }?>
    }
}
function isCheckedByReset(check) {
    if ($(check).is(':checked')) {
        $('.reset').prop('checked', true);
        $('#resetButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            disabled: true,
            checked: false
        });
    } else {
        $('.reset').prop('checked', false);
        $('#resetButton').prop('disabled', true);
        <?php if($smsConfigured == 0 && $emailConfigured == 0) { ?>
            $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', true);
        <?php } else { ?>
            $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
        <?php }?>
    }
}
function check_smsIndividual() {
    if ($('.sendCheck:checked').length > 0) {
        $('#sendButton').prop('disabled', false);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll, .reset, #resetButton, .resetCheck').prop({
            disabled: true,
            checked: false
        });
    } else {
        $('.sendCheck').prop('checked', false);
        $('#sendButton').prop('disabled', true);
        $('.re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll, .reset, #resetButton, .resetCheck').prop('disabled', false);
    }
}
function check_reSMSIndividual() {
    if ($('.re_sendCheck:checked').length > 0) {
        $('#re-sendButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll, .reset, #resetButton, .resetCheck').prop({
            disabled: true,
            checked: false
        });
    } else {
        $('.re_sendCheck').prop('checked', false);
        $('#re-sendButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .deactivate_sendCheck, #deactivateAll, .reset, #resetButton, .resetCheck').prop('disabled', false);
    }
}
function check_deactivateIndividual() {
    if ($('.deactivate_sendCheck:checked').length > 0) {
        $('#deactivateButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, .reset, #resetButton, .resetCheck').prop({
            disabled: true,
            checked: false
        });
    } else {
        $('.deactivate_sendCheck').prop('checked', false);
        $('#deactivateButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, .reset, #resetButton, .resetCheck').prop('disabled', false);
    }
}
function check_resetIndividual() {
    if ($(".reset:checked").length > 0) {
        $('#resetButton').prop('disabled', false);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop({
            "disabled": true,
            "checked": false
        });
    } else {
        $('#resetButton').prop('disabled', true);
        $('.sendCheck, #sendAll, .re_sendCheck, #re_sendAll, .deactivate_sendCheck, #deactivateAll').prop('disabled', false);
    }
}
function send_provision_credentials() {
    var pids = [];
    $('.sendCheck:checked').each(function() {
        var methodType = $('#method_type_indiv_' + $(this).val()).val();
        if (methodType == 'sms') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
        if (methodType == 'email' && $('#parent-email' + $(this).val()).html() != '') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
    });
    if (pids.length <= 0) {
        return false;
    }
    $("#credit-err").hide();
    $("#confirmBtn").attr('disabled', true);
    $('#summary').modal('show');
    sendProvisionLink(pids, 'activation');
    $("#process").val('activation');
    $("#smsType").val('send');
}
function re_send_provision_credentials() {
    var pids = [];
    $('.re_sendCheck:checked').each(function() {
        var methodType = $('#method_type_indiv_' + $(this).val()).val();
        if (methodType == 'sms') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
        if (methodType == 'email' && $('#parent-email' + $(this).val()).html() != '') {
            pids.push($(this).val() + '_' + $('#method_type_indiv_' + $(this).val()).val());
        }
    });
    if (pids.length <= 0) {
        Swal.fire({
            icon: 'warning',
            title: 'No Users Selected',
            text: 'Please select at least one user to re-send.',
            timer: 3000,
            timerProgressBar: true
        });
        return false;
    }
    $("#credit-err").hide();
    $("#confirmBtn").attr('disabled', true);
    $('#summary').modal('show');
    sendProvisionLink(pids, 'activation');
    $("#process").val('activation');
    $("#smsType").val('send');
}
function sendProvisionLink(pids, provision = 'activation', alreadyProv = 0) {
    $('#modal-loader').show();
    $('#dynamic-content').html('');
    if (pids.length > 0) {
        $("#warnMsg").html('');
        $.ajax({
                url: '<?php echo site_url('parent_activation/getPreview_credentials'); ?>',
                type: 'POST',
                data: {
                    'pids': pids,
                    'process': provision
                },
                dataType: 'html'
            })
            .done(function(data) {
                var data = $.parseJSON(data);
                if(data.config){
                    $('#modal-loader').hide();
                    $("#credit-err").hide();
                    $('#dynamic-content').html(`<div class="no-data-display">${data.config}</div>`);
                    return;
                }
                var previewData = data.preview;
                var credits_available = data.credits_available;
                var html = '';
                let hasData = false;
                $('#dynamic-content').html('');
                if (previewData.length == 0) {
                    html += '<h4>No data</h4>';
                } else {
                    html +=
                        '<table><thead><tr><th>#</th><th style="width:30%;">Name / Relation</th><th>Number / Email</th><th>Message</th></tr></thead><tbody id="provisionPreviewMessage">';
                    let slno = 0;
                    for (var i = 0; i < previewData.length; i++) {
                        if (previewData[i].send_type == 'email' && (!previewData[i].message_by || !previewData[i].message_by.includes('@'))) {
                            continue;
                        }
                        if (previewData[i].send_type == 'sms' && (!previewData[i].message_by || previewData[i].message_by.length != 10)) {
                            continue;
                        }
                        hasData = true;
                        var escapedMessage = previewData[i].message.replace(/"/g, '&quot;');
                        html += '<tr>';
                        html += '<td>' + (slno + 1) + '</td>';
                        html += '<td>' + previewData[i].name + ' / ' + previewData[i].relation_type + '</td>';
                        html += '<td>' + previewData[i].message_by + '</td>';
                        html += '<td>' + previewData[i].message + '</td>';
                        html += '</tr>';
                        $("#send_provision_credentials").append(`
                            <input type="hidden" name="codes[${previewData[i].pid}]" value="${previewData[i].code}">
                            <input type="hidden" name="messages[${previewData[i].std_id}_${previewData[i].pid}_${previewData[i].relation_type}_${previewData[i].user_id}_${previewData[i].send_type}]" value="${escapedMessage}">
                        `);
                        slno++;
                    }
                    if(!hasData){
                        html += '<tr><td colspan="4" class="text-center">No Contact Details Found For The Selected Users</td></tr>';
                        $("#confirmBtn").attr('disabled', true);
                    }
                    html += '</tbody></table>';
                }
                $('#dynamic-content').html(html);
                if (alreadyProv > 0) {
                    $("#warnMsg").html(alreadyProv + ' already provisioned numbers are not taken.');
                }
                $('#modal-loader').hide();
                if (credits_available == 1) {
                    if(hasData){
                        $("#confirmBtn").attr('disabled', false);
                    }
                    $("#credit-err").hide();
                } else {
                    $("#confirmBtn").attr('disabled', true);
                    $("#credit-err").show();
                }
            })
            .fail(function() {
                $('#dynamic-content').html('');
                $('#dynamic-content').html('<i class="glyphicon glyphicon-info-sign"></i> Something went wrong, Please try again...');
                $('#modal-loader').hide();
            });
    } else {
        $("#warnMsg").html('');
        if (alreadyProv > 0) {
            $("#warnMsg").html(alreadyProv + ' already provisioned member(s) is/are not shown here.');
        }
        $("#confirmBtn").hide();
        $('#dynamic-content').html('<i class="glyphicon glyphicon-info-sign"></i> Please select students...');
    }
}
function deactivate_provision_credentials() {
    var userIds = [];
    $('.deactivate_sendCheck:checked').each(function() {
        userIds.push($(this).val());
    });
    if (userIds.length <= 0) {
        Swal.fire({
            icon: 'warning',
            title: 'No Users Selected',
            text: 'Please select at least one user to deactivate.',
            timer: 3000,
            timerProgressBar: true
        });
        return false;
    }
    Swal.fire({
        title: 'Deactivating Users',
        html: `You are deactivating <b>${userIds.length}</b> user(s). Are you sure?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        confirmButtonColor: '#28a745', // Green
        cancelButtonColor: '#dc3545',  // Red
        reverseButtons: true,
        customClass: {
            popup: 'medium'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo site_url('parent_activation/deactivate_provision_credentials_by_user_id'); ?>',
                type: 'POST',
                data: {
                    'userIds': userIds
                }
            })
            .done(function(data) {
                if (data) {
                    fetchAndBuildTable(student_id);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Something went wrong while deactivating users.',
                        timer: 3000,
                        timerProgressBar: true
                    });
                }
            })
            .fail(function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to send request to the server.',
                    timer: 3000,
                    timerProgressBar: true
                });
            });
        }
    });
}

function reset_password(){
    const selectedCheckboxes = $('.reset:checked');

        if (selectedCheckboxes.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'No Users Selected',
                text: 'Please select at least one user to reset password.',
                timer: 3000,
                timerProgressBar: true
            });
            return false;
        }

        // Gather userIds and usernames
        const users = Array.from(selectedCheckboxes).map(cb => ({
            userId: $(cb).val(),
            username: $(cb).data('username'),
            checkbox: cb
        }));

        const usernamesList = users.map(u => `<li>${u.username}</li>`).join('');
        Swal.fire({
            title: 'Reset to Default Password?',
            html: `
                <b>The following usernames will be reset:</b>
                <div style="max-height: 200px; overflow-y: auto; text-align: left; margin-top: 10px; border: 1px solid #ddd; padding: 10px; border-radius: 4px;">
                    <ul style="margin: 0; padding-left: 20px;">
                        ${usernamesList}
                    </ul>
                </div>
                <br><b>Password:</b> welcome123
            `,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, Reset All',
            cancelButtonText: 'No',
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#dc3545',
            customClass: {
                popup: 'medium'
            }
        }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo site_url('parent_activation/reset_default_password_user_id'); ?>',
                type: 'post',
                data: {
                    userId: userid
                },
                success: function(data) {
                    if (data) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Password Reset',
                            text: 'Password reset successfully.',
                            confirmButtonColor: '#28a745',
                            timer: 3000,
                            timerProgressBar: true
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Reset Failed',
                            text: 'Failed to reset password.',
                            confirmButtonColor: '#dc3545',
                            timer: 3000,
                            timerProgressBar: true
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Server Error',
                        text: 'Something went wrong while resetting.',
                        confirmButtonColor: '#dc3545',
                        timer: 3000,
                        timerProgressBar: true
                    });
                }
            });
        }
    });
}

function resetFrom() {
    $('#send_provision_credentials')[0].reset();
    $('#send_provision_credentials').find('input[type=hidden]').remove();
}

function form_submit() {
    var action = "<?php echo site_url('parent_activation/send_parent_provision_credentials') ?>";
    var form = $('#send_provision_credentials');

    $("#confirmBtn").html('Please Wait...').attr('disabled', true);

    $.ajax({
        url: action,
        type: "POST",
        data: form.serialize(),
        success: function (response) {
            $("#confirmBtn").html('Confirm').attr('disabled', false);
            let parsedData = JSON.parse(response);
            if(parsedData.status == 'success'){
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: parsedData.message,
                    timer: 3000,
                    timerProgressBar: true
                }).then((result) => {
                    $('#summary').modal('hide');
                    $('#send_provision_credentials')[0].reset();
                    $('#send_provision_credentials').find('input[type=hidden]').remove();
                    fetchAndBuildTable(student_id);
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: parsedData.message,
                    timer: 3000,
                    timerProgressBar: true
                });
            }
        },
        error: function () {
            $("#confirmBtn").html('Confirm').attr('disabled', false);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to send request to the server.',
                timer: 3000,
                timerProgressBar: true
            });
        }
    });
}
$(document).ready(function(){
    fetchAndBuildTable(student_id);
    $(document).on('change', '#send_method_selector', function () {
        const value = $(this).val();
        $('.method_type_dropdown').val(value);
    });
});
function fetchAndBuildTable(student_id) {
    $.ajax({
        url: '<?php echo site_url('parent_activation/get_credentials_student_data_by_id'); ?>',
        type: 'POST',
        data: { student_id: student_id },
        dataType: 'json',
        success: function(data) {
            buildProvisionTable(data);
        },
        error: function() {
            $('.stdudentData').html('<div class="no-data-display">Failed to load data.</div>');
        }
    });
}
function buildProvisionTable(stdData) {
    let smsConfigured = <?php echo $smsConfigured; ?>;
    let emailConfigured = <?php echo $emailConfigured; ?>;
    let html = `<table class="table table-bordered" id="student_provision">
        <thead>
            <tr>
                <th>#</th>
                <th>Student Name</th>
                <th>Relation</th>
                <th>Parent Name</th>
                <th>Mobile Number</th>
                <th>Email</th>
                <th style="width: 10%;">
                    <select class="form-control" id="send_method_selector" name="send_method_type">`;
                    if(smsConfigured == 0 && emailConfigured == 0) {
                        html += '<option value="sms" disabled selected>SMS</option><option value="email" disabled>Email</option>';
                    } else if(smsConfigured == 0) {
                        html += '<option value="email">Email</option>';
                    } else if(emailConfigured == 0) {
                        html += '<option value="sms">SMS</option>';
                    } else {
                        html += '<option value="sms">SMS</option><option value="email">Email</option>';
                    }
                    html += `</select>
                </th>
                <th>Status</th>`;
                if(smsConfigured == 0 && emailConfigured == 0) {
                    html += `
                        <th>
                            <input type="button" id="sendButton" disabled class="btn btn-primary" value="Send">
                            <br><br>Send <input type="checkbox" name="selectAll" disabled value="send" onclick="check_all(this, value)" id="sendAll" class="check">
                        </th>
                    `;
                } else {
                    html += `<th>
                        <input type="button" onclick="send_provision_credentials()" id="sendButton" disabled class="btn btn-primary" value="Send">
                        <br><br>Send <input type="checkbox" name="selectAll" value="send" onclick="check_all(this, value)" id="sendAll" class="check">
                    </th>`;
                }
                if(smsConfigured == 0 && emailConfigured == 0) {
                    html += `<th>
                        <input type="button" id="re-sendButton" disabled class="btn btn-primary" value="Re-Send">
                        <br><br>Re-send <input type="checkbox" name="selectAll" value="re_send" onclick="check_all(this, value)" id="re_sendAll" class="check" disabled>
                    </th>`;
                } else {
                    html += `<th>
                        <input type="button" onclick="re_send_provision_credentials()" id="re-sendButton" disabled class="btn btn-primary" value="Re-Send">
                        <br><br>Re-send <input type="checkbox" name="selectAll" value="re_send" onclick="check_all(this, value)" id="re_sendAll" class="check">
                    </th>`;
                }
                html += `<th>
                    <input type="button" onclick="deactivate_provision_credentials()" id="deactivateButton" disabled class="btn btn-primary" value="Deactivate">
                    <br><br>Deactivate <input type="checkbox" name="selectAll" value="deactivate" onclick="check_all(this, value)" id="deactivateAll" class="check">
                </th>
                <th><input type="button" onclick="reset_password()" id="resetButton" disabled value="Reset to default" class="btn btn-primary"/>
                <br><br><input type="checkbox" name="selectAll" value="reset" onclick="check_all(this, value)" id="reset" class="resetCheck"></th>
            </tr>
        </thead>
        <tbody>`;
    if (!stdData || stdData.length === 0) {
        html += `<tr><td colspan="13" class="text-center">No data found</td></tr>`;
    } else {
        for (let i = 0; i < stdData.length; i++) {
            const student = stdData[i];
            let accountStatus = 1;
            let activeDisabled = student.Active == 1 ? 1 : 0;
            html += `<tr>
                <td>${i + 1}</td>
                <td>${student.studentName}</td>
                <td>${student.relation_type}</td>
                <td>${student.parentName}</td>
                <td>${student.mobile == null || student.mobile.trim() == '' ? 'N/A' : student.mobile }</td>
                <td id="parent-email${student.pid}">${student.email == null || student.email.trim() == '' ? 'N/A' : student.email }</td>`;
            if (student.oldUid != null && student.userId != student.oldUid) {
                html += `<td>${student.siblings.map(sib => `<p>${sib.stdName} (${sib.aSection})</p>`).join('')}</td>`;
            } else {
                html += `<td>
                    <select class="form-control method_type_dropdown" name="select_send_options" id="method_type_indiv_${student.pid}">`;
                    if(smsConfigured == 0 && emailConfigured == 0) {
                        html += '<option value="sms" disabled selected>SMS</option><option value="email" disabled>Email</option>';
                    } else if(smsConfigured == 0) {
                        html += '<option value="email">Email</option>';
                    } else if(emailConfigured == 0) {
                        html += '<option value="sms">SMS</option>';
                    } else {
                        html += '<option value="sms">SMS</option><option value="email">Email</option>';
                    }
                    html += `</select>
                </td>`;
            }
            let loggedStatus = '';
            if (student.oldUid != null && student.userId != student.oldUid) {
                loggedStatus = 'Sibling Connected';
            } else if (student.loggedin_atleast_once == 1) {
                loggedStatus = '<button type="button" class="btn btn-info btn-xs">Logged-in</button>';
            } else if (student.Active == 1) {
                loggedStatus = '<button type="button" class="btn btn-warning btn-xs">Activated</button>';
            } else {
                accountStatus = 0;
                loggedStatus = '<button type="button" class="btn btn-danger btn-xs">Not activated</button>';
            }
            html += `<td class="statusColumn">${loggedStatus}</td>`;
            if (student.oldUid != null && student.userId != student.oldUid) {
                html += `<td>Sibling connected</td>`;
            } else {
                if (activeDisabled) {
                    html += `<td>${student.loggedin_atleast_once == 1 ? '<font color="green">Logged-in</font>' : '<font color="orange">Activated</font>'}</td>`;
                } else {
                    if(smsConfigured == 0 && emailConfigured == 0) {
                        html += '<td>-</td>';
                    } else {
                        html += `<td><input type="checkbox" onclick="check_smsIndividual()" name="send_credentials" value="${student.pid}" class="sendCheck"></td>`;
                    }
                }
            }
            if (student.oldUid != null && student.userId != student.oldUid) {
                html += `<td>Sibling connected</td>`;
            } else {
                if (activeDisabled) {
                    if(smsConfigured == 0 && emailConfigured == 0) {
                        html += '<td>-</td>';
                    } else {
                        html += `<td><input type="checkbox" onclick="check_reSMSIndividual()" name="re_send_credentials" value="${student.pid}" class="re_sendCheck"></td>`;
                    }
                } else {
                    html += `<td>Not Activated</td>`;
                }
            }
            if (student.oldUid != null && student.userId != student.oldUid) {
                html += `<td>Sibling connected</td>`;
            } else if(accountStatus == 1) {
                html += `<td><input type="checkbox" onclick="check_deactivateIndividual()" name="deactivate_credentials" value="${student.userId}" class="deactivate_sendCheck"></td>`;
            } else {
                html += `<td>Not Activated</td>`;
            }
            if (student.oldUid != null && student.userId != student.oldUid) {
                html += `<td>Sibling connected</td>`;
            } else {
                if (activeDisabled) {
                    html += `<td><input type="checkbox" class="reset" onclick="check_resetIndividual()" value="${student.userId}" data-username="${student.username}"></td>`;
                } else {
                    html += `<td>Not Activated</td>`;
                }
            }
            html += '</tr>';
        }
    }
    html += `</tbody></table>`;
    $('.stdudentData').html(html);
}
</script>