<?php

class Lesson_plan extends CI_Controller {

  function __construct() {
      parent::__construct();
      if (!$this->ion_auth->logged_in()) {
          redirect('auth/login', 'refresh');
      }
      $this->load->model('academics/lessonPlan_model', 'lessonplan_model');
      $this->load->model('academics/ManageSubject_model');
      $this->load->model('staff/Staff_Model');
      $this->load->library('filemanager');
      $this->load->model('attendance_v2/attendance_model');
  }

  public function addScheme() {
    $data['grades'] = $this->lessonplan_model->getAllClasses();
    // echo "<pre>";print_r('skjdf');die();
    $data['viewSchemes'] = $this->lessonplan_model->getSchemes();
    $data['main_content'] = 'academics/lesson_plan/schemes';
    $this->load->view('inc/template', $data);
  }

  public function submitScheme(){
    $data = $this->lessonplan_model->submitScheme();
    echo ($data);
  }

  public function getSubjects(){
    $data['subjectsList'] = $this->lessonplan_model->getSubjectsList();
    echo json_encode($data);
  }

  public function lesson_add(){
    $data['main_content'] = 'academics/lesson_plan/lesson';
    $this->load->view('inc/template', $data);
  }

  public function session_plan_status() {
    $data['classes'] = $this->ManageSubject_model->getClassesForLms();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'academics/lesson_plan/session_plan/status/index_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'academics/lesson_plan/session_plan/status/index_mobile';
    }else{
      $data['main_content'] = 'academics/lesson_plan/session_plan/status/index'; 	
    }    
    $this->load->view('inc/template', $data);
  }

  public function get_subjects_list(){
    $result = $this->lessonplan_model->get_subjects_list();
    echo json_encode($result);
  }

  public function get_lesson_plan_by_lp_sub_id(){
    $subject_id_main = $_POST['subject_id_main'];
    $result = $this->lessonplan_model->get_lesson_plan_by_lp_sub_id_details($subject_id_main);
    echo json_encode($result);
  }

  public function session_plan_console($lesson_id) {
    $data['lesson_id'] = $lesson_id;
    $data['lesson_name'] = $this->lessonplan_model->get_lesson_detailsby_id($lesson_id);
    $data['learning_objectives'] = $this->lessonplan_model->get_lesson_details_performance_pointers();
    $data['skills'] = $this->lessonplan_model->get_skills();
    $data['assessment_type'] = $this->lessonplan_model->get_assessment_type();
    $data['resource_types'] = $this->lessonplan_model->get_resources_type();
    $data['subject_class'] = $this->lessonplan_model->get_subject_name($lesson_id);
    $data['book_resource'] = $this->lessonplan_model->get_books_resource_sub_wise($data['subject_class']->id);
    $data['main_content'] = 'academics/lesson_plan/session_plan/detail/index';
    $this->load->view('inc/template', $data);
  }

  public function session_plan_console2($lesson_id) {
    $data['lesson_id'] = $lesson_id;
    $data['lesson_name'] = $this->lessonplan_model->get_lesson_detailsby_id($lesson_id);
    $data['learning_objectives'] = $this->lessonplan_model->get_lesson_details_performance_pointers();
    $data['skills'] = $this->lessonplan_model->get_skills();
    $data['assessment_type'] = $this->lessonplan_model->get_assessment_type();
    $data['resource_types'] = $this->lessonplan_model->get_resources_type();
    $data['subject_class'] = $this->lessonplan_model->get_subject_name($lesson_id);
    $data['session_list'] = $this->lessonplan_model->get_all_sessions_by_lesson($lesson_id);
    $data['topic_list'] = $this->lessonplan_model->get_all_topics_by_lesson($lesson_id);
    //echo "<pre>";print_r($data['topic_list']);die();
    $data['book_resource'] = $this->lessonplan_model->get_books_resource_sub_wise($data['subject_class']->id);
    $data['main_content'] = 'academics/lesson_plan/session_plan2/detail/index';
    $this->load->view('inc/template', $data);
  }

  public function get_session_plan(){
    $lesson_id = $_POST['lesson_id'];
    $result = $this->lessonplan_model->get_topic_details($lesson_id);
    echo json_encode($result);
  }
  public function generate_lesson_code(){
    $topic_id = $_POST['topic_id'];
    echo $this->lessonplan_model->genearte_lesson_code_last_id($topic_id);
  }

  public function get_new_session_code(){
    $topic_id = $_POST['topic_id'];
    $session_code = $this->lessonplan_model->genearte_lesson_code_last_id($topic_id);
    echo json_encode($session_code); 
  }

  public function add_new_session(){
    $topic_id = $_POST['topic_id'];
    $session_code = $_POST['session_code'];
    $session_description = $_POST['session_description'];
    $result = $this->lessonplan_model->add_new_session($topic_id,$session_code,$session_description);
    echo json_encode($result); 
  }

  public function get_session_details(){
    $session_id = $_POST['session_id'];
    //echo "<pre>";print_r($session_id);die();
    $data['session_details'] = $this->lessonplan_model->get_session_details($session_id);

    $data['learningObjectiveType'] = $this->lessonplan_model->get_learning_objective_by_session_id($session_id);
    $resourceType = $this->lessonplan_model->get_resources_type_details_by_id($session_id);

    foreach($resourceType as $key => $resource){
      if(isset($resource->resource_file)){
        if($resource->resource_type=="Video Link" || $resource->resource_type == "Hyper Link" || $resource->resource_type == "Audio"){
          $resource->resource_url = $resource->resource_file;
        }else{
          $resource->resource_url = $this->filemanager->getSignedUrlWithExpiry($resource->resource_file);
          // $resource->resource_url = $this->filemanager->getFilePath($resource->resource_file);
        }
      }
    };

    $data['resourceType']=$resourceType;
    $data['book_resourceType'] = $this->lessonplan_model->get_book_resources_type_details_by_id($session_id);
    $data['assessmentType'] = $this->lessonplan_model->get_sesssion_assessment_type_id($session_id);
    $data['skillType'] = $this->lessonplan_model->get_sesssion_skill_type_id($session_id);
    //echo "<pre>";print_r($data);die();
    echo json_encode($data); 
  }

  public function delete_resourceType(){
    echo $this->lessonplan_model->delete_resourceType($_POST);
  }

  public function delete_assessmentType(){
    echo $this->lessonplan_model->delete_assessmentType($_POST);
  }

  public function delete_book_resourceType(){
    echo $this->lessonplan_model->delete_book_resourceType($_POST);
  }

  public function delete_learningObjectiveType(){
    echo $this->lessonplan_model->delete_learningObjectiveType($_POST);
  }

  public function delete_skillType(){
    echo $this->lessonplan_model->delete_skillType($_POST);
  }

  public function resourceType($session_id){
    $data["session_id"]=$session_id;
    $data['main_content'] = 'academics/lesson_plan/report/view_resources/resources/index';
    $this->load->view('inc/template', $data);
  }

  public function book_resourceType($session_id){
    $data["session_id"]=$session_id;
    $data['main_content'] = 'academics/lesson_plan/report/view_resources/book_resources/index';
    $this->load->view('inc/template', $data);
  }

  public function learningObjectiveType($session_id){
    $data["session_id"]=$session_id;
    $data['main_content'] = 'academics/lesson_plan/report/view_resources/learning_objective/index';
    $this->load->view('inc/template', $data);
  }

  public function assessmentType($session_id){
    $data["session_id"]=$session_id;
    $data['main_content'] = 'academics/lesson_plan/report/view_resources/assessments/index';
    $this->load->view('inc/template', $data);
  }

  public function skillType($session_id){
    $data["session_id"]=$session_id;
    $data['main_content'] = 'academics/lesson_plan/report/view_resources/skills/index';
    $this->load->view('inc/template', $data);
  }

  public function updateLearningContext() {
    $input = $_POST;
    $value =  $_POST['value'];
    $session_id =  $_POST['id'];
    $visible_to_students = $_POST['visible_to_students'];
    echo json_encode($this->lessonplan_model->updateLearningContext($session_id,$value,$visible_to_students));
  }

  public function updateLearningIntention() {
    $input = $_POST;
    $value =  $_POST['value'];
    $session_id =  $_POST['id'];
    $visible_to_students = $_POST['visible_to_students'];
    echo json_encode($this->lessonplan_model->updateLearningIntention($session_id,$value,$visible_to_students));
    
  }

  public function updateSuccessCriteria() {
    $input = $_POST;
    $value =  $_POST['value'];
    $session_id =  $_POST['id'];
    $visible_to_students = $_POST['visible_to_students'];
    echo json_encode($this->lessonplan_model->updateSuccessCriteria($session_id,$value,$visible_to_students));
  }

  public function updateExtendedLearning() {
    $input = $_POST;
    $value =  $_POST['value'];
    $session_id =  $_POST['id'];
    $visible_to_students = $_POST['visible_to_students'];
    echo json_encode($this->lessonplan_model->updateExtendedLearning($session_id,$value,$visible_to_students));
  }

  public function updateAdditionalInformation() {
    $input = $_POST;
    $value =  $_POST['value'];
    $session_id =  $_POST['id'];
    $visible_to_students = $_POST['visible_to_students'];
    echo json_encode($this->lessonplan_model->updateAdditionalInformation($session_id,$value,$visible_to_students));
  }

  public function updateContengencyPlan() {
    $input = $_POST;
    $value =  $_POST['value'];
    $session_id =  $_POST['id'];
    $visible_to_students = $_POST['visible_to_students'];
    echo json_encode($this->lessonplan_model->updateContengencyPlan($session_id,$value,$visible_to_students));
  }

  public function updateBeginningMinute() {
    $input = $_POST;
    $value =  $_POST['value'];
    $session_id =  $_POST['id'];
    echo json_encode($this->lessonplan_model->updateBeginningMinute($session_id,  $value));
  }

  public function updateBeginningPlan() {
    $input = $_POST;
    $value =  $_POST['value'];
    $session_id =  $_POST['id'];
    $visible_to_students = $_POST['visible_to_students'];
    echo json_encode($this->lessonplan_model->updateBeginningPlan($session_id,$value,$visible_to_students));
  }

  public function updateMiddleMinute() {
    $input = $_POST;
    $value =  $_POST['value'];
    $session_id =  $_POST['id'];
    echo json_encode($this->lessonplan_model->updateMiddleMinute($session_id,  $value));
  }

  public function updateMiddlePlan() {
    $input = $_POST;
    $value =  $_POST['value'];
    $session_id =  $_POST['id'];
    $visible_to_students = $_POST['visible_to_students'];
    echo json_encode($this->lessonplan_model->updateMiddlePlan($session_id,$value,$visible_to_students));
  }

  public function updateEndMinute() {
    $input = $_POST;
    $value =  $_POST['value'];
    $session_id =  $_POST['id'];
    echo json_encode($this->lessonplan_model->updateEndMinute($session_id,  $value));
  }

  public function updateEndPlan() {
    $input = $_POST;
    $value =  $_POST['value'];
    $session_id =  $_POST['id'];
    $visible_to_students = $_POST['visible_to_students'];
    echo json_encode($this->lessonplan_model->updateEndPlan($session_id,$value,$visible_to_students));
  }

  public function updateSessionSkill() {
    $input = $_POST;
    $value =  $_POST['value'];
    $id =  $_POST['id'];
    $this->lessonplan_model->updateSessionSkill($id,  $value);

    echo $value;
  }

  public function updateSessionLearninigObjective() {
    $input = $_POST;
    $value =  $_POST['value'];
    $id =  $_POST['id'];
    $this->lessonplan_model->updateSessionLearninigObjective($id,  $value);

    echo $value;
  }

  public function updateSessionAssessment() {
    $input = $_POST;
    $value =  $_POST['value'];
    $id =  $_POST['id'];
    $this->lessonplan_model->updateSessionAssessment($id,  $value);

    echo $value;
  }

  public function updateSessionBookReference() {
    $input = $_POST;
    $value =  $_POST['value'];
    $id =  $_POST['id'];
    $this->lessonplan_model->updateSessionBookReference($id,  $value);

    echo $value;
  }

  public function get_learning_objective_details(){
    $session_id = $_POST['session_id'];
    $result = $this->lessonplan_model->get_learning_objective_type();
    echo json_encode($result); 
  }

  public function add_new_learning_objective(){
    $session_id = $_POST['session_id'];
    $objective_id = $_POST['objective_id'];
    $learning_objective_description = $_POST['learning_objective_description'];
    $visible_to_students = $_POST['visible_to_students'];

    $result = $this->lessonplan_model->add_new_learning_objective_to_session($session_id,$objective_id,$learning_objective_description,$visible_to_students);
    echo json_encode($result); 
  }

  public function delete_session_learning_objective(){
    $learning_objective_id = $_POST['learning_objective_id'];
    echo json_encode($this->lessonplan_model->delete_session_learning_objective($learning_objective_id));
  }

  public function get_skills_details(){
    // $session_id = $_POST['session_id'];
    $result = $this->lessonplan_model->get_skill_type();
    echo json_encode($result); 
  }

  public function add_new_skill(){
    $session_id = $_POST['session_id'];
    $skill_id = $_POST['skill_id'];
    $skill_description = $_POST['skill_description'];
    $visible_to_students = $_POST['visible_to_students'];
    $result = $this->lessonplan_model->add_new_skill_to_session($session_id,$skill_id,$skill_description,$visible_to_students);
    echo json_encode($result); 
  }

  public function delete_session_skill(){
    $session_skill_id = $_POST['session_skill_id'];
    echo json_encode($this->lessonplan_model->delete_session_skill($session_skill_id));
  }

  public function add_new_beginning(){
    $session_id = $_POST['session_id'];
    $new_beginning_minute = $_POST['new_beginning_minute'];
    $new_beginning_plan = $_POST['new_beginning_plan'];
    $result = $this->lessonplan_model->add_new_beginning($session_id,$new_beginning_minute,$new_beginning_plan);
    echo json_encode($result); 
  }

  public function add_new_middle(){
    $session_id = $_POST['session_id'];
    $new_middle_minute = $_POST['new_middle_minute'];
    $new_middle_plan = $_POST['new_middle_plan'];
    $result = $this->lessonplan_model->add_new_middle($session_id,$new_middle_minute,$new_middle_plan);
    echo json_encode($result); 
  }

  public function add_new_end(){
    $session_id = $_POST['session_id'];
    $new_end_minute = $_POST['new_end_minute'];
    $new_end_plan = $_POST['new_end_plan'];
    $result = $this->lessonplan_model->add_new_end($session_id,$new_end_minute,$new_end_plan);
    echo json_encode($result); 
  }

  public function get_assessment_details(){
    $result = $this->lessonplan_model->get_assessment_type();
    echo json_encode($result); 
  }

  public function add_new_assessment(){
    $session_id = $_POST['session_id'];
    $assessment_type_id = $_POST['assessment_type_id'];
    $new_assessment_remarks = $_POST['new_assessment_remarks'];
    $visible_to_students = $_POST['visible_to_students'];
    $result = $this->lessonplan_model->add_new_assessment($session_id,$assessment_type_id,$new_assessment_remarks,$visible_to_students);
    echo json_encode($result); 
  }

  public function save_beginning_minute(){
    $session_id = $_POST['session_id'];
    $new_beginning_minute = $_POST['new_beginning_minute'];
    $result = $this->lessonplan_model->save_beginning_minute($session_id,$new_beginning_minute);
    echo json_encode($result); 
  }

  public function get_sessions_by_topic(){
    $topic_id = $_POST['topic_id'];
    $result = $this->lessonplan_model->get_sessions_by_topic($topic_id);
    //echo "<pre>";print_r($result);die();
    echo json_encode($result); 
  }

  public function delete_session(){
    $session_id = $_POST['session_id'];
    $result = $this->lessonplan_model->delete_session_by_id($session_id);
    echo json_encode($result); 
  }

  public function get_topic_list(){
    $lesson_id = $_POST['lesson_id'];
    $result = $this->lessonplan_model->get_topic_list_by_id($lesson_id);
    echo json_encode($result); 
  }

  public function insert_session(){
    echo $this->lessonplan_model->insert_session_data();
  }

  public function insert_session_general(){
    echo $this->lessonplan_model->insert_session_general_data();
  }

  public function insert_session_asssment(){
    echo $this->lessonplan_model->insert_session_assessment_data();
  }

  public function lesson_topic(){
    $data['main_content'] = 'academics/lesson_plan/lesson_topic';
    $this->load->view('inc/template', $data);
  }

  public function program_of_work(){
    $data['main_content'] = 'academics/lesson_plan/program_of_work/index';
    $this->load->view('inc/template', $data);
  }

  private function getlmsTilePermissionType($staffId, $staffType){
    return $this->lessonplan_model->getlmsTilePermissionType($staffId, $staffType);
  }

  public function checkinout(){
    $staffId = $this->authorization->getAvatarStakeHolderId();
    // bring permission type (read_only or write) of the current logged in staff
    if($this->authorization->isSuperAdmin()){
      $has_write_permission=1;
    }else{
      $has_write_permission = $this->getlmsTilePermissionType($staffId, "section");
    }

    $has_write_permission = 1;
    $data['has_write_permission'] = $has_write_permission;
    
    $staff_has_rollback_privilege=(int)$this->authorization->isAuthorized("LESSON_PLAN.LMS_ROLLBACK_PRIVILEGE");
    $data['staff_has_rollback_privilege']=$staff_has_rollback_privilege;

    $data['is_semester_scheme'] = $this->settings->getSetting('is_semester_scheme');
    $data['classes'] = $this->ManageSubject_model->getClassesForLms();
    $data['main_content'] = 'academics/lesson_plan/checkinout/index';
    $this->load->view('inc/template', $data);
  }

  public function get_time_table_periods_list_for_lms(){
    $periodsList = $this->lessonplan_model->get_time_table_periods_list_for_lms($_POST);
    echo json_encode($periodsList);
  }
  
  public function session_progress_report(){
    $data['main_content'] = 'academics/lesson_plan/report/session_progress_report/index';
    $this->load->view('inc/template', $data);
  }

  public function school_academic_report(){
    $data['main_content'] = 'academics/lesson_plan/report/session_academic_report/index';
    $this->load->view('inc/template', $data);
  }

  public function grade_academic_report(){
    $data['main_content'] = 'academics/lesson_plan/report/grade_academic_report/index';
    $this->load->view('inc/template', $data);
  }

  public function section_wise_academic_report(){
    $data['main_content'] = 'academics/lesson_plan/report/section_wise_academic_report/index';
    $this->load->view('inc/template', $data);
  }

  public function staff_wise_report(){
    $data['main_content'] = 'academics/lesson_plan/report/staff_wise_report/index';
    $this->load->view('inc/template', $data);
  }

  public function feedback_report(){
    $data['main_content'] = 'academics/lesson_plan/report/feedback_report/index';
    $this->load->view('inc/template', $data);
  }

  public function get_details_session_id(){
    $session_id = $_POST['session_id'];

    $general = $this->lessonplan_model->get_sesssion_details_by_id($session_id);

    $skill = $this->lessonplan_model->get_sesssion_skill_details_by_id($session_id);

    $objects = $this->lessonplan_model->get_sesssion_objects_details_by_id($session_id);

    $assessmentType = $this->lessonplan_model->get_sesssion_assessment_type_id($session_id);

    $resourceType = $this->lessonplan_model->get_resources_type_details_by_id($session_id);

    $book_resourceType = $this->lessonplan_model->get_book_resources_type_details_by_id($session_id);
    
    echo json_encode(array('general'=>$general,'skill'=>$skill , 'objects'=>$objects , 'assessmentType'=>$assessmentType, 'resourceType'=>$resourceType,'book_resourceType'=>$book_resourceType));
  }

  public function get_assessment_type_details(){
    $session_id = $_POST['session_id'];
    $assessmentType = $this->lessonplan_model->get_sesssion_assessment_type_id($session_id);
    echo json_encode($assessmentType);
  }

  public function delete_assessment_type_by_id(){
    $assTypeId = $_POST['assTypeId'];
    echo json_encode($this->lessonplan_model->delete_assessment_type_by_id($assTypeId));
  }

  public function save_resources_details(){
    $session_id = $_POST['session_id'];
    $resources_ids = $_POST['resources_ids'];
    $visible_to_students = $_POST['visible_to_students'];
    echo json_encode($this->lessonplan_model->insert_resources_details($session_id, $resources_ids, $visible_to_students));
  }

  public function save_books_resources_details(){
    $session_id = $_POST['session_id'];
    $book_resources = $_POST['book_resources'];
    $book_page_reference = $_POST['book_page_reference'];
    $visible_to_students = $_POST['student_visibility'];
    echo json_encode($this->lessonplan_model->insert_book_resources_details($session_id, $book_resources, $book_page_reference, $visible_to_students));
  }

  public function get_resources_type_details(){
    $session_id = $_POST['session_id'];
    $resourceType = $this->lessonplan_model->get_resources_type_details_by_id($session_id);
    echo json_encode($resourceType);
  }
  public function delete_resources_type_by_id(){
    $resource_id = $_POST['resource_id'];
    echo json_encode($this->lessonplan_model->delete_resources_type_by_id($resource_id));
  }

  public function get_resources_book_type_details(){
    $session_id = $_POST['session_id'];
    $resourceType = $this->lessonplan_model->get_book_resources_type_details_by_id($session_id);
    echo json_encode($resourceType);
  }
  public function delete_resources_book_type_by_id(){
    $resource_id = $_POST['resource_id'];
    echo json_encode($this->lessonplan_model->delete_resources_book_type_by_id($resource_id));
  }

  public function assessment_types(){
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'academics/lesson_plan/assessment_types_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'academics/lesson_plan/assessment_types_mobile';
    }else{
      $data['main_content'] = 'academics/lesson_plan/assessment_types';    	
    }    
    $this->load->view('inc/template', $data);
  }

  public function add_assessment(){
    $assessment_name = trim($_POST['assessment_name']);
    $success = $this->lessonplan_model->add_assessment($assessment_name);
    echo json_encode($success);
  }

  public function get_assessment_types(){
    $data['assessment_types'] = $this->lessonplan_model->get_assessment_types();
    echo json_encode($data);
  }

  public function delete_assessment(){
    $assessment_id = $_POST['assessment_id'];
    $success = $this->lessonplan_model->delete_assessment($assessment_id);
    echo json_encode($success);

  }

  public function reference_books_view(){
    $data['grades'] = $this->lessonplan_model->getClasses();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'academics/lesson_plan/reference_books_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'academics/lesson_plan/reference_books_mobile';
    }else{
      $data['main_content'] = 'academics/lesson_plan/reference_books';    	
    }
    $this->load->view('inc/template', $data);

  }

  public function viewAllBooksByGrade(){
    $grade = $_POST['grade'];
    $data['viewBooks'] = $this->lessonplan_model->getAllBooksByGrade($grade);
    echo json_encode($data);
  }

  public function updateBookStatusToInactive(){
    $book_id = $_POST['book_id'];
    $result = $this->lessonplan_model->updateBookStatusToInactive($book_id);
    echo json_encode($result);
  }

  public function updateBookStatusToActive() {
    $book_id = $_POST['book_id'];
    $result = $this->lessonplan_model->updateBookStatusToActive($book_id);
    echo json_encode($result);
  }

  public function updateBookName() {
    $input = $_POST;
    $value =  $_POST['value'];
    $book_id =  $_POST['id'];
    $this->lessonplan_model->updateBookName($book_id,  $value);

    echo $value;
  }

  public function updateBookAuthor() {
    $input = $_POST;
    $value =  $_POST['value'];
    $book_id =  $_POST['id'];
    $this->lessonplan_model->updateBookAuthor($book_id,  $value);

    echo $value;
  }

  public function updateBookPublisher() {
    $input = $_POST;
    $value =  $_POST['value'];
    $book_id =  $_POST['id'];
    $this->lessonplan_model->updateBookPublisher($book_id,  $value);

    echo $value;
  }

  public function getSubjectsFromClass(){
    $class_id =  $_POST['class_id'];
    $subject_details = $this->lessonplan_model->getSubjectsFromClass($class_id);
    echo json_encode($subject_details);
  }

  public function save_book_data(){
    $book_added = $this->lessonplan_model->save_book_data();
    echo json_encode($book_added);
  }

  public function in_active_session(){
    $session_id = $_POST['session_id'];
    echo $this->lessonplan_model->in_active_session_by_id($session_id);

  }

  public function lesson_plan_status(){
    $data['classes'] = $this->ManageSubject_model->getClassesForLms();
    $data['staff_list'] = $this->lessonplan_model->get_lp_session_created_staff_list();
    // echo "<pre>"; print_r($data['staff_list']); die();
    if ($this->mobile_detect->isTablet()) {
      $data['main_content'] = 'academics/lesson_plan/report/lesson_plan_status_tablet';
    }else if($this->mobile_detect->isMobile()){
      $data['main_content'] = 'academics/lesson_plan/report/lesson_plan_status_mobile';
    }else{
      $data['main_content'] = 'academics/lesson_plan/report/lesson_plan_status';   	
    }
    $this->load->view('inc/template', $data);
  }

  public function get_lp_plan_report_datas(){
    $staffId = $_POST['staffId'];
    $classMasterId = $_POST['classMasterId'];
    $subjectId = $_POST['subjectId'];
    $result = $this->lessonplan_model->get_lp_plan_status_report($staffId,$classMasterId,$subjectId);
    echo json_encode($result);
  }

  public function design_session($grade="",$subject_id="",$lesson_id="",$topic_id="", $session_id = ""){
    if ($this->authorization->isSuperAdmin()) {
      $has_write_permission = 1;
    } else {
      $staffId = $this->authorization->getAvatarStakeHolderId();
      $has_write_permission = $this->getlmsTilePermissionType($staffId, "Primary") || $this->getlmsTilePermissionType($staffId, "Assistant");
    }
    $data['has_write_permission'] = $has_write_permission;

    $data["lesson_name"]= "";
    $data["topic_name"]= "";
    $data["session_name"]= "";

    if($session_id){
      $session_details=$this->lessonplan_model->get_session_names($session_id);
      $data["lesson_name"] = $session_details->lesson_name;
      $data["topic_name"] = $session_details->topic_name;
      $data["session_name"] = $session_details->session_name;
    }

    $data["grade"]= $grade;
    $data["subject_id"]= $subject_id;
    $data["session_id"]= $session_id;
    $data["lesson_id"]= $lesson_id;
    $data["topic_id"]= $topic_id;
    $data["skill_type"]=$this->lessonplan_model->get_skill_type();

    $data['resource_types'] = $this->lessonplan_model->get_resources_type();
    // $data['subject_class'] = $this->lessonplan_model->get_subject_name($lesson_id);

    if(!empty($data['subject_class'])){
      $lesson_id= $data['subject_class']->id;
      $data['class_name']= $data['subject_class']->class_name;
    }else{
      $lesson_id=0;
      $data['class_name'] = "";
    }
    // echo "<pre>"; print_r($data['subject_class']); die();
    $data['book_resource'] = $this->lessonplan_model->get_books_resource_sub_wise($lesson_id);

    // $data['viewBooks'] = $this->lessonplan_model->getAllBooksByGrade($grade);

    $data['classes'] = $this->ManageSubject_model->getClassesForLms();
    $data['sections'] = json_encode([]);
    $data['is_semester_scheme'] = $this->settings->getSetting('is_semester_scheme');
    $data['main_content'] = 'academics/lesson_plan/report/design_session/index';
    $this->load->view('inc/template', $data);
  }

  public function get_subject_name_by_lesson_id(){
    $subject_class = $this->lessonplan_model->get_subject_name($_POST['lesson_id']);
    echo json_encode($subject_class);
  }

  public function manage_Session(){
    // if($this->authorization->isSuperAdmin()){
    //   $has_write_permission=1;
    // }else{
    //   $staffId = $this->authorization->getAvatarStakeHolderId();
    //   $has_write_permission = $this->getlmsTilePermissionType($staffId, "Primary") || $this->getlmsTilePermissionType($staffId, "Assistant");
    // }

    $data['has_write_permission']=1;

    $data["isLessonPlanAdmin"]=(int)$this->authorization->isAuthorized("LESSON_PLAN.ADMIN");

    // echo (int)$data["isLessonAdmin"]; die();

    $data['classes'] = $this->ManageSubject_model->getClassesForLms();
    $data['sections'] = json_encode([]);
    $data['is_semester_scheme'] = $this->settings->getSetting('is_semester_scheme');
    // $data['is_semester_scheme'] = 1;
    $data['main_content'] = 'academics/lesson_plan/report/manage_session/index';
    $this->load->view('inc/template', $data);
  }

  public function schedule_master() {
    $data['main_content'] = 'academics/lesson_plan/report/schedule_master/index';
    $this->load->view('inc/template', $data);
  }

  public function store_lp_weeks(){
    $result=$this->lessonplan_model->store_lp_weeks($_POST);
    echo json_encode($result);
  }

  public function get_lp_weeks_data(){
    // echo "<pre>"; print_r($_POST); die();
    $data["lp_weeks_data"]=$this->lessonplan_model->get_lp_weeks_data($_POST);
    $data["lp_program_weeks_data"] = $this->lessonplan_model->get_lp_program_weeks_data();
    $data["final_approval_status"]=$this->lessonplan_model->get_final_approval_status($_POST);
    echo json_encode($data);
  }

  public function get_newly_added_lp_weeks_data(){
    $lp_subject_id= $_POST["subjectId"];
    $program_week_id =$_POST["lpWeekId"];
    $lp_program_weeks_data = $this->lessonplan_model->get_lp_program_weeks_data($lp_subject_id, $program_week_id);
    echo json_encode($lp_program_weeks_data);
  }

  public function add_lp_session(){
    echo json_encode($this->lessonplan_model->add_lp_session($_POST));
  }

  public function delete_lp_session(){
    echo json_encode($this->lessonplan_model->delete_lp_session($_POST));
  }

  public function edit_lp_session(){
    echo json_encode($this->lessonplan_model->edit_lp_session($_POST));
  }

  public function get_LP_Subject_name(){
    $data['subject_name']=$this->lessonplan_model->get_LP_Subject_name($_POST);
    $data['grade_name']=$this->lessonplan_model->get_LP_Grade_name($_POST);
    echo json_encode($data);
  }

  public function go_to_previous_week(){
    echo json_encode($this->lessonplan_model->go_to_previous_week($_POST));
  }

  public function get_lesson_details_performance_pointers(){
    $result=$this->lessonplan_model->get_lesson_details_performance_pointers();
    echo json_encode($result);
  }

  public function get_lp_objectives(){
    $this->load->model('academics/objective_model');
    $result=$this->objective_model->getObjectives();
    echo json_encode($result);
  }

  public function download_LP_resource($resource_name,$link){
    $link=str_replace("-","/",$link);

    $file = explode("/", $link);
    $fname = $resource_name .'.'.explode(".", $file[count($file)-1])[1];
    
    $data = file_get_contents($link);
    $this->load->helper('download');
    force_download($fname, $data, TRUE);
  }

  public function session_tracking(){
    $data['classes'] = $this->ManageSubject_model->getClassesForLms();
    $data['sections'] = json_encode([]);
    $data['is_semester_scheme'] = $this->settings->getSetting('is_semester_scheme');
    $data['main_content'] = 'academics/lesson_plan/report/session_tracking/index';
    $this->load->view('inc/template', $data);
  }

  public function get_lp_tracking_details(){
    $result["sessionData"]=$this->lessonplan_model->get_lp_tracking_details($_POST);
    $result["staffData"]=$this->lessonplan_model->get_lp_staff_details($_POST);
    echo json_encode($result);
  }

  public function get_sessionList(){
    $result["sessionList"]=$this->lessonplan_model->get_sessionList($_POST);
    echo json_encode($result);
  }

  public function get_lp_session_tracking_details() {
    $result["sessionData"]=$this->lessonplan_model->get_lp_session_tracking_details($_POST)["session_tarcking_data"];
    $result["staffData"]=$this->lessonplan_model->get_lp_session_tracking_details($_POST)["staff_details"];
    $result["sections"]=$this->lessonplan_model->get_class_sections(["classMasterId"=>$_POST["class_master_id"]]);
    echo json_encode($result);
  }

  public function get_class_sections(){
    $result=$this->lessonplan_model->get_class_sections($_POST);
    echo json_encode($result);
  }

  public function get_subject_lessons(){
    $result=$this->lessonplan_model->get_subject_lessons($_POST);
    echo json_encode($result);
  }

  public function lp_check_in(){
    $result=$this->lessonplan_model->lp_check_in($_POST);
    echo json_encode($result);
  }

  public function update_lp_check_in(){
    $updated_lp_check_in=$this->lessonplan_model->update_lp_check_in($_POST);

    $publishStatus=$_POST["publishStatus"];
    if($publishStatus==1){
      $updated_lp_publish_status = $this->lessonplan_model->update_lp_publish_status(["checkInId"=>$_POST["checkInId"],"status"=>$publishStatus]);
    }
    // $result=$this->lessonplan_model->lp_check_in_feedback($_POST);
    echo json_encode($updated_lp_check_in && $updated_lp_publish_status);
  }
  
  public function check_session_checked_in(){
    $result=$this->lessonplan_model->check_session_checked_in($_POST);
    
    foreach($result as $key => $val){
      $val->check_in_datetime=date("j-F-Y h:i:s A",strtotime($val->check_in_datetime));
      $val->check_out_datetime=date("j-F-Y h:i:s A",strtotime($val->check_out_datetime));
    }
    echo json_encode($result);
  }
  
  public function update_lp_publish_status(){
    $updated_lp_check_in=$this->lessonplan_model->update_lp_publish_status($_POST);
    echo json_encode($updated_lp_check_in);
  }

  // lesson plan again from here
  public function get_lp_programs(){
    $lp_weeks=$this->lessonplan_model->get_lp_programs($_POST);
    echo json_encode($lp_weeks);
  }

  public function update_lp_programs_status(){
    echo json_encode($this->lessonplan_model->update_lp_programs_status($_POST));
  }

  public function get_semesters(){
    $class_master_id = $_POST['class_master_id'];
    $semesters = $this->ManageSubject_model->get_class_semesters($class_master_id);
    echo json_encode($semesters);
  }
  public function check_LP_program_Already_exists(){
    echo $this->lessonplan_model->check_LP_program_Already_exists();
  }

  public function get_classes_for_lp_programs(){
    $classes= $this->lessonplan_model->get_classes_for_lp_programs($_POST);
    // echo "<pre>"; print_r($classes); die();
    echo json_encode($classes);
  }

  public function add_classes_for_lp_programs(){
    echo json_encode($this->lessonplan_model->add_classes_for_lp_programs($_POST));
  }

  public function get_added_classes_of_lp_programs(){
    $classes = $this->lessonplan_model->get_added_classes_of_lp_programs($_POST);
    // echo "<pre>"; print_r($classes); die();
    echo json_encode($classes);
  }

  public function remove_classes_of_lp_programs(){
    echo json_encode($this->lessonplan_model->remove_classes_of_lp_programs($_POST));
  }

  public function check_is_already_exists_added_classes_inlp_programs(){
    $addedClasses = $this->lessonplan_model->get_added_classes_of_lp_programs($_POST);
    echo $this->lessonplan_model->check_is_already_exists_added_classes_inlp_programs($addedClasses,$_POST);
  }

  public function get_lp_weeks_using_programs_id(){
    $weeks=$this->lessonplan_model->get_lp_weeks_using_programs_id($_POST);
    echo json_encode($weeks);
  }

  public function update_lp_weeks(){
    echo json_encode($this->lessonplan_model->update_lp_weeks($_POST));
  }

  public function get_deactive_lp_sessions(){
    $lp_sessions = $this->lessonplan_model->get_deactive_lp_sessions($_POST);
    echo json_encode($lp_sessions);
  }

  public function active_lp_sessions(){
    echo json_encode($this->lessonplan_model->active_lp_sessions($_POST));
  }

  public function update_plan_syllabus_final_approval_status(){
    echo json_encode($this->lessonplan_model->update_plan_syllabus_final_approval_status($_POST));
  }

  public function move_week_session_to_other_week(){
    echo $this->lessonplan_model->move_week_session_to_other_week($_POST);
  }
}