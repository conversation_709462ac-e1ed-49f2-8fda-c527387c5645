<link rel="stylesheet" type="text/css" id="theme" href="<?php echo base_url() ?>assets/css/fullcalendar/fullcalendar.css" />

<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
    <li>Student 360 Degree</li>
</ul>

<div class="col-md-12">
    <div class="card cd_border" style="height:auto;">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                    <h3 class="card-title panel_title_new_style_staff">
                        Student 360 Degree
                    </h3>
                </div>
            </div>
        </div>

        <div class="card-body" style="position: relative;z-index: 10;">
            <div class="col-md-2 form-group">
                <select class="form-control" name="class_section" onchange="onchange_class_load_function()" id="classSectionId">
                    <option value="">Select Class/Section</option>
                    <?php foreach ($getclassinfo as $key => $val) { ?>
                        <option <?php if($class_id == $val->id) echo 'selected' ?> value="<?php echo $val->id ?>"><?php echo $val->class_name . '' . $val->section_name ?></option>
                    <?php } ?>
                </select>
            </div>
            <div class="col-md-2 form-group">
                <select class="form-control" name="select_student" id="selectStudents" onchange="onchange_student_selection()">
                    <option value="">Select Student</option>
                </select>
            </div>

            <div class="col-md-2 form-group">
                <input type="text" class="form-control"  autofocus="" autocomplete="off" placeholder="Search by Student name " id="staff_studentid" name="staff_student" >
            </div>

        </div>
        <div class="card-body" id="befoe_selecting">
            <h5>Please select the student to continue</h5>
        </div>


        <div class="card-body" id="show_student_after_selection" style="height:75vh;display: none;">
            <div class="col-md-2" style="margin-top : -2.5vh">
                <div class="panel panel-default" style="border: none; box-shadow:noe">
                    <div class="panel-body profile">
                        <div class="profile-image">
                            <img width="150px" src="<?php echo base_url() . 'assets/img/sample_boy_image.png' ?>" alt="Avatar">
                        </div>
                        <div class="profile-data">
                            <div id="profile-data-name"></div>
                        </div>
                    </div>
                </div>


                <div class="panel-body list-group border-bottom" style="padding: 0px 1px;height: 40vh; ">
                    <?php

                    $navTabs = array(

                        'personal_details' => array('url' => '', 'display' => 'Personal Details', 'permissions' => $this->authorization->isAuthorized('STUDENT_360.MODULE')),

                        'school_details' => array('url' => '', 'display' => 'School Details', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.SCHOOL_DETAILS')),

                        'documents' => array('url' => '', 'display' => 'Documents', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.DOCUMENTS')),

                        'prev_school_details' => array('url' => '', 'display' => 'Previous School Details', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.PREV_SCHOOL_DETAILS')),

                        'student_health' => array('url' => '', 'display' => 'Health Report', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.STUDENT_HEALTH')),

                        'single_window' => array('url' => '', 'display' => 'Single Window Process', 'permissions'=> $this->settings->getSetting('enable_indus_single_window_approval_process') && $this->authorization->isAuthorized('STUDENT_360.SINGLE_WINDOW_PROCESS')),

                        'student_medical_forms' => array('url' => '', 'display' => 'Hospitalization Details', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.STUDENT_MEDICAL_FORM')),

                        'student_consent_forms' => array('url' => '', 'display' => 'Student Consent Forms', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.STUDENT_CONSENT_FORMS')),

                        'fees' => array('url' => '', 'display' => 'Fees', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.FEES')),

                        'circular' => array('url' => '#', 'display' => 'Circular', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.CIRCULARS')),

                        'sms' => array('url' => '', 'display' => 'SMS', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.SMS')),

                        'attendance' => array('url' => '', 'display' => 'Attendance', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.ATTENDANCE')),

                        'examination' => array('url' => '', 'display' => 'Examination', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.EXAMINATION')),

                        'observation' => array('url' => '', 'display' => 'Observation', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.OBSERVATION')),

                        'academicanalysis' => array('url' => '', 'display' => 'Academic Analysis', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.ACADEMICANALYSIS')),

                        'non_compliance' => array('url' => '', 'display' => 'Non Compliance', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.NON_COMPLIANCE')),

                        'transportation' => array('url' => '', 'display' => 'Transportation', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.TRANSPORTATION')),

                        'login_report' => array('url' => '', 'display' => 'Login Report', 'permissions'=> $this->authorization->isAuthorized('STUDENT_360.RFID_REPORT'))
                    );
                    ?>
                    <?php
                    $i = 1;
                    foreach ($navTabs as $key => $value) { 
                        if ($value['permissions'] != '0') { ?>
                        <a href="#<?php echo $key ?>" role="tab" style="border-radius: 10px;margin-bottom: 0.5rem;" onclick="get_active_tab_data('<?php echo $key ?>')" class="list-group-item <?php if ($i == 1) echo 'active' ?> " data-toggle="tab" aria-expanded="true">
                            <span style="float: right;" class="glyphicon glyphicon-chevron-right"></span><?php echo $value['display'] ?>
                        </a>
                     <?php }
                        ?>

                    <?php $i++;
                    }
                    ?>

                </div>

            </div>

            <div class="col-md-10">
                <!-- <div class="panel panel-default tabs"> -->

                <!-- <div class="panel-body tab-content"> -->
                <div class="tab-pane fade in active" id="personal_details" style="overflow-y:scroll;height:45vh;">
                    <div class="container" style="margin-bottom:10px;">
                        <input type="hidden" id="tablSite_url_personal_details" value="<?php echo site_url('student/Student_controller/addMoreStudentInfo') ?>">
                        <a target="_blank" id="url_key_personal_details" class="btn btn-primary pull-right" href=""><i class="fa fa-trash"></i> Edit</a>
                    </div>
                    <h4>Personal Details</h4>
                    <table class="table table-bordered" id="student_personal_details">
                        
                    </table>

                    <table class="table table-bordered" id="father_details">
                    <h4>Father Details</h4>
                       
                    </table>

                    <table class="table table-bordered" id="mother_details">
                    <h4>Mother Details</h4>
                        
                    </table>

                    <table class="table table-bordered" id="siblings" >
                     
                    </table>

                    <table class="table table-bordered" id="guardian_details">
                   
                           
                        </table>

                     <table class="table table-bordered" id="guardian2_details">
                   
                            
                        </table>

                         <table class="table table-bordered" id="driver_details">
                    
                           
                        </table>

                        <table class="table table-bordered" id="driver2_details">
                    
                           
                        </table>

                    <?php if (!empty($custom) || !empty($custom_sy)) { ?>

                        <h4>Addtional Fields</h4>
                        <table class="table table-bordered table-hover">
                            <thead>
                                <?php
                                foreach ($chunkData as $value) {
                                    echo '<tr>';
                                    foreach ($value as $key => $val) {
                                        echo '<th width="20%">' . $custom[$val] . '</th>';
                                        echo '<td  width="30%" id=' . $val . '></td>';
                                    }
                                    echo '<tr>';
                                } 
                                
                                foreach ($chunkData_sy as $value) {
                                    echo '<tr>';
                                    foreach ($value as $key => $val) {
                                        echo '<th width="20%">' . $custom_sy[$val] . '</th>';
                                        echo '<td  width="30%" id=' . $val . '></td>';
                                    }
                                    echo '<tr>';
                                } 
                                ?>
                            </thead>
                        </table>

                    <?php } ?>
                </div>
                
                <div class="tab-pane fad" id="school_details" style="overflow-y:scroll;height:50vh;">
                    <div class="container" style="margin-bottom:10px;">
                        <input type="hidden" id="tablSite_url_school_details" value="<?php echo site_url('student/Student_controller/addMoreStudentInfo') ?>">
                        <a target="_blank" id="url_key_school_details" class="btn btn-primary pull-right" href=""><i class="fa fa-trash"></i> Edit</a>
                    </div>
                    <table class="table table-bordered table-hover" id="school_details_info">
                        <h4>School Details</h4>
                       
                    </table>

                </div>
                <div class="tab-pane fade" id="documents" style="overflow-y:scroll;height:50vh;">
                    <div id="student_school_documents">
                    </div>
                    <div id="pan_aadhar_documents">
                    </div>
                </div>
                <div class="tab-pane fade" id="prev_school_details" style="overflow-y:scroll;height:50vh;">

                </div>
                <div class="tab-pane fade" id="fees" style="overflow-y:scroll;height:50vh;">
                    <div id="fee_summary_details">
                    </div>
                </div>
                <div class="tab-pane fade" id="circular" style="overflow-y:scroll;height:50vh;">

                </div>
                <div class="tab-pane fade" id="sms" style="overflow-y:scroll;height:50vh;">

                </div>

                <div class="tab-pane" id="attendance" style="overflow-y:scroll;height:50vh;">
                    <div class="row col-md-12" style="margin-bottom:1rem">
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="pwd">From</label>
                                <div class="input-group date" id="datetimepicker6">
                                    <input placeholder="From Month" value="From Month" required="" type="text" class="form-control date_pick" autocomplete="off" id="from_date" name="report_month_from" data-parsley-id="4">
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="pwd">To</label>
                                <div class="input-group date" id="datetimepicker7">
                                    <input placeholder="To Month" value="To Month" required="" type="text" class="form-control date_pick" autocomplete="off" id="to_date" name="report_month_to" data-parsley-id="6">
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1" style="margin-top:2rem">
                            <div class="form-group">
                                <label></label>
                                <button type="submit" onclick="get_attendance()" class="btn btn-primary btn-mag">Get</button>
                            </div>
                        </div>
                    </div>

                    <table class="table table-sm table-bordered" id="attendance_table">
                       
                    </table>
                </div>

                
<div class="tab-pane" id="examination" style="overflow: auto; height: 2000px;">
    <select style="display: none;" name="assessments" size="8" id="assessments" class="form-control" multiple></select>

    <!-- Assessment wise Overall Percentage Section -->
    <div class="col-md-12">
        <div id="show_hide_ass_wise_total" class="panel-header" style="display: flex; justify-content: space-between; align-items: center;">
            <h3>Assessment wise Overall Percentage</h3>
            <div style="width: fit-content;">
                <select id="graph_type3" onchange="onchange_graph_type('graph_type3', 3)" class="form-control">
                    <option value="line">Line Graph</option>
                    <option value="bar">Bar Graph</option>
                </select>
            </div>
        </div>
        <div class="col-md-12" style="opacity: 0; height: 15px;">l</div>
        <div class="row m-0" id="total_subjects_per_assessment_Linegraph" style="display: block; height: 300px; width: 100%;"></div>
        <div class="row m-0" id="total_subjects_per_assessment_Bargraph" style="display: block; height: 300px; width: 100%;"></div>
    </div>

    <div class="col-md-12" style="height: 50px; opacity: 0;">l</div>

    <!-- Assessment wise Subject Percentage Section -->
    <div class="col-md-12">
        <div id="show_hide_ass_wise" class="modal-header" style="display: flex; justify-content: space-between; align-items: center;">
            <h3>Assessment wise Subject Percentage</h3>
            <div style="display: flex; align-items: center; gap: 10px;">
                <select style="" name="selected_subjects[]" id="selected_subjects" class="form-control" multiple title="Select Subjects"></select>
                <button class="btn btn-info" onclick="onclick_subject_wise_percentage()">Graph</button>
                <select style="max-width: fit-content; align-items: flex-end;" id="graph_type2" onchange="onchange_graph_type('graph_type2', 2)" class="form-control">
                    <option value="line">Line Graph</option>
                    <option value="bar">Bar Graph</option>
                </select>
            </div>
        </div>
        <div class="col-md-12" style="opacity: 0; height: 15px;">l</div>
        <div class="row m-0" id="subjects_per_assessment_graph" style="display: block; height: 500px; width: 100%;"></div>
    </div>

    <div class="col-md-12" style="opacity: 0; height: 50px;">l</div>

    <!-- Student Performance Section -->
    <div class="col-md-12" style="display: flex; justify-content: space-between; align-items: center;">
        <div class="col-md-9"><b>Student Performance</b></div>
        <div class="col-md-3">
            <ul class="panel-controls" style="list-style: none; padding: 0; margin: 0;">
                <li>
                    <select class="form-control" style="max-width: fit-content; align-items: flex-end;" id="acad_year_id">
                        <?php
                            foreach ($acad_years as $k => $year) {
                                $selected = '';
                                if ($year->id == $acad_year_id) {
                                    $selected = 'Selected';
                                }
                                echo '<option value="' . $year->id . '" ' . $selected . '>' . $year->acad_year . '</option>';
                            }
                        ?>
                    </select>
                </li>
            </ul>
            <div class="col-md-12" style="opacity: 0; height: 25px;">l</div>
        </div>
    </div>

    <!-- Performance and Analysis Sections -->
    <div class="card-body" id="performance"></div>
    <div class="card-body" id="analysis"></div>
</div>

                <div class="tab-pane" id="observation" style="overflow-y:scroll;height:50vh;">

                </div>
                <div class="tab-pane" id="academic_analysis" style="overflow-y:scroll;height:50vh;">
<!-- <div class="card-body" id="performance"></div> -->
                </div>
                <div class="tab-pane fade" id="non_compliance" style="overflow-y:scroll;height:50vh;">

                </div>
                <div class="tab-pane fade" id="transportation" style="overflow-y:scroll;height:50vh;">

                </div>

                <div class="tab-pane fade" id="login_report" style="overflow-y:scroll;height:50vh;">

                </div>

                <div class="tab-pane fade" id="single_window" style="overflow-y:scroll;height:50vh;">

                </div>
                

                <div class="tab-pane fade" id="student_health" style="overflow-y:scroll;height:50vh;">
                    <h4>Student Health</h4>


                <table class="table table-bordered table-hover" id="student_health_table" style="width: 80%;">
                        
                </table>

                <h4>Student Vaccination Details</h4>
                <table class="table table-bordered table-hover" id="student_vacination_table" style="width: 80%;">
                        
                </table>

                </div>

                <div class="tab-pane fade" id="student_medical_forms" style="overflow-y:scroll;height:50vh;">
                </div>

                <div class="tab-pane fade" id="student_consent_forms" style="overflow-y:scroll;height:50vh;">
                </div>

                <!-- </div> -->
                <!-- </div> -->
            </div>

        </div>
    </div>

</div>


<div id="myModal" class="modal">
    <div class="hfc">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
    <span aria-hidden="true">&times;</span>
    </button>
    <img class="modal-content" id="img01">
</div>
</div>

<?php $this->load->view("student_analytics/__student_consent_form_modal.php") ?>

<script type="text/javascript" src="<?php echo site_url('assets/js/plugins/morris/raphael-min.js') ?>"></script>
<script type="text/javascript" src="<?php echo site_url('assets/js/plugins/morris/morris.min.js') ?>"></script>

<script type="text/javascript" src="<?php echo base_url() ?>assets/js/plugins/moment.min.js"></script>
<script type="text/javascript" src="<?php echo base_url() ?>assets/js/plugins/fullcalendar/fullcalendar.min.js"></script>
<script type="text/javascript" src="<?php echo base_url() ?>assets/js/plugins_student_report.js"></script>

<script>


$(document).ready(function(){
    get_active_tab_data('personal_details');
});


    // function getimgsrcpath(){
    //     var srcPath = $('#previewing_family').attr('src');
    //     var modal = document.getElementById('myModal');    
    //     var modalImg = document.getElementById('img01');
    //     modal.style.display = 'block';
    //     modalImg.src = srcPath;
    // }
    var span = $('.close')[0];
    span.onclick = function() {
        modal.style.display ='none';
    }
    $(document).ready(function() {
        // Initialize the start date picker
        $('#from_date').datepicker({
            format: 'd-m-yyyy',
            autoclose: true,
            todayHighlight: true,
        
        });

        // Initialize the end date picker
        $('#to_date').datepicker({
            format: 'd-m-yyyy',
            autoclose: true,
            todayHighlight: true,
            startDate:'+1d'
            
        });

        // Add event listener for the start date picker
        $('#from_date').on('changeDate', function(e) {
            // Get the selected start date
            var startDate = new Date(e.date.valueOf());

            // Set the minimum date of the end date picker to be the start date + 1 day
            $('#to_date').datepicker('setStartDate', new Date(startDate.setDate(startDate.getDate() + 1)));
        });

        // Add event listener for the end date picker
        $('#to_date').on('changeDate', function(e) {
            // Get the selected end date
            var endDate = new Date(e.date.valueOf());

            // Set the maximum date of the start date picker to be the end date - 1 day
            //$('#from_date').datepicker('setEndDate', new Date(endDate.setDate(endDate.getDate() - 1)));
        });
    });
    
</script>

<style>

#img01 {
  /* border-radius: 5px;
  cursor: pointer;
  transition: 0.3s; */
  filter:blur();
}

#myImg:hover {opacity: 0.7;}


/* Caption of Modal Image */
#caption {
  margin: auto;
  display: block;
  width: 80%;
  max-width: 700px;
  text-align: center;
  color: #ccc;
  padding: 10px 0;
  height: 150px;
}

/* Add Animation */
.modal-content, #caption {  
  -webkit-animation-name: zoom;
  -webkit-animation-duration: 0.6s;
  animation-name: zoom;
  animation-duration: 0.6s;
}

@-webkit-keyframes zoom {
  from {-webkit-transform:scale(0)} 
  to {-webkit-transform:scale(1)}
}

@keyframes zoom {
  from {transform:scale(0)} 
  to {transform:scale(1)}
}

/* The Close Button */
.close {
  position: absolute;
  top: 15px;
  right: 35px;
  color: #f1f1f1;
  font-size: 40px;
  font-weight: bold;
  transition: 0.3s;
}

.close:hover,
.close:focus {
  color: #bbb;
  text-decoration: none;
  cursor: pointer;
}

/* 100% Image Width on Smaller Screens */
@media only screen and (max-width: 700px){
  .modal-content {
    width: 100%;
  }
}
</style>
<style type="text/css">
    .nav-tabs>li>a.active {
        background: #6793ca;
        color: #fff;
    }

    .navSub>li>a.active {
        background: #6793ca;
        color: #fff;
    }

    .list-group-item.active {
        /* //z-index: 2; */
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }
    \.list-group-item.active:hover, .list-group-item.active:focus {
    /* z-index: 2; */
    /* color: #fff;
    background-color: #428bca;
    border-color: #428bca; */
}
    
</style>


<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script>
    let current_subs_arr = [];
    let current_subs_name_arr = [];
    let current_assessments_arr = [];

    var acad_year;
    function get_student_academic_data() {
        acad_year = $("#acad_year_id").val();
        getPerformanceData(acad_year);
        currentSubId = '0';
        currentSubName = '';
        $("#subName").html(currentSubName);
        callGetMarksAnalysis(currentSubId, currentSubName);
    }

    function callGetMarksAnalysis(id, name){
        currentSubId = id;
        currentSubName = name;
        $("#subName").html(currentSubName);
        var check = [];
        $('[name="assIds[]"]:checked').each(function () {
            check.push($(this).val());
        });
    
        if (check.length > 0) {
            current_assessments_arr= check;
            getMarksAnalysis(check, id, name);
            // _construct_subs_per_ass_graph(check, id, name);
            $("#show_hide_ass_wise").show();
            onclick_subject_wise_percentage();
            _construct_total_subs_per_ass_graph(check, id, name);
        }
            
    }

    function callSubjectChange(){
        callGetMarksAnalysis();
    };

    function setSubject(){
        callGetMarksAnalysis(currentSubId, currentSubName);
    }
    
    $("#acad_year_id").change(function(){
        acad_year = $(this).val();
        getPerformanceData(acad_year);
    });

    function onchange_graph_type(type_selector, type) {
        var graph_type= $(`#${type_selector}`).val();
        
            if(type == '1') {
                if(graph_type == 'line') {
                    $("#marksAnalysis-line").show();
                    $("#marksAnalysis-Bar").hide();
                } else {
                    $("#marksAnalysis-line").hide();
                    $("#marksAnalysis-Bar").show();
                }
            } else if(type == '3') {
                if(graph_type == 'line') {
                    $("#total_subjects_per_assessment_Linegraph").show();
                    $("#total_subjects_per_assessment_Bargraph").hide();
                } else {
                    $("#total_subjects_per_assessment_Linegraph").hide();
                    $("#total_subjects_per_assessment_Bargraph").show();
                }
            } else {
                $("#subjects_per_assessment_graph").html(`<div class="flex justify-center items-center h-full">
                    <div>
                        <div class="spinner-border text-primary" role="status" style="font-size: large; height: 120px; width: 120px;">
                            <span class="sr-only">Loading...</span>
                        </div>
                    </div>
                </div>`);
                onclick_subject_wise_percentage();
            }
        
    }

    function getMarksAnalysis(assIds, subject, subjectName){
        var stdId = $("#selectStudents").val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/getMarksAnalysis'); ?>',
            type: 'POST',
            data: {'assIds':assIds, 'subject':subject, 'stdId':stdId, 'acad_year': acad_year},
            dataType: 'json',
            success: function(stdData){
                $("#marksAnalysis-line").html('');
                $("#marksAnalysis-Bar").html('').hide();
                Morris.Line({
                  element: 'marksAnalysis-line',
                  data: stdData,
                  xkey: 'assName',
                  axes: 'y',
                  ykeys: ['Average', subjectName],
                  labels: ['Average', subjectName],
                  resize: true,
                  parseTime: false,
                  hideHover: true,
                  lineColors: ['#33414E', '#95B75D']
                });

                Morris.Bar({
                  element: 'marksAnalysis-Bar',
                  data: stdData,
                  xkey: 'assName',
                  ykeys: ['Average', subjectName],
                  labels: ['Average', subjectName],
                  resize: true,
                  parseTime: false,
                  hideHover: true,
                  lineColors: ['#33414E', '#95B75D']
                });

                var graph_type= $("#graph_type").val();
                if(graph_type == 'line') {
                    $("#marksAnalysis-line").show();
                    $("#marksAnalysis-Bar").hide();
                } else {
                    $("#marksAnalysis-line").hide();
                    $("#marksAnalysis-Bar").show();
                }
            }
        })
        .fail(function(){
            // alert("Something Went Wrong!");
        });
    }

    var student_id = '<?php echo $student_id ?>';
$(document).ready(function(){
    onchange_class_load_function();
});
    function onchange_class_load_function() {
        var classSectionId = $("#classSectionId").val();
        if (classSectionId != "") {
             $.post("<?php echo site_url('student_analytics/student_analytics/get_student_by_class_section'); ?>", {
            classSectionId: classSectionId
        }, function(data) {
            var resultData = $.parseJSON(data);
            console.log(resultData);
            var output1 = '';
            var stdName = resultData;

            output1 += '<option value="">Select Student</option>';
            for (var i = 0, j = stdName.length; i < j; i++) {
                var selectStd ='';
                if(stdName[i].id == student_id){
                    selectStd = 'selected';
                }
                output1 += '<option value="' + stdName[i].id + '"  '+selectStd+'>' + stdName[i].name + ' </option>';
            }
            $("#selectStudents").html(output1);
          get_student_analyticsbyid();
          onchange_section();
        });
        }
       
    }

function onchange_section(){
    var section_id = $("#classSectionId").val();
    // var is_checked= $("#show_derived_assessments").is(':checked') ? 1 : 2;
    // $('#students').html('<option value="0">--- Select Student ---</option>');
    $('#assessments').html('<option value="0">--- Select Assessments ---</option>');
    $.ajax({
        url: "<?php echo site_url('examination/assessment_reports/getSectionStudentsAndAssessments');?>",
        data: {'section_id': section_id, 'is_checked': 2, 'assessment_type': 'Manual'},
        type: 'post',
        success: function(data) {
        var data = JSON.parse(data);
        var students = data.students;
        var assessments = data.assessments;
        // console.log('k1k2k3',data)
        // if(students.length > 0) {
        //     var option = '';
        //     for(var i=0; i<students.length; i++){
        //         option += '<option value="'+students[i].student_id+'">'+students[i].student_name+'</option>';
        //     }
        //     $("#students").append(option);
        // }
        if(assessments.length > 0) {
            var option = '';
            for(var i=0; i<assessments.length; i++){
                option += '<option selected value="'+assessments[i].id+'">'+assessments[i].assessment_name+'</option>';
            }
            $("#assessments").html(option);
        }
        },
        error: function(err) {
        console.log(err);
        }
    });
}
    

    function onchange_student_selection() {
        //Get the basic student data
        get_student_analyticsbyid();
        $('#befoe_selecting').hide();

        //Get the active tab data
        get_active_tab_data();
        var select_val = $('#selected_staff_id').val();
        if (select_val == '-1') {
            $('#befoe_selecting').show();
            $('#show_student_after_selection').hide();
        }

    }

    function onclick_student_selection() {
       
        get_student_analyticsbyid();
        $('#befoe_selecting').hide();

        //Get the active tab data
        get_active_tab_data();
        var select_val = $('#staff_studentid').val();
        if (select_val == '-1') {
            $('#befoe_selecting').show();
            $('#show_student_after_selection').hide();
        }
    }

    function get_active_tab_data(key) {
        let current_subs_arr = [];
    let current_subs_name_arr = [];
    let current_assessments_arr = [];
        if(key == 'examination') {
            get_student_academic_data();
        }
        //For some reason, it takes time to set the 'active' tab. Hence using a timer to sleep until the 'active' tab is rendered.
        var timer = setInterval(function() {
            clearInterval(timer);
            display_data();
        }, 500);
        
        var taburl = $('#tablSite_url_'+key).val();
        if(key == undefined || key =='personal_details'){
            var taburl = $('#tablSite_url_personal_details').val();
            key ='personal_details';
        }
        var student_id = $('#selectStudents').val();       
        var gotourl = taburl + '/' + student_id;
        
        // if (key == 'attendance' || key == 'leave_records' || key == 'message') {
        //     var gotourl = taburl;
        // }
        $('#url_key_' + key).attr('href', gotourl);
    }

    function display_data() {

        var attr = $('.active').attr('href');
        if (attr == undefined) {
            return false;
        }
        var targetKey = attr.replace("#", '');
        $('.tab-pane').css('display', 'none');
        $('.active').css('display', 'block');
        switch (targetKey) {
            case 'school_details':
                select_school_details();
                break;
            case 'documents':
                display_documents();
                break;
            case 'prev_school_details':
                display_prev_schooling_details();
                break;
            case 'fees':
                get_fees();
                break;
            case 'sms':
                get_sms();
                break;
            case 'circular':
                get_student_circular();
                break;
            case 'observation':
                get_student_observation();
                break;
            case 'non_compliance':
                get_non_compliance();
                break;
            case 'transportation':
                get_transposrtation_details();
                break;
            case 'student_health':
                get_health_report();
                break;
            case 'single_window':
                single_window_details();
                break;
            case 'student_consent_forms':
                get_student_consent_forms();
                break;
            case 'student_medical_forms':
                get_student_medical_forms();
                break; 
            case 'login_report':
                get_login_report();
                break;    

        }


    }
    function toTitleCase(str) {
        return str.replace(/(?:^|\s)\w/g, function(match) {
        return match.toUpperCase();
    });
}

    function get_student_analyticsbyid() {
        
       
        //$('#beforeselecting').hide();
        var selectStudents = $("#selectStudents :selected").text();

        //$('#profile-data-name').html(selectStudents);
        var student_id = $('#selectStudents').val();
       
        if(student_id == ''){
            return false;
        }
        $('#show_student_after_selection').show();
        $.post("<?php echo site_url('student_analytics/student_analytics/get_student_databyid'); ?>", {
            student_id: student_id
        }, function(data) {
            $('#befoe_selecting').hide();
            var resData = $.parseJSON(data);
            var resultData = resData.student_data;
            var fresultData = resData.student_data.father;
            var mresultData = resData.student_data.mother;
            var enabledField = resData.enabled_field;  
            var f_enabled_field = resData.f_enabled_field;  
            var gresultData = resData.student_data.guardians;
            // console.log(gresultData);
            var g2resultData = resData.student_data.guardians2;
            var g_enabled_field = resData.g_enabled_field;  

            var dresultData = resData.student_data.driver;
            var d2resultData = resData.student_data.driver2;
            var d_enabled_field = resData.d_enabled_field;  
           
            const requiredColumns = ["custom1", "custom2", "custom3", "custom4", "custom5","custom6", "custom7", "custom8", "custom9", "custom10","sy_custom1","sy_custom2","sy_custom3","sy_custom4","sy_custom5"];

            requiredColumns.forEach(function (key) {
                let cell = document.getElementById(key);
                if (cell && resData.student_data[key] !== undefined) {
                    cell.textContent = resData.student_data[key]; // Update only the required fields
                }
            });

            var std_html = '';
             for (var s in enabledField){
                 std_html +='<tr>';
                 for (var i = 0; i < enabledField[s].length; i++) {
                    heading = enabledField[s][i].replace(/_/g, ' ');
                    std_html +='<th style="background:#f1f5f9">'+toTitleCase(heading);+'</th>';
                    var result_values = resultData[enabledField[s][i]];
                    if(resultData[enabledField[s][i]] == null){
                        result_values = '';
                    }
                    std_html +='<td>'+result_values+'</td>';
                 }
                 std_html +='</tr>';
             }

            $('#student_personal_details').html(std_html);

            var father_html = '';
             for (var f in f_enabled_field){
                 father_html +='<tr>';
                 for (var i = 0; i < f_enabled_field[f].length; i++) {
                    heading = f_enabled_field[f][i].replace(/_/g, ' ');
                    father_html +='<th style="background:#f1f5f9">'+toTitleCase(heading);+'</th>';

                    var result_father_values = fresultData[f_enabled_field[f][i]];
                    if(f_enabled_field[f][i] =='picture_url'){
                        if(result_father_values == ''){
                            father_html +='<td><img id="previewing_family" onclick="getimgsrcpath()" class="preview_big_image" name="family_photograph" style="width: 50px;height: 50px;" src="https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Parent App Icons 64px/male_placeholder_icon.png"/></td>';
                        }else{
                             father_html +='<td><img id="previewing_family" onclick="getimgsrcpath()" class="preview_big_image" name="family_photograph" style="width: 50px;height: 50px;" src="'+result_father_values+'"/></td>';
                        }
                    
                    }else{
                        if(fresultData[f_enabled_field[f][i]] == null){
                            result_father_values = '';
                        }

                        father_html +='<td>'+result_father_values+'</td>';
                    }
                    
                 }
                 father_html +='</tr>';
             }

            $('#father_details').html(father_html);

            var mother_html = '';
             for (var f in f_enabled_field){
                 mother_html +='<tr>';
                 for (var i = 0; i < f_enabled_field[f].length; i++) {
                    heading = f_enabled_field[f][i].replace(/_/g, ' ');
                    mother_html +='<th style="background:#f1f5f9">'+toTitleCase(heading);+'</th>';
                    var result_mother_values = mresultData[f_enabled_field[f][i]];

                   if(f_enabled_field[f][i] == 'picture_url'){
                        if(result_mother_values == ''){
                            mother_html +='<td><img id="previewing_family" onclick="getimgsrcpath()" class="preview_big_image" name="family_photograph" style="width: 50px;height: 50px;" src="https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Parent App Icons 64px/male_placeholder_icon.png"/></td>';
                        }else{
                             mother_html +='<td><img id="previewing_family" onclick="getimgsrcpath()" class="preview_big_image" name="family_photograph" style="width: 50px;height: 50px;" src="'+result_mother_values+'"/></td>';
                        }
                    }else{
                        if(mresultData[f_enabled_field[f][i]] == null){
                            result_mother_values = '';
                        }
                        mother_html +='<td>'+result_mother_values+'</td>';
                    }

                   
                 }
                 mother_html +='</tr>';
             }

            $('#mother_details').html(mother_html);

            //siblings
            if (resultData['siblings_name'] != '') {
                var html  = '';
                for (var i = 0; i < resultData['siblings_name'].length; i++) {
                    if(resultData['siblings_name'][i].id != resultData.id){
                        var urlSibling = '<?php echo site_url('student_analytics/student_analytics/index/') ?>'+resultData['siblings_name'][i].id+'/'+resultData['siblings_name'][i].class_section_id;
                        html += '<tr>';
                        html += '<td colspan="4" style="font-size: 16px;"><strong>' + resultData['siblings_name'][i].student_name + '(' + resultData['siblings_name'][i].class_name + ')' + '</strong> (Siblings) <a class="btn btn-info"  href="'+urlSibling+'">View Profile</a> </td>';
                    }
                   
                   html += '</tr>';
                }
                $('#siblings').html(html);
           
            } else {

                $('#siblings').html('<td style="font-size: 16px;"><strong> No Siblings </strong></td>');

            }

         $('#guardian_details').html('');
        if (gresultData != null) {
            var guardian_html = '';
            guardian_html +='<h4>Guardian Details</h4>';
             for (var x in g_enabled_field){
                 guardian_html +='<tr>';
                 for (var i = 0; i < g_enabled_field[x].length; i++) {
                    heading = g_enabled_field[x][i].replace('_', ' ');
                    guardian_html +='<th style="background:#f1f5f9">'+toTitleCase(heading);+'</th>';
                    var result_guardian_values = gresultData[g_enabled_field[x][i]];

                   if(g_enabled_field[x][i] == 'picture_url'){
                        if(result_guardian_values == ''){
                            guardian_html +='<td><img id="previewing_family" onclick="getimgsrcpath()" class="preview_big_image" name="family_photograph" style="width: 50px;height: 50px;" src="https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Parent App Icons 64px/male_placeholder_icon.png"/></td>';
                        }else{
                             guardian_html +='<td><img id="previewing_family" onclick="getimgsrcpath()" class="preview_big_image" name="family_photograph" style="width: 50px;height: 50px;" src="'+result_guardian_values+'"/></td>';
                        }
                    }else{
                        if(gresultData[g_enabled_field[x][i]] == null){
                            result_guardian_values = '';
                        }
                        guardian_html +='<td>'+result_guardian_values+'</td>';
                    }
                   
                 }
                 guardian_html +='</tr>';
             }

            $('#guardian_details').html(guardian_html);
        }


         $('#guardian2_details').html('');
        if (g2resultData != null) {
            var guardian2_html = '';
            guardian2_html +=' <h4>Guardian 2 Details</h4>';
             for (var z in g_enabled_field){
                 guardian2_html +='<tr>';
                 for (var i = 0; i < g_enabled_field[z].length; i++) {
                    heading = g_enabled_field[z][i].replace('_', ' ');
                    guardian2_html +='<th style="background:#f1f5f9">'+toTitleCase(heading);+'</th>';
                    var result_guardian2_values = g2resultData[g_enabled_field[z][i]];

                   if(g_enabled_field[z][i] == 'picture_url'){
                        if(result_guardian2_values == ''){
                            guardian2_html +='<td><img id="previewing_family" onclick="getimgsrcpath()" class="preview_big_image" name="family_photograph" style="width: 50px;height: 50px;" src="https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Parent App Icons 64px/male_placeholder_icon.png"/></td>';
                        }else{
                             guardian2_html +='<td><img id="previewing_family" onclick="getimgsrcpath()" class="preview_big_image" name="family_photograph" style="width: 50px;height: 50px;" src="'+result_guardian2_values+'"/></td>';
                        }
                    }else{
                        if(g2resultData[g_enabled_field[z][i]] == null){
                            result_guardian2_values = '';
                        }
                        guardian2_html +='<td>'+result_guardian2_values+'</td>';
                    }
                   
                 }
                 
                 guardian2_html +='</tr>';
             }

            $('#guardian2_details').html(guardian2_html);
        }
         $('#driver_details').html('');
        if (dresultData != null) {
            var driver_html = '';
            driver_html +='<h4>Driver Details</h4>';
             for (var f in d_enabled_field){
                 driver_html +='<tr>';
                 for (var i = 0; i < d_enabled_field[f].length; i++) {
                    heading = d_enabled_field[f][i].replace(/_/g, ' ');
                    driver_html +='<th style="background:#f1f5f9">'+toTitleCase(heading);+'</th>';
                    var result_driver_values = dresultData[d_enabled_field[f][i]];

                   if(d_enabled_field[f][i] == 'picture_url'){
                        if(result_driver_values == ''){
                            driver_html +='<td><img id="previewing_family" onclick="getimgsrcpath()" class="preview_big_image" name="family_photograph" style="width: 50px;height: 50px;" src="https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Parent App Icons 64px/male_placeholder_icon.png"/></td>';
                        }else{
                             driver_html +='<td><img id="previewing_family" onclick="getimgsrcpath()" class="preview_big_image" name="family_photograph" style="width: 50px;height: 50px;" src="'+result_driver_values+'"/></td>';
                        }
                    }else{
                        if(dresultData[d_enabled_field[f][i]] == null){
                            result_driver_values = '';
                        }
                        driver_html +='<td>'+result_driver_values+'</td>';
                    }
                   
                 }

                 driver_html +='</tr>';
             }

            $('#driver_details').html(driver_html);
        }
        $('#driver2_details').html('');
        if (d2resultData != null) {
            var driver2_html = '';
             driver2_html +='<h4>Driver 2 Details</h4>';
             for (var f in d_enabled_field){
                 driver2_html +='<tr>';
                 for (var i = 0; i < d_enabled_field[f].length; i++) {
                    heading = d_enabled_field[f][i].replace(/_/g, ' ');
                    driver2_html +='<th style="background:#f1f5f9">'+toTitleCase(heading);+'</th>';
                    var result_driver2_values = d2resultData[d_enabled_field[f][i]];

                   if(d_enabled_field[f][i] == 'picture_url'){
                        if(result_driver2_values == ''){
                            driver2_html +='<td><img id="previewing_family" onclick="getimgsrcpath()" class="preview_big_image" name="family_photograph" style="width: 50px;height: 50px;" src="https://s3.us-west-1.wasabisys.com/nextelement/nextelement-common/Parent App Icons 64px/male_placeholder_icon.png"/></td>';
                        }else{
                             driver2_html +='<td><img id="previewing_family" onclick="getimgsrcpath()" class="preview_big_image" name="family_photograph" style="width: 50px;height: 50px;" src="'+result_driver2_values+'"/></td>';
                        }
                    }else{
                        if(d2resultData[d_enabled_field[f][i]] == null){
                            result_driver2_values = '';
                        }
                        driver2_html +='<td>'+result_driver2_values+'</td>';
                    }
                   
                 }

                 driver2_html +='</tr>';
             }

            $('#driver2_details').html(driver2_html);
        }


            var min_html = '';
            min_html += '<br>' + resultData.class_name +''+resultData.section_name+ '<br>';
            min_html += resultData.admission_no;
            var single_window_fees_approval = '<?php echo $this->settings->getSetting('enable_indus_single_window_approval_process'); ?>';
            if(single_window_fees_approval == 1){
                var status = resultData.single_window_approval_status;
                var btn_color = 'warning';
                if(resultData.single_window_approval_status == ''){
                    status = 'Not Started';
                }
                if(status == 'Rejected'){
                    btn_color = 'danger';
                }else if(status == 'Approved'){
                    btn_color = 'success';
                }
            }
            $('#profile-data-name').html('<large>' + selectStudents + '</large>' + min_html);
            $('.profile-image').html('<img width="150px" src="<?php echo base_url() . 'assets/img/sample_boy_image.png' ?>" alt="Avatar">')
            if(resultData.picture_url !=''){
                $('.profile-image').html(' <img width="150px" src="'+resultData.picture_url+'" alt="Avatar">')
            }
            

        });
    }


    function select_school_details() {
        var student_id = $('#selectStudents').val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/get_school_details') ?>',
            type: "post",
            data: {
                'student_id': student_id
            },
            success: function(data) {
                var school_details = $.parseJSON(data);
            var resultData = school_details.student_data;
            var enabledField = school_details.enabled_field;  
            console.log(enabledField);

            var std_html = '';
             for (var s in enabledField){
                 std_html +='<tr>';
                 for (var i = 0; i < enabledField[s].length; i++) {
                    if(enabledField[s][i] != 'picture_url'){
                        heading = enabledField[s][i].replace(/_/g, ' ');
                        std_html +='<th style="background:#f1f5f9">'+toTitleCase(heading);+'</th>';
                        var result_school_values = resultData[enabledField[s][i]];
                        if(resultData[enabledField[s][i]] == null){
                            result_school_values = '';
                        }
                        std_html +='<td>'+result_school_values+'</td>';
                    }
                    
                 }
                 std_html +='</tr>';
             }

            $('#school_details_info').html(std_html);

            }
        });


    }

    function display_prev_schooling_details() {
        var student_id = $('#selectStudents').val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/get_prev_schooling_details') ?>',
            type: "post",
            data: {
                'student_id': student_id
            },
            success: function(data) {
                var prev_schooling_details = $.parseJSON(data);
                if (prev_schooling_details.length != 0) {
                    $('#prev_school_details').html(construct_prev_schooling_details_table(prev_schooling_details));
                } else {
                    $('#prev_school_details').html('<h4>Previous Schooling Details</h4><br><h6 class="no-data-display">No Data</h4>');
                }
            }

        })
    }

    function construct_prev_schooling_details_table (prev_schooling_details){
        var student_id = $('#selectStudents').val();
        var html ='';
        var previtUrl = '<?php echo site_url('student/student_controller/add_school_details/') ?>'+student_id;
        html += ` <div class="container" style="margin-bottom:10px;">
                        <a target="_blank" id="url_key" class="btn btn-primary pull-right" href="${previtUrl}"><i class="fa fa-trash"></i> Edit</a>
        <h4>Previous Schooling Details</h4>
                    </div>`;
        for (var i = 0; i < prev_schooling_details.length; i++) {
            html +=`<div class="panel panel-info">
                <div class="panel-heading">
                    <h3 class="panel-title">Year - ${prev_schooling_details[i].year_id}</h3>
                </div>`;

            html += `<div class="panel-body">`;
                html += 
                    `<div class="col-md-4">
                        <table class="table table-bordered">
                            <thead><tr><th>School Name</th></tr></thead>
                            <thead><tr><th>Class</th></tr></thead>
                            <thead><tr><th>Board</th></tr></thead>
                            <thead><tr><th>School Address</th></tr></thead>
                            <thead><tr><th>Medium of Instruction</th></tr></thead>
                        </table>
                    </div>

                    <div class="col-md-8" style="margin-left: -2.2rem;"> 
                        <table class="table table-bordered">
                            <tbody><tr><td>${prev_schooling_details[i].school_name || 'Not Provided'}</td></tr></tbody>
                            <tbody><tr><td>${prev_schooling_details[i].class || 'Not Provided'}</td></tr></tbody>
                            <tbody><tr><td>${prev_schooling_details[i].board || 'Not Provided'}</td></tr></tbody>
                            <tbody><tr><td>${prev_schooling_details[i].school_address || 'Not Provided'}</td></tr></tbody>
                            <tbody><tr><td>${prev_schooling_details[i].medium_of_instruction || 'Not Provided'}</td></tr></tbody>
                        </table>
                    </div>
                `;
                if(prev_schooling_details[i].report_card){
            
                    html += `<a target="_blank" class="btn btn-primary" href="${prev_schooling_details[i].report_card_view_url}">
                    View report card
                    </a>`;
                }
                else {
                    html += `<span class="no-data-display">No Document attached</span>`;
                }
                html += ` </p>
                </div>
                </div>     
            `;
        }
        return html;
    }

    function display_documents() {
        var student_id = $('#selectStudents').val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/get_student_documents') ?>',
            type: "post",
            data: {
                'student_id': student_id
            },
            success: function(data) {
                var stud_doc = $.parseJSON(data);
                // var stud_doc = stud_doc.document;
                // var fatherdata = stud_doc.fatherdata;
               
                // var motherdata = stud_doc.motherdata;
                // var guardiandata = stud_doc.guardiandata;
                if (stud_doc.document.length > 0) {
                    $('#student_school_documents').html(construct_document_table(stud_doc.document, stud_doc.fatherdata, stud_doc.motherdata));
                } else {
                    $('#student_school_documents').html('<h4>Documents</h4><br><h6 class="no-data-display">No Data</h4>');
                }
            }

        })
    }

    function construct_parent_aadhara_pan_table(fatherdata, motherdata, guardiandata) {
      
        // var student_id = $('#selectStudents').val();
        // html = `<h4>${fatherdata.relation_type} Aadhar and PAN card</h4><br>
        //      <table class="table table-bordered">
        //      <thead>
        //      <tr>
        //      <th>Aadhar</th>
        //      <th>Pan Card</th>
        //      <th>Actions</th>
        //      </tr>
        //      </thead>`;
        //     html += `<tr>
        //         <td>${fatherdata.aadhar_number}</td>
        //         <td>${data.aadhar_document_path}</td>
        //         <td>`
        //     if (fatherdata.aadhar_document_path == 'NA') {
        //         html += `Document Not Attached`
        //     } else {
        //         html += `<a target="_blank" href="${fatherdata.aadhar_document_path}" class="btn btn-secondary" data-placement="top" data-toggle="tooltip" data-original-title="View" target="_blank">View <i class="fa fa-eye"></i></a>`;
        //     }
        //     html += `</td>`;
        // html += '</table>'
        // return html;

    }

    function construct_document_table(documents,father_doc, mother_doc) {
        var fAadharNumber =father_doc.aadhar_number;
        var fAadharName =father_doc.name_as_per_aadhar;
        if(father_doc.aadhar_number ==''){
            fAadharNumber = '-'
            fAadharName ='-';
        }
        var fPanNumber =father_doc.pan_card_number;
        if(father_doc.pan_card_number ==''){
            fPanNumber = '-';
        }

        var mAadharNumber =mother_doc.aadhar_number;
        var mAadharName =mother_doc.name_as_per_aadhar;
        if(mother_doc.aadhar_number ==''){
            mAadharNumber = '-';
            mAadharName = '-';
        }
        var mPanNumber =mother_doc.pan_card_number;
        if(mother_doc.pan_card_number ==''){
            mPanNumber = '-';
        }

        var student_id = $('#selectStudents').val();
        var docsitUrl = '<?php echo site_url('student/student_controller/upload_documents/') ?>'+student_id;
        html = ` <div class="container" style="margin-bottom:10px;">
                        <a target="_blank" id="url_key" class="btn btn-primary pull-right" href="${docsitUrl}"><i class="fa fa-trash"></i> Edit</a>
                    </div><h4>Documents</h4><br>
             <table class="table table-bordered">
             <thead>
             <tr>
             <th>#</th>
             <th>Uploaded On</th>
             <th>Document Type</th>
             <th>Actions</th>
             <th>Uploaded By</th>
             </tr>
             </thead>`;
        documents.forEach((data, i) => {
            var downloadurl = '<?php echo site_url('student_analytics/student_analytics/student_documents_download/') ?>' + data.id + '/' + student_id;
            html += `<tr>
                <td>${++i}</td>
                <td>${data.created_on}</td>
                <td>${data.document_type}</td>
                <td>`
            if (data.document_url == 'NA' || data.document_url == '') {
                html += `Document Not Attached`
            } else {
                // html += `<a class="btn btn-info " data-placement="top" data-toggle="tooltip" data-original-title="Download" href="${downloadurl}">Download <i class="fa fa-cloud-download"></i></a>`
                html += `<button class="btn btn-info" id="downloadDcId${data.id}" data-url='${data.document_view_url}' data-url_name='${data.document_type}' onclick="download_student_documents(${data.id})"> Download <i class="fa fa-cloud-download"></i></button>`

                html += `<a target="_blank" href="${data.document_view_url}" class="btn btn-secondary" data-placement="top" data-toggle="tooltip" data-original-title="View" target="_blank">View <i class="fa fa-eye"></i></a>`;
            }
            html += `<td>${data.create_name}</td>`;

        });
        html += '</table>';
        // html += `<h4>${father_doc.relation_type}</h4><br>
        //      <table class="table table-bordered">
        //      <thead>
        //      <tr>
        //      <th>Type</th>
        //      <th>Name as per Aadhar</th>
        //      <th>Aadhar Number</th>
        //      <th>Remarks</th>
        //      <th>Action</th>
        //      </tr>
        //      </tr>
        //      </thead>`;
            
        //     html += `<tr>
        //         <td><b>Aadhar Details</b></td>
        //         <td>${fAadharName}</td>
        //         <td>${fAadharNumber}</td>
        //         <td>${father_doc.aadhar_document_remarks}</td>
        //         <td>`
        //         if (father_doc.aadhar_document_path == 'NA') {
        //             html += `Document Not Attached`
        //         } else {
        //             html += `<a target="_blank" href="${father_doc.aadhar_document_path}" class="btn btn-secondary" data-placement="top" data-toggle="tooltip" data-original-title="View" target="_blank">View <i class="fa fa-eye"></i></a>`;
        //         }
        //     html += `</td>
        //     </tr>
        //     <td><b>Pan Details</b></td>
        //     <td></td>
        //     <td>${fPanNumber}</td>
        //     <td>${father_doc.pan_card_document_remarks}</td>
        //             <td>`
        //         if (father_doc.pan_card_document_path == 'NA') {
        //             html += `Document Not Attached`
        //         } else {
        //             html += `<a target="_blank" href="${father_doc.pan_card_document_path}" class="btn btn-secondary" data-placement="top" data-toggle="tooltip" data-original-title="View" target="_blank">View <i class="fa fa-eye"></i></a>`;
        //         }
        //     html += `</td>`;
        //     html += `</tr>`;
        //     html += '</table>';

        //     html += `<h4>${mother_doc.relation_type}</h4><br>
        //      <table class="table table-bordered">
        //      <thead>
        //      <tr>
        //      <th>Type</th>
        //      <th>Name as per Aadhar</th>
        //      <th>Aadhar Number</th>
        //      <th>Remarks</th>
        //      <th>Action</th>
        //      </tr>
        //      </tr>
        //      </thead>`;
            
        //     html += `<tr>
        //         <td><b>Aadhar Details</b></td>
        //         <td>${mAadharName}</td>
        //         <td>${mAadharNumber}</td>
        //         <td>${mother_doc.aadhar_document_remarks}</td>
        //         <td>`
        //         if (mother_doc.aadhar_document_path == 'NA') {
        //             html += `Document Not Attached`
        //         } else {
        //             html += `<a target="_blank" href="${mother_doc.aadhar_document_path}" class="btn btn-secondary" data-placement="top" data-toggle="tooltip" data-original-title="View" target="_blank">View <i class="fa fa-eye"></i></a>`;
        //         }
        //     html += `</td>
        //     </tr>
        //     <td><b>Pan Details</b></td>
        //     <td></td>
        //     <td>${mPanNumber}</td>
        //     <td>${mother_doc.pan_card_document_remarks}</td>
        //             <td>`
        //         if (mother_doc.pan_card_document_path == 'NA') {
        //             html += `Document Not Attached`
        //         } else {
        //             html += `<a target="_blank" href="${mother_doc.pan_card_document_path}" class="btn btn-secondary" data-placement="top" data-toggle="tooltip" data-original-title="View" target="_blank">View <i class="fa fa-eye"></i></a>`;
        //         }
        //     html += `</td>`;
        //     html += `</tr>`;
        //     html += '</table>';
        // return html;
        return html;
    }

    function download_student_documents(docId) {
    var url = $('#downloadDcId' + docId).data('url');
    var filename = $('#downloadDcId' + docId).data('url_name');
    console.log(url);
    console.log(filename);
    
    var xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'blob';
    
    xhr.onload = function() {
        if (xhr.status === 200) {
            var contentType = xhr.getResponseHeader('Content-Type');
            var fileExtension = '';

            // Determine file extension from the Content-Type
            if (contentType) {
                var typeMap = {
                    'image/png': 'png',
                    'image/jpeg': 'jpg',
                    'application/pdf': 'pdf',
                    // Add other MIME types and their corresponding extensions as needed
                };
                fileExtension = typeMap[contentType] || '';
            }

            // If the file extension is not determined from Content-Type, extract from URL
            if (!fileExtension) {
                var urlParts = url.split('.');
                if (urlParts.length > 1) {
                    fileExtension = urlParts.pop().split('?')[0]; // Remove query parameters if any
                }
            }

            // Append the file extension to the filename if not already present
            if (filename.indexOf('.') === -1 && fileExtension) {
                filename += '.' + fileExtension;
            }

            var blob = new Blob([xhr.response], { type: contentType || 'application/octet-stream' });
            var a = document.createElement('a');
            a.href = window.URL.createObjectURL(blob);
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(a.href);  // Clean up the URL object
        } else {
            console.error(`Failed to download: ${filename}`);
        }
    };
    
    xhr.onerror = function() {
        console.error('Network error');
    };
    
    xhr.send();
}

    function get_sms() {
        var student_id = $('#selectStudents').val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/get_student_sms') ?>',
            type: "post",
            data: {
                'student_id': student_id
            },
            success: function(data) {
                var student_sms = $.parseJSON(data);
                console.log(student_sms);
                if (student_sms.length > 0) {
                    $('#sms').html(construct_sms_table(student_sms));
                } else {
                    $('#sms').html('<h4>SMS</h4><h6 class="no-data-display">No Data</h4>');

                }
            }
        })
    }

    function construct_sms_table(smstable) {
        var html = `<h4>SMS</h4>
             <table class="table table-bordered" width="100%">
             <thead>
             <tr>
             <th  width="2%">#</th>
             <th  width="8%">Date</th>
             <th  width="82%">Message</th>
             <th  width="3%">Type</th>
             </tr>
             </thead>`;
        smstable.forEach((data, i) => {
            html += `<tr>
                <td>${++i}</td>
                <td>${data.date}</td>
                <td>${data.message}</td>
                <td>`;
            if (data.mode == 1) {
                html += 'SMS';

            } else {
                html += 'Notification';
            }
            html += `</td>
                </tr>`;
        });
        return html;

    }

    function get_student_circular() {
        var student_id = $('#selectStudents').val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/get_circular') ?>',
            type: "post",
            data: {
                'student_id': student_id
            },
            success: function(data) {
                var circular = $.parseJSON(data);
                console.log(circular);
                if (circular.length > 0) {
                    $('#circular').html(construct_circular_table(circular));
                } else {
                    $('#circular').html('<h4>Circular</h4><br><h6 class="no-data-display">No Data</h4>');
                }


            }


        })

    }

    function construct_circular_table(circular) {
        var html = `<h4>Circular</h4>
             <table class="table table-bordered" width="100%">
             <thead>
             <tr>
             <th  width="2%">#</th>
             <th  width="8%">Date</th>
             <th  width="82%">Title</th>
             <th  width="3%">Category</th>
             </tr>
             </thead>`;
        circular.forEach((data, i) => {
            html += `<tr>
                <td>${++i}</td>
                <td>${data.sent_on}</td>
                <td>${data.title}</td>
                <td>${data.category}</td>
                <td></tr>`;
            html += '</thead';

        });
        return html;

    }

    function get_student_observation() {
        var student_id = $('#selectStudents').val();
        var class_id = $('#classSectionId').val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/get_student_observation'); ?>',
            type: 'post',
            data: {
                'student_id': student_id,
                'class_id': class_id
            },
            success: function(data) {
                var parsed_data = $.parseJSON(data);
                console.log(parsed_data);
                if (parsed_data.length > 0) {
                    $("#observation").html(construct_observation_table(parsed_data));
                } else {
                    $("#observation").html('<h4>Observation</h4><br><h6 class="no-data-display">No Data</h4>');
                }

            },
            error: function(err) {
                console.log(err);
            }
        });
    }



    function construct_observation_table(observation_data) {
        var html = '';
        if (observation_data.length == 0) {
            html += '<h4>No Observations.</h4>';
        } else {
            html += `<h4>Observations</h4><br>
            <table id="observation_table" class="table table-bordered">
            <thead>
            <tr>
            <th>#</th>
            <th>Student Name</th>
            <th>Class</th>
            <th>Observation Date</th>
            <th>Category</th>
            <th>Observation</th>
            <th>Observed By</th>
            <th>Created On</th>
            </tr>
            </thead>`;
            html += `<tbody>`;
            for (var i = 0; i < observation_data.length; i++) {
                var data = observation_data[i];
                html += `
                <tr>
                <td>${i+1}</td>
                <td>${data['std_name']}</td>
                <td>${data['class_name']}</td>
                <td>${data['observation_date']}</td>
                <td style="background-color:${data['color_code']}">${data['category']}</td>
                <td>${data['observation']}</td>
                <td>${data['staff_name']} (${data['capacity']})</td>
                <td>${data['created_on']}</td>
                </tr>`;
            }
            html += `</tbody>
            </table>`;
        }
        return html;
    }

    function get_non_compliance() {
        var student_id = $('#selectStudents').val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/get_student_non_compliance'); ?>',
            type: 'post',
            data: {
                'student_id': student_id
            },
            success: function(data) {
                var datas = $.parseJSON(data);
                console.log(datas);
                if (datas.length > 0) {
                    $('#non_compliance').html(construct_balance_nc_report(datas));
                } else {
                    $('#non_compliance').html('<h4>Non Compliance</h4><br><h6 class="no-data-display">No Data</h4>');

                }
            }
        })
    }

    function construct_balance_nc_report(student_data) {
        let total_ncs = 0;
        let served_ncs = 0;
        let balance_ncs = 0;
        let roll_no = 0;
        var html = '';
        html = `
          <table class="table table-bordered ">
            <tr class="table-primary">
                <th>Sl</th>
                <th>Date</th>
                <th>NC Category</th>
                <th>Assigned By</th>
                <th>Status</th>
                <th>Remarks</th>
            </tr>
        `;

        let name = "";
        student_data.forEach((data, i) => {
            let dateFormat = data['Date'].split("-").reverse().join("-")
            total_ncs++;
            if (data['Status'] == "penalty") {
                balance_ncs++;
            }

            if (data['Status'] != "penalty") {
                served_ncs++;
            }

            name =
                html += `<tr>
            <td>${i+1}</td>
            <td>${dateFormat}</td>
            <td>${data['nc_category']}</td>
            <td>${data['Assigned_by']}</td>
            <td style="background:${data['Status']=="penalty"&&"#ff9999;" || "#afffaf;"}">${data['Status'].at(0).toUpperCase()}${data['Status'].slice(1)}</td>
            <td style=color:${data['Remarks']|| "grey"}>${data['Remarks']|| "NA"}</td>
            </tr>`
        })
        return html;
    }

    function get_fees() {
        var student_id = $('#selectStudents').val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/fee_detailed_summary'); ?>',
            type: 'post',
            data: {
                'student_id': student_id
            },
            success: function(data) {
                var rData = JSON.parse(data);
                console.log(rData);
                if (rData.length != 0) {
                    $('#fee_summary_details').html(construct_summary_table(rData));
                } else {
                    $('#fee_summary_details').html('<h4>Fees</h4><br><h6 class="no-data-display">No Data</h4>');
                }
            }
        });
    }

    function construct_summary_table(rData) {
        var html = '<h4>Fees</h4>';
        html += '<a onclick="download_fees_recipt('+rData.stdid+')" class="btn btn-primary" data-target="#recipts_by_id" data-toggle="modal"><i class="fa fa-eye"> </i> View Receipts </a>';
        for (var i = 0; i < rData.length; i++) {
            html += '<div class="col-md-4 box" style="border: 1px solid #ccc; border-radius: 30px;margin-bottom: 0.6rem;margin-right:1rem">';
            html += '<button class="btn btn btn-primary" style="margin-left: -15px;border-top-left-radius: 60px;border-bottom-right-radius: 60px;padding: 4px 10px;">' + rData[i].blueprint_name + '</button>';
            var btnColor = 'btn-danger';
            var payemntStatus = 'Not Started';
            if (rData[i].payment_status == 'FULL') {
                btnColor = 'btn-success';
                payemntStatus = 'Fully Paid';
            } else {
                btnColor = 'btn-warning';
                payemntStatus = 'Partially Paid';
            }
            html += '<button class="btn ' + btnColor + '" style="border-top-left-radius: 0px;border-bottom-right-radius: 0px;padding: 4px 10px;float: right;margin-right: -8px;"> ' + payemntStatus + ' </button>';
            html += '<table class="table borderless">';
            html += '<tr>';
            html += '<th>Total Fee</th>';
            html += '<td>' + numberToCurrency(rData[i].total_fee) + '</td>';
            html += '</tr>';
            html += '<tr>';
            html += '<th>Paid Amount</th>';
            html += '<td>' + numberToCurrency(rData[i].total_fee_paid) + '</td>';
            html += '</tr>';
            if (rData[i].total_concession != 0) {
                html += '<tr>';
                html += '<th>Concession</th>';
                html += '<td>' + numberToCurrency(rData[i].total_concession) + '</td>';
                html += '</tr>';
            }
            if (rData[i].total_adjustment != 0) {
                html += '<tr>';
                html += '<th>Adjustment</th>';
                html += '<td>' + numberToCurrency(rData[i].total_adjustment) + '</td>';
                html += '</tr>';
            }

            if (rData[i].discount != 0) {
                html += '<tr>';
                html += '<th>Discount</th>';
                html += '<td>' + numberToCurrency(rData[i].discount) + '</td>';
                html += '</tr>';
            }

            if (rData[i].refund_amount != 0) {
                html += '<tr>';
                html += '<th>Refund</th>';
                html += '<td>' + numberToCurrency(rData[i].refund_amount) + '</td>';
                html += '</tr>';
            }
            html += '<tr>';
            html += '<th>Balance</th>';
            html += '<td>' + numberToCurrency(rData[i].balance) + '</td>';
            html += '</tr>';

            if (rData[i].total_fine != 0) {
                html += '<tr>';
                html += '<th>Total Fine</th>';
                html += '<td>' + numberToCurrency(rData[i].total_fine) + '</td>';
                html += '</tr>';
            }
            var overallbalance = 0;
            overallbalance = parseFloat(rData[i].balance) + parseFloat(rData[i].total_fine);
            if (rData[i].total_fine != 0) {
                html += '<tr>';
                html += '<th>Over All Balance</th>';
                html += '<td>' + numberToCurrency(overallbalance) + '</td>';
                html += '</tr>';
            }

            html += '</table>';
            html += '</div>';
        }
        return html;
    }

    

    function numberToCurrency(amount) {
        var formatter = new Intl.NumberFormat('en-IN', {
            // style: 'currency',
            currency: 'INR',
        });
        return formatter.format(amount);
    }


    function get_student_medical_forms() {
       var student_id = $('#selectStudents').val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/get_student_medical_forms_details'); ?>',
            type: 'post',
            data: {
                'student_id': student_id
            },
            success: function(data) {
                var rData = JSON.parse(data);
                console.log(rData);
                if (rData != 0) {
                    $('#student_medical_forms').html(construct_student_medical_forms(rData));
                } else {
                    $('#student_medical_forms').html('<h4>Hospitalization Details</h4><br><h6 class="no-data-display">No Data</h4>');
                }
            }
        });
    }

    function construct_student_medical_forms(hospital_data) {

        html = `<h4>Hospitalization Details
        
        </h4>
                <table class="table table-bordered">
                <thead>
                <tr>
                <th>#</th>
                <th>Hospitalization Date</th>
                <th>Reason</th>
                <th>Treatment</th>
                <th>Action</th>
                </thead>`;
        hospital_data.forEach((data, i) => {
            //console.log();
            html += `<tr>
            <td>${++i}</td>
            <td>${data.hospitilization_date}</td>
            <td>${data.hospitilization_reason}</td>
            <td>${data.treatment_provided}</td>
            <td><a data-toggle="modal" onclick="medicalid_full_details(${data.id})"  class="btn btn-info">View Details</a></td>
            </tr>`;
        });
        html += '</table>';
        return html;
    }

    function medicalid_full_details(id) {
       var student_id = $('#selectStudents').val();
        $.ajax({
          url: '<?php echo site_url('student_analytics/student_analytics/student_medical_full_details'); ?>',
          type: 'post',
          data: {
            'id': id, 'student_id' : student_id
          },
        success: function(data) {
            var resData = $.parseJSON(data);
            $('#medical_full_details').modal('show');

            $('#hospital_admitted_to').val(resData.hospital_admitted);
            $('#hospitilization_date').val(resData.hospitilization_date);
            $('#discharge_date').val(resData.discharge_date);
            $('#hospital_reason').val(resData.hospitilization_reason);
            $('#treatment_provided').val(resData.treatment_provided);
            $('#Remarks').val(resData.remarks);
        }
    });
}

 function get_login_report(){
     $('#login_report').fullCalendar('refetchEvents');

        var calendar = $('#login_report');
        calendar.fullCalendar('destroy');
        var student_biometric_code = $('#selectStudents').val();
          let url = `<?php echo site_url('student_analytics/student_analytics/get_student_attendance_current_month_calendar/') ?>`+student_biometric_code;
            
                var calendar = $('#login_report').fullCalendar({
                    header: {
                        left: 'prev,next today',
                        center: 'title',
                        right: 'month,agendaWeek,agendaDay'
                    },
                    editable: false,
                    eventSources: { url: `${url}` },
                    droppable: false,
                    selectable: false,
                    selectHelper: false,
                    select: function(start, end, allDay) {
                        $('#login_report').fullCalendar( 'refetchEvents' );
                        var title = prompt('Event Title:');
                        if (title) {
                            calendar.fullCalendar('renderEvent', {
                                    title: title,
                                    start: start,
                                    end: end,
                                    allDay: allDay,
                                    color: red
                                },
                                true
                            );
                        }
                        calendar.fullCalendar('unselect');
                        calendar.fullCalendar('refetchEvents');
                    },
                    drop: function(date, allDay) {

                        var originalEventObject = $(this).mergedArray('eventObject');
                       
                        var copiedEventObject = $.extend({}, originalEventObject);
                        copiedEventObject.start = date;
                        copiedEventObject.allDay = allDay;

                        $('#login_report').fullCalendar('renderEvent', copiedEventObject, true);


                        if ($('#drop-remove').is(':checked')) {
                            $(this).remove();
                        }

                    }
                });
    }
    
   
    function get_student_consent_forms() {
        var student_id = $('#selectStudents').val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/get_student_consent_forms_details'); ?>',
            type: 'post',
            data: {
                'student_id': student_id
            },
            success: function(data) {
                var rData = JSON.parse(data);
                console.log(rData);
                if (rData != 0) {
                    $('#student_consent_forms').html(construct_student_consent_forms(rData));
                } else {
                    $('#student_consent_forms').html('<h4>Student Consent Form</h4><br><h6 class="no-data-display">No Data</h4>');
                }
            }
        });
    }

    function construct_student_consent_forms(consent_form) {
        console.log(consent_form);
        var student_id = $('#selectStudents').val();
        html = `<h4>Student Consent Form</h4><br>
             <table class="table table-bordered">
             <thead>
             <tr>
                <th>#</th>
                <th>Form</th>
                <th>Submitted On</th>
                <th>Submitted By</th>
                <th>Status</th>
                <th>Agreed?</th>
                <th>Remarks</th>
                <th>Action</th>
             </tr>
             </thead>`;
        consent_form.forEach((data, i) => {
            var buttondisabled = '';
            if(data.status == 'Submitted'){
                buttondisabled ='disabled';
            }
            html += `<tr>
                <td>${++i}</td>
                <td>${data.template_name}</td>
                <td>${data.submitted_on || '-'}</td>
                <td>${data.submitted_by || '-'}</td>
                <td>${data.status}</td>
                <td>${data.consent_provided || '-'}</td>
                <td>${data.consent_remarks || '-'}</td>
                <td>
                <a target="_blank" href="${data.template_path}" class="btn btn-secondary" data-placement="top" data-toggle="tooltip" data-original-title="View" target="_blank">View Form <i class="fa fa-eye"></i></a>
                </td>
            </tr>
            `;

        });
        html += '</table>'
        return html;
    }

    function onclick_consent_form_view(consent_template_id) {
        $('#consent_template_id').val(consent_template_id);
    }

    function get_health_report() {
       var student_id = $('#selectStudents').val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/get_student_health_details'); ?>',
            type: 'post',
            data: {
                'student_id': student_id
            },
            success: function(data) {
                var resultData = JSON.parse(data);
                
                var result = resultData.result;
                var disabledField = resultData.enabled;
                if(result != null && result.length != 0){
                    var std_html = '';
                    if($.inArray('blood_group', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9;width:50%" >Child blood group </th>';
                        
                        std_html +='<td>'+result.blood_group+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('father_bld_group', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9;width:50%">Fathers blood group</th>';
                        
                        std_html +='<td>'+result.father_bld_group+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('mother_bld_group', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9;width:50%">Mothers blood group</th>';
                        
                        std_html +='<td>'+result.mother_bld_group+'</td>';
                        std_html +='</tr>';
                    }
                     if($.inArray('physical_disability', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9;width:50%">Does the child have any physical disability?</th>';
                        
                        std_html +='<td>'+result.physical_disability+'</td>';
                        std_html +='</tr>';
                    }
                    
                    if($.inArray('physical_disability_reason', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9;width:50%">Reason for physical disability </th>';
                        
                        std_html +='<td>'+result.physical_disability_reason+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('learning_disability', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9;width:50%">Does the child have any learning disability? </th>';
                        
                        std_html +='<td>'+result.learning_disability+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('learning_disability_reason', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9;width:50%">Reason for learning disability </th>';
                        
                        std_html +='<td>'+result.learning_disability_reason+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('allergy', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9;width:50%">Allergic To </th>';
                        
                        std_html +='<td>'+result.allergy+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('family_history', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9;width:50%">Past/Family History </th>';
                        
                        std_html +='<td>'+result.family_history+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('anaemia', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Anaemia </th>';
                        
                        std_html +='<td>'+result.anaemia+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('fit_to_participate', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Fit to participate in age specific physical activity? </th>';
                        
                        std_html +='<td>'+result.fit_to_participate+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('height', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Height </th>';
                        
                        std_html +='<td>'+result.height+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('weight', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Weight </th>';
                        
                        std_html +='<td>'+result.weight+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('hair', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Hair </th>';
                        
                        std_html +='<td>'+result.hair+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('skin', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Skin </th>';
                        
                        std_html +='<td>'+result.skin+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('ear', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Ear </th>';
                        
                        std_html +='<td>'+result.ear+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('nose', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Nose </th>';
                        
                        std_html +='<td>'+result.nose+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('throat', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Throat </th>';
                        
                        std_html +='<td>'+result.throat+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('neck', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Neck </th>';
                        
                        std_html +='<td>'+result.neck+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('respiratory', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Respiration </th>';
                        
                        std_html +='<td>'+result.respiratory+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('cardio_vascular', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Cardio-Vascular </th>';
                        
                        std_html +='<td>'+result.cardio_vascular+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('abdomen', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Abdomen </th>';
                        
                        std_html +='<td>'+result.abdomen+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('nervous_system', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Nervous System </th>';
                        
                        std_html +='<td>'+result.nervous_system+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('left_eye', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Left Eye </th>';
                        
                        std_html +='<td>'+result.left_eye+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('right_eye', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Right Eye </th>';
                        
                        std_html +='<td>'+result.right_eye+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('extra_oral', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Dental Examination - Extra Oral </th>';
                        
                        std_html +='<td>'+result.extra_oral+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('intra_oral', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Dental Examination - Intra Oral</th>';
                        
                        std_html +='<td>'+result.intra_oral+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('bad_breath', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Bad Breath </th>';
                        
                        std_html +='<td>'+result.bad_breath+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('tooth_cavity', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Tooth Cavity </th>';
                        
                        std_html +='<td>'+result.tooth_cavity+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('plaque', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Plaque </th>';
                        
                        std_html +='<td>'+result.plaque+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('gum_inflamation', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Gum Inflammation </th>';
                        
                        std_html +='<td>'+result.gum_inflamation+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('stains', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Stains </th>';
                        
                        std_html +='<td>'+result.stains+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('gum_bleeding', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Gum Bleeding </th>';
                        
                        std_html +='<td>'+result.gum_bleeding+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('soft_tissue', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Soft Tissue </th>';
                        
                        std_html +='<td>'+result.soft_tissue+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('covid', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Covid </th>';
                        
                        std_html +='<td>'+result.covid+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('vitaminb12', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Vitamin B12 </th>';
                        
                        std_html +='<td>'+result.vitaminb12+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('vitamind', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Vitamin D </th>';
                        
                        std_html +='<td>'+result.vitamind+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('iron', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Iron </th>';
                        
                        std_html +='<td>'+result.iron+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('calcium', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Calcium </th>';
                        
                        std_html +='<td>'+result.calcium+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('vaccines', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Vaccines </th>';
                        
                        std_html +='<td>'+result.vaccines+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('head_injury', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Child has head injury? </th>';
                        
                        std_html +='<td>'+result.head_injury+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('ear_impartement', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Child has ear impairment? </th>';
                        
                        std_html +='<td>'+result.ear_impartement+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('difficulty_in_breathing', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Child has difficulty in breathing? </th>';
                        
                        std_html +='<td>'+result.difficulty_in_breathing+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('child_has_join_pain', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Child has recurrent pain in joints? </th>';
                        
                        std_html +='<td>'+result.child_has_join_pain+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('child_past_hsitory_fracture', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Child has past history of fractures? </th>';
                        
                        std_html +='<td>'+result.child_past_hsitory_fracture+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('fracture', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Select type of fracture </th>';
                        
                        std_html +='<td>'+result.fracture+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('foodytpe', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Child food habits  </th>';
                        
                        std_html +='<td>'+result.foodytpe+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('child_nervous_breakdown', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Child suffers from nervous breakdown/depression?</th>';
                        
                        std_html +='<td>'+result.child_nervous_breakdown+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('child_color_blindness', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Child suffers from color blindness?</th>';
                        
                        std_html +='<td>'+result.child_color_blindness+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('child_diabities', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Child suffers from juvenile diabetes?</th>';
                        
                        std_html +='<td>'+result.child_diabities+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('congenital_heart_disease', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Child suffers from congenital heart disease?</th>';
                        
                        std_html +='<td>'+result.congenital_heart_disease+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('more_than_month_disease', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Child has taken medicine for more than one month? If yes, specify.</th>';
                        
                        std_html +='<td>'+result.more_than_month_disease+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('medicine_name_for_month', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Mention medicine names</th>';
                        
                        std_html +='<td>'+result.medicine_name_for_month+'</td>';
                        std_html +='</tr>';
                    }
                    if($.inArray('any_other_medical_treatment', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Are there any other medical treatment/ailment that is not mentioned?</th>';
                        
                        std_html +='<td>'+result.any_other_medical_treatment+'</td>';
                        std_html +='</tr>';
                    }
                    
                    
                    if($.inArray('child_past_hsitory_sea', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Child has past history of sea/air/motion sickness?</th>';
                        
                        std_html +='<td>'+result.child_past_hsitory_sea+'</td>';
                        std_html +='</tr>';
                    }

                    if($.inArray('renal_issues', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Renal Issues</th>';
                        
                        std_html +='<td>'+result.renal_issues+'</td>';
                        std_html +='</tr>';
                    }

                    if($.inArray('skin_issues', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Skin Issues</th>';
                        
                        std_html +='<td>'+result.skin_issues+'</td>';
                        std_html +='</tr>';
                    }

                    if($.inArray('daily_medication', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Is child on daily medication?</th>';
                        
                        std_html +='<td>'+result.daily_medication+'</td>';
                        std_html +='</tr>';
                    }

                    if($.inArray('family_doctor_name', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Family Doctor Name</th>';
                        
                        std_html +='<td>'+result.family_doctor_name+'</td>';
                        std_html +='</tr>';
                    }

                    if($.inArray('family_doctor_contact_number', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Family Doctor Contact Number</th>';
                        
                        std_html +='<td>'+result.family_doctor_name+'</td>';
                        std_html +='</tr>';
                    }

                    if($.inArray('family_doctor_city', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Family Doctor City Name</th>';
                        
                        std_html +='<td>'+result.family_doctor_city+'</td>';
                        std_html +='</tr>';
                    }

                    if($.inArray('number_of_sons', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Total number of Sons</th>';
                        
                        std_html +='<td>'+result.number_of_sons+'</td>';
                        std_html +='</tr>';
                    }

                    if($.inArray('number_of_daughters', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Total number of Daughters</th>';
                        
                        std_html +='<td>'+result.number_of_daughters+'</td>';
                        std_html +='</tr>';
                    }

                    if($.inArray('does_child_suffering_from_headaches', disabledField) == -1){
                        std_html +='<tr>';
                        std_html +='<th style="background:#f1f5f9">Does Child Suffering from headache</th>';
                        
                        std_html +='<td>'+result.does_child_suffering_from_headaches+'</td>';
                        std_html +='</tr>';
                    }

                    // std_html +='<th style="background:#f1f5f9"></th>';

                    // for(var h in result){
                    //        if(jQuery.inArray(h, disabledField) === -1){
                    //             heading = h.replace(/_/g, ' ');
                    //             std_html +='<tr>';
                    //             std_html +='<th style="background:#f1f5f9">'+toTitleCase(heading);+'</th>';

                    //         var result_school_values = result[h];
                    //         if(result[h] == null){
                    //         result_school_values = '';
                    //     }

                          
                    //     }
                    // }
                }else{
                    var std_html = '<br><h6 class="no-data-display">No Data</h4>';
                }
            
                $('#student_health_table').html(std_html);
            }

        });

         var student_id = $('#selectStudents').val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/student_vaccination_history_details'); ?>',
            type: 'post',
            data: {
                'student_id': student_id
            },
            success: function(data) {
                var rData = JSON.parse(data);
                console.log(rData);
                if (rData != 0) {
                    $('#student_vacination_table').html(construct_student_vacination_table(rData));
                } else {
                    $('#student_vacination_table').html('<br><h6 class="no-data-display">No Data</h4>');
                }
            }
        });
    }

    function construct_student_vacination_table(rData) {
        // console.log(consent_form);
        var student_id = $('#selectStudents').val();
        html = `<br><h4>Student Vaccination Details</h4><br>`
            html = ` <table class="table table-bordered" style="width:80%">
             <thead>
             <tr>
                <th>#</th>
                <th>Vaccination Name</th>
                <th>Vaccination Status</th>
                <th>Month/ Year</th>
                <th>Comments</th>
             </tr>
             </thead>`;
        rData.forEach((data, i) => {
            html += `<tr>
                <td>${++i}</td>
                <td>${data.vaccination_name}</td>
                <td>${data.Vstatus}</td>
                <td>${data.year_of_vaccination_date || '-'}</td>
                <td>${data.description || '-'}</td>
            </tr>
            `;

        });
        html += '</table>'
        return html;
    }

    function single_window_details(){
        var student_id = $('#selectStudents').val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/get_approval_process_details') ?>',
            type: 'post',
            data: {'student_id':student_id},
            success: function(data) {
              var res_data = $.parseJSON(data);
              construct_single_window_details(res_data);
            }
          });

        

    }

    function construct_single_window_details(std_single_window_data){
        
        var html = '<h4 style="display:inline-block">Single Window Approval Process</h4>&nbsp;&nbsp;<button onclick="single_window_details()" class="btn btn-warning">Refresh</button>'; 
        var approval_teams = '<?php echo json_encode($this->settings->getSetting('indus_single_window_approval_teams')) ?>';
        var approval_teams_arr = $.parseJSON(approval_teams);
        html += '<div class="col-md-12" style="margin-top:15px">';
        for (var i in approval_teams_arr) {
            var btn_color = '#c1cad2';
            var approved_by = '';
            var approved_on = '';
            var remarks = '';
            var status = 'Not Started';
            if (std_single_window_data != '') {
                for (var k = 0; k < std_single_window_data.length; k++) {
                    // Trim both values before comparison
                    var teamNameTrimmed = std_single_window_data[k].team_name
                        ? std_single_window_data[k].team_name.trim().toLowerCase()
                        : '';
                    var approvalTeamTrimmed = approval_teams_arr[i]
                        ? approval_teams_arr[i].trim().toLowerCase()
                        : '';

                    if (approvalTeamTrimmed == teamNameTrimmed) {
                        approved_by = (std_single_window_data[k].taken_by == ' ' ? '-' : std_single_window_data[k].taken_by);
                        approved_on = std_single_window_data[k].taken_on;
                        remarks = std_single_window_data[k].remarks;
                        status = std_single_window_data[k].status;
                    }
                    if (approvalTeamTrimmed == teamNameTrimmed && std_single_window_data[k].status == 'Approved') {
                        btn_color = '#7eed97';
                    } else if (approvalTeamTrimmed == teamNameTrimmed && std_single_window_data[k].status == 'Rejected') {
                        btn_color = '#ff8685';
                    }
                }
            }
            html += '<div class="col-md-6" style="margin-top:15px">';
            html += `<a data-toggle="modal" data-target="#approve_single_window" onclick="show_single_window_approve_popup('${approval_teams_arr[i]}')" style="margin-right:10px;border-radius:5px;width:100%;height:14rem;font-size:2rem;display:flex;flex-direction:column;align-items:center;justify-content:center;background-color:${btn_color};overflow-wrap:break-word;word-wrap:break-word;word-break:break-word;" class="btn btn-hover-effect">`;
            html += `${approval_teams_arr[i]}`;
            if (status == 'Approved' || status == 'Rejected') {
                html += `<ul style="list-style: none; padding: 0; width: 90%; margin: auto;">
                    <li style="font-size: small; margin-bottom: 5px;"><b>Status : </b>${status}</li>
                    <li style="font-size: small; margin-bottom: 5px;"><b>${status} By : </b>${approved_by}</li>
                    <li style="font-size: small; margin-bottom: 5px;"><b>${status} On : </b>${approved_on}</li>
                    <li style="font-size: small;"><b>Remarks :</b>
                    <div style="text-wrap:balance;display:inline">${remarks}</div></li>
                </ul>`;
            } else {
                html += '<br><p style="font-size:small">Status : <b>Not Started</b></p>';
            }

            html += `</a>`;
            html += '</div>';
        }
        html += '</div>';
        $('#single_window').html(html);
    }

    function show_single_window_approve_popup(team_name){
        $('#team_name').val(team_name);
        $('#modal_header').html('Approve/Reject '+team_name+' Single Window Process');
        $('#single_window_approval_content').html(`<input class="form-control" type="remarks" id="single_wind_remarks" placeholder="Enter the Remarks" name="single_wind_remarks">`);
    }

    function submit_approval_process(status){
        var student_id = $('#selectStudents').val();
        var team_name = $('#team_name').val();
        var single_wind_remarks = $('#single_wind_remarks').val();
            if(single_wind_remarks == ''){
                if(status == 'Rejected'){
                $('#single_wind_remarks').attr('required','required');
                }else{
                $('#single_wind_remarks').removeAttr('required');
                }
            }
            var form = $('#single_window_form');
            if (!form.parsley().validate()) {
                return 0;
            }
            $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/submit_approval_process') ?>',
            type: 'post',
            data: {'student_id':student_id,'status':status,'single_wind_remarks':single_wind_remarks,'team_name':team_name},
            success: function(data) {
              var res_data = $.parseJSON(data);
              if(res_data){
                $('#approve_single_window').modal('hide');
                single_window_details();
                // window.location.reload();
              }
            }
          });
    }

    function get_transposrtation_details() {
        var student_id = $('#selectStudents').val();
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/get_transposrtation_details'); ?>',
            type: 'post',
            data: {
                'student_id': student_id
            },
            success: function(data) {
                var rData = JSON.parse(data);
                console.log(rData);
                if (rData.length != 0) {
                    $('#transportation').html(construct_transportation_details(rData));
                } else {
                    $('#transportation').html('<h4>Transportation</h4><br><h6 class="no-data-display">No Data</h4>');
                }
            }
        });
    }

    function construct_transportation_details(transport) {
        var html = `<h4>Transportation</h4><table class="table table-bordered ">
        <thead>
        <tr class="table-primary">
        <th>#</th>
        <th>Journey Day</th>
        <th>Route</th>
        <th>Start Time</th>
        <th>End Time</th>
        <th>Transport Name</th>
        <th>Transport Registration Number</th>
        <th>Driver Name</th>  
        <th>Driver Contact Number</th>
        </tr><tr>`;
        html += '<tbody>'
        for (let i = 0; i < transport.length; i++) {
            html += '<td>' + i + '</td>';;
            html += '<td>' + transport[i].day + '</td>';
            html += '<td>' + transport[i].journey_name + '</td>';
            html += '<td>' + transport[i].startTime + '</td>';
            html += '<td>' + transport[i].endTime + '</td>';
            html += '<td>' + transport[i].thing_name + '</td>';
            html += '<td>' + transport[i].thing_reg_number + '</td>';
            html += '<td>' + transport[i].driver_name + '</td>';
            html += '<td>' + transport[i].phone_number + '</td>';
            html += '</tr>';
        }
        html += '</tbody></table>';
        return html;
    }

    function getPerformanceData(acad_year) {
        $("#performance").html('');
        var student_id = $("#selectStudents").val();
        var assIds = $('#assessments').val();
        if(assIds == null || assIds[0] == 0 || student_id == 0) {
            html = '<h4 class="no-data-display">No data available.</h4>';
            $("#total_subjects_per_assessment_Linegraph").html(html);
            return ;
        }
        $.ajax({
            url: '<?php echo site_url('examination/assessment_reports/studentWiseMarks') ?>',
            type: 'post',
            data: {
                'acad_year': acad_year,
                'student_id': student_id,
                'assIds':assIds
            },
            success: function(data) {
                var data = JSON.parse(data);
                console.log('Device Checking in....');
        console.log(data);
                var subWise = data.subWiseStatus;
                var assessments = data.assessments;
                var html = '';
        // console.log('Device Checking in....');
        // console.log(data.marks);
        // console.log(data.assessments);
        // console.log(data.student);

                fillTable(data.marks, data.assessments, data.student);
                if (assessments.length == 0) {
                    html = '<h4 class="no-data-display">No data available.</h4>';
                } else {
                    html = `<div style="opacity: 0; height: 20px;">l</div>
                            <div class="modal-header">
                                <h3 style="max-width: fit-content;">
                                    Marks Analysis for sudent - <b>${data.student.stdName}</b> of class/section <b>${ data.student.class_name} / ${ data.student.section_name}  (${ data.student.admission_no})</b>
                                </h3> 
                                <select id="graph_type" onchange="onchange_graph_type('graph_type', 1)" class="pull-right form-control" style="max-width: fit-content; align-items: flex-end;">
                                    <option value="line">Line Graph</option><option  value="bar">Bar Graph</option>
                                </select>
                            </div>
                            <div style="opacity: 0; height: 6px;">l</div>`;
                    html += '<div class="col-md-2" style="padding: 2px;">';
                    var index = 1;
                    for (var status in subWise) {
                        var subData = subWise[status];
                        html += '<div style="max-height: 200px;overflow-y: scroll;">';
                        html += '<table class="table table-bordered"><thead><tr>';
                        html += '<th><span>' + status + '</span><span style="float: right;">(' + subData.length + ')</span></th>';
                        html += '</tr></thead><tbody>';
                        for (var j = 0; j < subData.length; j++) {
                            current_subs_arr.push(subData[j].subId);
                            if (index++ == 1) {
                                currentSubId = subData[j].subId;
                                currentSubName = subData[j].name;
                            }
                            html += '<tr>';
                            html += '<td><a style="cursor: pointer;" onclick="callGetMarksAnalysis(' + subData[j].subId + ',\'' + subData[j].name + '\')">' + subData[j].name + '</a></td>';
                            html += '</tr>';
                        }
                        html += '</tbody></table></div><br>';
                    }
                    html += '</div>';
                    html += '<div class="col-md-10"><h3 id="subName" class="text"></h3><div id="marksAnalysis-line" style="height: 300px;"></div>   <div id="marksAnalysis-Bar" style="height: 300px; width: 100%; display: none;"></div>   <br><br><div class="form-group">';
                    for (var k = 0; k < assessments.length; k++) {
                        html += '<div class="col-md-3">';
                        html += '<label class="check"><input checked type="checkbox" name="assIds[]" class="icheckbox" value="' + assessments[k].assId + '" />' + assessments[k].assName + '</label>';
                        html += '</div>';
                    }
                    html += '</div><button class="btn  btn-primary pull-right" style="margin-right: 3px;" onclick="setSubject()">Refresh</button>';
                    html += '</div>';
                }
                $("#performance").html(html);
                setSubject();
            }
        });

    }

    function fillTable(marks, assessments, student) {
        if($.isEmptyObject(marks)) {
            $("#analysis").html('<h4 class="no-data-display">No Data</h4>');
        } else {
            var html = '<div class="std-container">';
            html += '<div class="modal-header" style="margin: 0 0 10px 0;">';
            html += `<h3>Marks Scored by <b>${student.stdName}</b> of class/section <b> ${student.class_name} / ${student.section_name}  (${student.admission_no})</b></h3>`;
            // html += `<button id="export-button7" class="btn btn-primary t-export-button pull-right" style="width: 6rem;">Export</button>`;
            html += '</div>';
            html += '<div class="std-marks">';
            html += '<table class="table table-bordered class7" id="id7">';
            html += '<thead>';
            html += '<tr>';
            html += '<th>Subjects</th>';
            // console.log(assessments);
            for(var aId in assessments) {
                html += '<th>'+assessments[aId].assName+'</th>';
            }
            html += '</tr>';
            html += '</thead>';
            html += '<tbody>';






let current_subs_arr = [];
let current_subs_name_arr = [];
let current_assessments_arr = [];








            for(var gId in marks) {
                var entities = marks[gId].entities;
                var totals = {};
                var ent_count = 0;
                for(var eId in entities) {
                    ent_count++;
                    html += '<tr>';
                    html += '<th>'+entities[eId].name+'</th>';
                    // console.log("entities[eId]", entities[eId]);
                    if(!current_subs_arr.includes(entities[eId].group_id)) {
                        current_subs_arr.push(entities[eId].group_id);
                        current_subs_name_arr.push(entities[eId].name);
                    }
                    var gData = {};
                    var assmts = entities[eId].assessments;
                    for(var aId in assessments) {
                        if(assmts.hasOwnProperty(aId)) {
                            var score = (assmts[aId].marks == -1)?0:assmts[aId].marks;
                            if(!totals.hasOwnProperty(aId)) {
                                totals[aId] = {};
                                totals[aId]['name'] = assmts[aId].name;
                                totals[aId]['marks'] = 0;
                                totals[aId]['total_marks'] = 0;
                            }
                            totals[aId]['marks'] += parseFloat(score);
                            totals[aId]['total_marks'] += parseFloat(assmts[aId].total_marks);
                            var mark = assmts[aId].marks;
                            mark = (mark == -3)?'NA':(mark==-2?'TBD':(mark==-1?'Absent':mark));
                            html += '<td>'+mark+'/'+(assmts[aId].total_marks).replace('.00', '')+'</td>';
                        } else {
                            html += '<td>-</td>';
                        }
                    }
                    html += '</tr>';
                }
                var subs_options= '';
                console.log(current_subs_name_arr);
                for(var iiii= 0; iiii < current_subs_arr.length; iiii++) {
                    subs_options += `<option value="${current_subs_arr[iiii]}">${current_subs_name_arr[iiii]}</option>`;
                }
                // console.log('subs_options', subs_options);
                $("#selected_subjects").html(subs_options);
                $("#selected_subjects option").eq(0).prop('selected', true);
                if(ent_count > 1) {
                    html += '<tr style="background:#ccc;">';
                    html += '<th>'+marks[gId].group_name+' Total</th>';
                    for(var aId in assessments) {
                        if(totals.hasOwnProperty(aId)) {
                            html += '<td>'+(totals[aId].marks).toFixed(2)+'/'+totals[aId].total_marks+'</td>';
                        } else {
                            html += '<td>-</td>';
                        }
                    }
                    html += '</tr>';
                    var entity = {};
                    entity['assessments'] = totals;
                    entity['name'] = marks[gId].group_name+' Total';
                    entity['short_name'] = marks[gId].group_name+' Total';
                    marks[gId].entities['total'] = entity;
                }
            }
            // console.log(marks);
            html += '</tbody>';
            html += '</table>';
            html += '</div>';
            html += '</div>';
            // $("#analysis").html(html);


            $('.class7').DataTable( {
                    dom: 'Blfrtip',
                    buttons: [{
                        extend: 'excelHtml5',
                        exportOptions: {
                            columns: ':visible'
                        }
                    }],
        });

        // Export Button Decoration
        $(".dt-buttons span").text("Export");
        }
    }


    var gData = [];

    function get_fee_summary_amount() {
        $('.loadingClassNew').show();
        $("#myfirstchart1").html('');
        $.ajax({
            url: '',
            type: 'post',
            data: '',
            success: function() {
                var feeAmount = '10000';
                var paidAmount = '50000';
                gData.push({
                    label: 'Total fee',
                    value: feeAmount,
                }, {
                    label: 'Fee paid',
                    value: paidAmount,
                }, );
                new Morris.Donut({
                    element: 'myfirstchart1',
                    data: gData,
                    colors: ['#FF0000', '#00FFFF'],
                    formatter: function(y) {
                        return new Intl.NumberFormat('en-IN', {
                            style: 'currency',
                            currency: 'INR'
                        }).format(y)
                    }
                });
            }
        });
    }
    function get_attendance() {
        var student_id = $('#selectStudents').val();
        var from_date = $('#from_date').val();
        var to_date =$('#to_date').val();
        
        $.ajax({
            url: '<?php echo site_url('student_analytics/student_analytics/get_subject_wise_attendance'); ?>',
            type: 'post',
            data: {
                'student_id':student_id,'from_date':from_date,'to_date':to_date
            },
            success: function(data) {
                let attendance_data=JSON.parse(data);
                console.log(attendance_data);
                let html =`<thead>
                            <tr>
                                <th class="text-center">Subject</th>
                                <th class="text-center">Total</th>
                                <th class="text-center">Present</th>
                                <th class="text-center">Absent</th>
                                <th class="text-center">Late</th>
                                <th class="text-center">Percentage</th>
                            </tr>
                        </thead>
                        <tbody>`;


                for(let i=0;i<attendance_data.length;i++){
                    let total=Number(attendance_data[i].present_days) + Number(attendance_data[i].absent) +Number(attendance_data[i].late);
                    let perc=(Number(attendance_data[i].present_days)/total)*100;
                    html +=`<tr>
                    <td>${attendance_data[i].subject_name}</td>
                    <td>${total}</td>
                    <td>${attendance_data[i].present_days}</td>
                    <td>${attendance_data[i].absent}</td>
                    <td>${attendance_data[i].late}</td>
                    <td>${perc.toFixed(2)}</td>
                    </tr>`;

                }
                html +=`</tbody>`;
                $('#attendance_table').html(html);

                
            }
        });
    }


   
    $(function() {
        var heightScreen = $(window).height();
        $('#show_student_after_selection .list-group').css({
            'height': heightScreen - 450
        });
        $('#show_student_after_selection .tab-pane').css({
            'height': heightScreen - 250
        });

        $('#show_student_after_selection .list-group').css({
            'overflow': 'scroll'
        });
        $('#show_student_after_selection .tab-pane').css({
            'overflow': 'scroll'
        });

    })
</script>
<style type="text/css">
    .profile {
        background: #fff;
    }
        .btn-hover-effect:hover {
            transform: scale(1.05);
        }

    .profile .profile-image {
        margin: 0;
    }

    .profile .profile-data .profile-data-name {
        color: #1b1e24;
    }

    .nav-tabs .nav-item.show .nav-link,
    .nav-tabs .nav-link.active {
        color: #f5f5f5;
        background-color: #1caf9a;
    }
</style>

<script type="text/javascript">
var aMerge = [];
$(document).ready(function(){
    var s_names = JSON.parse('<?php echo json_encode($all_names); ?>');
  
  for(var i=0; i < s_names.student.length; i++){

      aMerge.push(s_names.student[i].s_name.trim() + '('+s_names.student[i].class_name + s_names.student[i].section_name+')'+' ('+s_names.student[i].student_id+')');
  }

  // for(var j=0; j < s_names.staff.length; j++){
  //     aMerge.push(s_names.staff[j].s_name + ' ('+'staff'+')'+' ('+s_names.staff[j].id_number+')');
  // }

});

autocomplete(document.getElementById("staff_studentid"), aMerge);
var merge = '';
//console.log(aMerge);
 // alert(aMerge);
function autocomplete(inp, arr) {
  // alert(inp);
     /*the autocomplete function takes two arguments,
    the text field element and an array of possible autocompleted values:*/
    var currentFocus;
    /*execute a function when someone writes in the text field:*/
    inp.addEventListener("input", function(e) {
        var a, b, i, val = this.value;
        /*close any already open lists of autocompleted values*/
        closeAllLists();
        if (!val) { return false;}
        currentFocus = -1;
        /*create a DIV element that will contain the items (values):*/
        a = document.createElement("DIV");
        a.setAttribute("id", this.id + "autocomplete-list");
        a.setAttribute("class", "autocomplete-items");
        /*append the DIV element as a child of the autocomplete container:*/
        this.parentNode.appendChild(a);
        /*for each item in the array...*/
        for (i = 0; i < arr.length; i++) {
            /*check if the item starts with the same letters as the text field value:*/
          if (arr[i].substr(0, val.length).toUpperCase() == val.toUpperCase()) {
            /*create a DIV element for each matching element:*/
            b = document.createElement("DIV");
            c = document.createElement("DIV");
            b.style.cursor = 'pointer';
            /*make the matching letters bold:*/
            var stdNameSplit = arr[i].split('\(');

            var merge = stdNameSplit[0];
            var split1 = '('+stdNameSplit[1];
            var card_number = stdNameSplit[2];
          
            var cardNumberSplit = card_number.split(')');

            b.innerHTML = "<strong>" + merge.substr(0, val.length) + "</strong>";
            // b.innerHTML +=  arr[i].substr(val.length);
            b.innerHTML += merge.substr(val.length) + ' ' + split1;
            /*insert a input field that will hold the current array item's value:*/
            b.innerHTML += "<input type='hidden' value='" +merge + " " + split1 + "_" +cardNumberSplit+"'>";
            /*execute a function when someone clicks on the item value (DIV element):*/
            b.addEventListener("click", function(e) {
                /*insert the value for the autocomplete text field:*/
                inp.value = this.getElementsByTagName("input")[0].value;
                sepstdName = this.getElementsByTagName("input")[0].value;
               // alert(JSON.stringify(sepstdName));
               var nameSplit = sepstdName.split('_');
               
               var student_name = sepstdName.split('\(');
               var cNo = nameSplit[1];
               var lcNumber = cNo.split(',');
               // alert(lcNumber[0]);
                var output='';
                output+='<option value="'+lcNumber[0]+'">'+student_name[0]+' </option>';
                $("#selectStudents").html(output); 
                onchange_student_selection();
                
                /*close the list of autocompleted values,
                (or any other open lists of autocompleted values:*/
                closeAllLists();
            });
            a.appendChild(b);
          }
        }
    });
    /*execute a function presses a key on the keyboard:*/
    inp.addEventListener("keydown", function(e) {
        var x = document.getElementById(this.id + "autocomplete-list");
        if (x) x = x.getElementsByTagName("div");
        if (e.keyCode == 40) {
            /*If the arrow DOWN key is pressed,
            increase the currentFocus variable:*/
            currentFocus++;
            /*and and make the current item more visible:*/
            addActive(x);
        } else if (e.keyCode == 38) { //up
            /*If the arrow UP key is pressed,
            decrease the currentFocus variable:*/
            currentFocus--;
            /*and and make the current item more visible:*/
            addActive(x);
        } else if (e.keyCode == 13) {
            /*If the ENTER key is pressed, prevent the form from being submitted,*/
            e.preventDefault();
            if (currentFocus > -1) {
            /*and simulate a click on the "active" item:*/
            if (x) x[currentFocus].click();
            }
        }
    });
    function addActive(x) {
        /*a function to classify an item as "active":*/
        if (!x) return false;
        /*start by removing the "active" class on all items:*/
        removeActive(x);
        if (currentFocus >= x.length) currentFocus = 0;
        if (currentFocus < 0) currentFocus = (x.length - 1);
        /*add class "autocomplete-active":*/
        x[currentFocus].classList.add("autocomplete-active");
    }
    function removeActive(x) {
        /*a function to remove the "active" class from all autocomplete items:*/
      for (var i = 0; i < x.length; i++) {
        x[i].classList.remove("autocomplete-active");
      }
    }
    function closeAllLists(elmnt) {
        /*close all autocomplete lists in the document,
        except the one passed as an argument:*/
      var x = document.getElementsByClassName("autocomplete-items");
      for (var i = 0; i < x.length; i++) {
        if (elmnt != x[i] && elmnt != inp) {
        x[i].parentNode.removeChild(x[i]);
        }
      }
    }
    /*execute a function when someone clicks in the document:*/
    document.addEventListener("click", function (e) {
        closeAllLists(e.target);
    });
}

var flag = 0;
function download_fees_recipt($stdid) {
var student_id = $('#selectStudents').val();
console.log(student_id); 
   $.ajax({
        url:'<?php echo site_url('student_analytics/student_analytics/download_fee_recipt_by_rowid') ?>',
        type: "post",
        data: {
                'student_id': student_id
            },
        success : function(data){
          var data = JSON.parse(data);
          var fee_data = data.fee_data.fees;
          var student_data = data.fee_data.student;
          var insdata = data.fee_data.installments;
          var delete_authorization = data.delete_authorization;
          var refund = data.refund;
          var fee_adjustment_amount = data.fee_adjustment_amount;
          var fee_fine_amount = data.fee_fine_amount;
          var fee_refund_amount = data.fee_refund_amount;
          var excess_amount = data.excess_amount;

          if (fee_data.length == 0) {
            // $("#fees_content").html('<h3>Fee history data not found</h3>');
            $("#history_content").html('');
          }else{
          // $("#fees_content").html(prepare_fee_student_table(fee_data, student_data, insdata, fee_adjustment_amount, excess_amount));
            var fee_history = data.fee_history;
            // var student = data.student;
            var refund = data.refund;
            var html_content = '';
            var concession=0;
            var adjustment=0;
            var total_feeAmount=0;
            var balance=0;
            for (var i = 0; i < fee_history.length; i++) {
              html_content +=`<div class="col-md-12">`;
              if (fee_history[i].std_fee==null && fee_history[i].std_fee==undefined) {
              }
              else{

                concession = parseFloat(fee_history[i].concession_adjustment.total_concession_amount) + parseFloat(fee_history[i].concession_adjustment.total_concession_amount_paid);

                adjustment = parseFloat(fee_history[i].concession_adjustment.total_adjustment_amount) + parseFloat(fee_history[i].concession_adjustment.total_adjustment_amount_paid);

                total_feeAmount = (fee_history[i].std_fee.total_fee_paid == null ? 0 : parseFloat(fee_history[i].std_fee.total_fee_paid));

                balance = (fee_history[i].std_fee.total_fee == null ? 0 : parseFloat(fee_history[i].std_fee.total_fee))  - total_feeAmount - concession - adjustment;
                if (fee_history[i].std_fee.stdSchId!=null && fee_history[i].std_fee!=null && fee_history[i].std_fee!=undefined){
                  // html_content +='<div class="card cd_border mb-2">';
                  // html_content +=`<div class="card-header panel_heading_new_style_staff_border">
                  //                 <div class="row" style="margin: 0px;">
                  //                 <div class="d-flex justify-content-between" style="width:100%;">`;
                  // html_content +='<h3 class="card-title panel_title_new_style_staff" ><b>'+fee_history[i].name+'</b></h3>';
                  // html_content +='<div>';
                }
                // Download consolidate fee receipt download if fee balance is 0;
                if (fee_history[i].consolidated_receipt_html != null && fee_history[i].consolidated_receipt_html != '' &&  balance == 0) {
                  if (fee_history[i].std_fee.pdf_status == 1) {
                    var fullReceiptUrl = '<?php echo base_url() ?>feesv2/fees_collection/consolidated_receipt_pdf_download/'+fee_history[i].std_fee.stdSchId;
                    html_content +='<a class="new_circleShape_res mr-2" style="margin-left: 8px; background-color: #fe970a;" data-placement="top" data-toggle="tooltip" title="Download PDF" data-original-title="Download PDF" href="'+fullReceiptUrl+'"><i class="fa fa-cloud-download" style="font-size: 19px;"></i></a>';
                  }else{
                    html_content += '<ul class="panel-controls"> <a class="new_circleShape_res mr-2" id="pdf_generate" style="background-color: #fe970a;" onclick="generate_pdf_consolidate_feeReceipt('+fee_history[i].std_fee.stdSchId+')" data-toggle="tooltip" data-placement="top" title="Generate Fee Receipt" data-original-title="Generate Fee Receipt " href="javascript:void(0)"><i class="fa fa-file-text-o" style="font-size: 19px;"></i></a></ul>';
                  }
                }
                // html_content +='</div></div></div></div>';
                // html_content +='<div class="card-body">';
                
                if (fee_history[i].history && (fee_history[i].history!=null || fee_history[i].history!=undefined)) {
                    html_content +='<table class="table table-bordered" id="mytable">';
                    html_content +=`<thead>
                                      <tr>
                                        <th>Paid date</th>
                                        <th>Receipt number</th>
                                        <th>Amount Paid</th>
                                        <th>Concession Applied</th>`;
                                      if (fee_fine_amount) {
                                        html_content +=`<th>Fine</th>`;        
                                      }
                                      if(fee_adjustment_amount) {
                                        html_content +=`<th>Adjustment Applied</th>`;         
                                      }  
                                      if(fee_refund_amount) {
                                        html_content +=`<th>Refund Amount</th>`;         
                                      } 
                                      html_content +=`<th>Payment Mode</th>`;
                                      html_content +=`<th>Status</th>
                                        <th style="width: 15vw" >Action</th>
                                      </tr>
                                    </thead>`;
                    html_content +='<tbody>';
                    var history = fee_history[i].history
                    for (var h = 0; h < history.length; h++) {
                      visibility = 'style="display:none"';
                      if (flag == 0 && history[h].status == 'SUCCESS') {
                        visibility="style='display:'''";
                      }
                      if (flag == 1) {
                        visibility="style='display:'''";
                      }
                      
                      html_content +='<tr '+visibility+' >';
                      html_content +='<td>'+history[h].paid_date+'</td>';
                      html_content +='<td>'+history[h].receipt_number+'</td>';
                      html_content +='<td>'+numberToCurrency(history[h].total_amount)+'</td>';
                      html_content +='<td> ( '+numberToCurrency(history[h].concession_amount)+' )</td>';
                      if (fee_fine_amount) {
                        html_content +='<td>'+numberToCurrency(history[h].fine_amount)+'</td>';
                      }
                      if(fee_adjustment_amount) {
                        html_content +='<td> ( '+numberToCurrency(history[h].adjustment_amount)+' )</td>';
                      }  
                      if(fee_refund_amount) {
                        html_content +='<td> ( '+numberToCurrency(history[h].refund_amount)+' )</td>';
                      }
                      html_content +='<td>'+history[h].paymentValue+'</td>';
                      html_content +='<td>'+history[h].status+'</td>';
                      html_content +='<td>';

                      var url = '<?php echo base_url() ?>feesv2/fees_collection/fee_reciept_viewv1/'+history[h].id;

                      html_content +='<a class="circleButton_noBackColor_actionBtn" style="margin-left: 8px;background-color: #6893ca;color:white;" disabled target="_blank" data-placement="top" data-toggle="tooltip" data-original-title="Print Receipt" href="'+url+'"><i class="fa fa-print" ></i></a>';

                      html_content +='<span data-toggle="modal" data-target="#receipt_view_model" data-original-title="View"><a class="circleButton_noBackColor_actionBtn" style="margin-left: 8px ;background-color: #89ad4d; color:white;" disabled data-toggle="tooltip" data-placement="top" data-original-title="View" title="View"  data-placement="top" href="javascript:void(0)" onclick="fee_receipt_view('+history[h].id +')"><i class="fa fa-eye"></i></a></span>';

                      if (history[h].pdf_status == '1' && history[h].reconciliation_status != '3') {
                        var url = '<?php echo base_url() ?>feesv2/fees_collection/receipt_pdf_download/'+history[h].id;
                        html_content +='<a class="circleButton_noBackColor_actionBtn" style="margin-left: 8px;background-color: #31b0d5; color:white;" data-placement="top" data-toggle="tooltip" data-original-title="Download PDF " href="'+url+'"><i class="fa fa-cloud-download"></i></a>';
                      }
                   
                      if(history[h].reconciliation_status == '3'){
                        html_content +='<b style="float: right;" ><span style="color: red">Reconciliation Failed </span></b>';
                      }
                      else if(history[h].reconciliation_status == '1'){
                        html_content +='<b style="float: right;" ><span style="color: red">Reconciliation Pending </span></b>';
                      }

                   
                      html_content +='</td>';
                    } 
                }
                   
                if (fee_history[i].std_fee.stdSchId!=null && fee_history[i].std_fee!=null && fee_history[i].std_fee!=undefined){
                  html_content +='<div class="panel panel-default new-panel-style_3 mb-0">';
                  html_content +='<table class="table table-bordered"><thead>'; 
                  html_content +='<tr>';
                  html_content +=`<th rowspan="2" style="vertical-align: middle; text-align: center;">Fee Summary</th>
                                  <th>Total Fee Amount</th>
                                  <th>Total Fee Amount paid</th>
                                  <th>Total Concession</th>`; 

                  if(fee_adjustment_amount){
                      html_content +='<th>Total Adjustment</th>';   
                  }
                  if(fee_history[i].std_fee.discount != null && fee_history[i].std_fee.discount != 0){
                      html_content +='<th>Discount</th>';   
                  }
                  if(fee_fine_amount){
                    html_content +='<th>Total Fine</th>';
                  }
                  if(fee_history[i].std_fee.total_card_charge_amount != null && fee_history[i].std_fee.total_card_charge_amount != 0){
                      html_content +='<th>Card Charge Amount</th>';
                  }
                  if(fee_history[i].std_fee.loan_provider_charges != null && fee_history[i].std_fee.loan_provider_charges != 0){
                      html_content +='<th>Loan Provider Charges</th>';
                  }
                  html_content +='<th>Balance Amount</th>';
                  if(refund){
                      html_content +='<th>Refund Amount</th>';
                  }
                  html_content +='</tr>';
                  html_content +='<tr>';
                  html_content +='<td>'+numberToCurrency(fee_history[i].std_fee.total_fee)+'</td>';
                  html_content +='<td>'+numberToCurrency((fee_history[i].std_fee.total_fee_paid == null ? 0 : fee_history[i].std_fee.total_fee_paid))+'</td>';
                  html_content +='<td> ( '+numberToCurrency(concession)+' )</td>';
                  if(fee_adjustment_amount){
                    html_content +='<td> ( '+numberToCurrency(adjustment)+' ) </td>';
                  }
                  if(fee_history[i].std_fee.discount != null && fee_history[i].std_fee.discount != 0){
                      html_content +='<td>'+numberToCurrency(fee_history[i].std_fee.discount)+'</td>';
                  }
                  if(fee_fine_amount){
                      html_content +='<td>'+numberToCurrency(fee_history[i].std_fee.total_fine_amount)+'</td>';
                  }
                  if(fee_history[i].std_fee.total_card_charge_amount != null && fee_history[i].std_fee.total_card_charge_amount != 0){
                    html_content +='<td>'+numberToCurrency(fee_history[i].std_fee.total_card_charge_amount)+'</td>';
                  }
                  if(fee_history[i].std_fee.loan_provider_charges != null && fee_history[i].std_fee.loan_provider_charges != 0){
                    html_content +='<td>'+numberToCurrency(fee_history[i].std_fee.loan_provider_charges)+'</td>';
                  }
                  html_content +='<td>'+numberToCurrency(balance)+'</td>';
                  if(refund){
                    if(fee_history[i].std_fee.refund_amount!= null){
                      html_content +='<td>'+numberToCurrency(fee_history[i].std_fee.refund_amount)+'</td>';
                    }
                    else{
                      html_content +='<td></td>';
                    }

                  }
                  html_content +='</tr>';                
                  html_content +='</thead></table>'; 
                  // html_content +='</div></div>'; 
                }
                // html_content +='</div>';
              }
               
              html_content +='</div></div>';
            }

            $("#history_content").html(html_content);

          }
        }
    });
  }

  function _construct_subs_per_ass_graph(assIds, subject_not_required, subjectName_not_required){
        // console.log('current_subs_arr:::', current_subs_arr);
        var stdId = $('#students').val();
        var section_id = $('#section_id').val();
        $.ajax({
            url: '<?php echo site_url('student/student_controller/get_subs_per_ass'); ?>',
            type: 'POST',
            data: {'assIds':assIds, 'subject': current_subs_arr, 'stdId':stdId, 'section_id':section_id},
            dataType: 'json',
            success: function(stdData){

                let chartData = [];
                var subs= [];
                for (var v of stdData) {
                   var objects= {};
                    for(var i in v.perc_str) {
                        objects[i]= v.perc_str[i];
                        if(!subs.includes(i)) {
                            subs.push(i);
                        }
                    }
                    objects['ass_name']= v.assName;
                    chartData.push(objects);
                }

                $("#subjects_per_assessment_graph").html('');
                Morris.Bar({
                    // barGap: 0.3, // Increase this value to space the bars further apart
                    // barSizeRatio: 1.5, // Adjust this value to control bar width
                    element: 'subjects_per_assessment_graph',
                    data: chartData,
                    xkey: 'ass_name',
                    ykeys: subs,
                    labels: subs,
                    resize: true,
                    parseTime: false,
                    hideHover: true,
                    lineColors: ['#33414E', '#95B75D', '#123456', '#234567', '#345678', '#456789', '#56789a', '#6789ab', '#789abc', '#89abcd']
                });

                $("#show_hide_ass_wise").show();

            }
        })
        .fail(function(){
            // alert("Something Went Wrong!");
        });
    }

    function _construct_total_subs_per_ass_graph(assIds, subject_not_required, subjectName_not_required) {
        var stdId = $('#selectStudents').val();
        var section_id = $('#classSectionId').val();
        $.ajax({
            url: '<?php echo site_url('student/student_controller/get_total_subs_per_ass'); ?>',
            type: 'POST',
            data: {'assIds':assIds, 'subject': current_subs_arr, 'stdId':stdId, 'section_id':section_id},
            dataType: 'json',
            success: function(stdData){

                let chartData = [];
                let ass = [];
                let assvalue = [];
                for (var i in stdData) {
                    chartData.push(stdData[i]);
                    ass.push(stdData[i].assName);
                    assvalue.push(stdData[i]['perc']);
                }

                $("#total_subjects_per_assessment_Linegraph").html('');
                $("#total_subjects_per_assessment_Bargraph").html('');
                Morris.Line({
                    // barGap: 4, // Increase this value to space the bars further apart
                    // barSizeRatio: 0.55, // Adjust this value to control bar width
                    element: 'total_subjects_per_assessment_Linegraph',
                    data: chartData,
                    xkey: 'assName',
                    ykeys: ['perc'],
                    labels: ['Total Percentage'],
                    resize: true,
                    parseTime: false,
                    hideHover: true,
                    barColors: ['#3498db']
                });

                Morris.Bar({
                    // barGap: 4, // Increase this value to space the bars further apart
                    // barSizeRatio: 0.55, // Adjust this value to control bar width
                    element: 'total_subjects_per_assessment_Bargraph',
                    data: chartData,
                    xkey: 'assName',
                    ykeys: ['perc'],
                    labels: ['Total Percentage'],
                    resize: true,
                    parseTime: false,
                    hideHover: true,
                    barColors: ['#3498db']
                });

                var graph_type= $("#graph_type3").val();
                if(graph_type == 'line') {
                    $("#total_subjects_per_assessment_Linegraph").show();
                    $("#total_subjects_per_assessment_Bargraph").hide();
                } else {
                    $("#total_subjects_per_assessment_Linegraph").hide();
                    $("#total_subjects_per_assessment_Bargraph").show();
                }

                $("#show_hide_ass_wise_total").show();

            }
        })
        .fail(function(){
            // alert("Something Went Wrong!");
        });
    }

    function onclick_subject_wise_percentage() {
       setTimeout(() => {
        $("#subjects_per_assessment_graph").html(`<div class="flex justify-center items-center h-full">
                    <div>
                        <div class="spinner-border text-primary" role="status" style="font-size: large; height: 120px; width: 120px;">
                            <span class="sr-only">Loading...</span>
                        </div>
                    </div>
                </div>`);

            var assessments_arr= current_assessments_arr;
            var selected_subjects= $("#selected_subjects").val();
            var stdId = $('#selectStudents').val();
            var section_id = $('#classSectionId').val();
            var graph_type= $("#graph_type2").val();
            let opted_subjects_name= [];
            let color_codes_arr= [];
            $("#selected_subjects option").each(function() {
                if(selected_subjects.includes($(this).val())) {
                    opted_subjects_name.push('Average ' + $(this).text());
                    opted_subjects_name.push($(this).text());
                    color_codes_arr.push('#33414E');
                    color_codes_arr.push('#95B75D');
                }
            });
            $.ajax({
                url: '<?php echo site_url('student/student_controller/getMarksAnalysis_version_2'); ?>',
                type: 'POST',
                data: {'assIds':assessments_arr, 'subject':selected_subjects, 'stdId':stdId, 'section_id':section_id},
                dataType: 'json',
                success: function(stdData){
                    console.log('stdData', stdData);
                    $("#subjects_per_assessment_graph").html('');
                    if(graph_type == 'line') {
                        Morris.Line({
                            element: 'subjects_per_assessment_graph',
                            data: stdData,
                            xkey: 'assName',
                            ykeys: opted_subjects_name,
                            labels: opted_subjects_name,
                            resize: true,
                            parseTime: false,
                            hideHover: true,
                            lineColors: ['lightpink', 'pink', 'green', 'lightgreen', 'blue', 'lightblue', 'gray', 'lightgray', 'lime', 'aqua']
                        });
                    } else {
                        Morris.Bar({
                            element: 'subjects_per_assessment_graph',
                            data: stdData,
                            xkey: 'assName',
                            ykeys: opted_subjects_name,
                            labels: opted_subjects_name,
                            resize: true,
                            parseTime: false,
                            hideHover: true,
                            lineColors: ['lightpink', 'pink', 'green', 'lightgreen', 'blue', 'lightblue', 'gray', 'lightgray', 'lime', 'aqua']
                        });
                    }

                }
            })

       }, 1000);
    }

    $(document).ready(() => {
        $(".select2").select2();
    });

</script>

<style>
    .select2-selection, .select2 {
        width: 500px;
        height: 50px;
        overflow: auto;
    }
</style>


<div id="medical_full_details"  class="modal" tabindex="-1" role="dialog" >

  <div class="modal-dialog" role="document" style="width:48%;margin: auto;border-radius: .75rem">
    <div class="modal-content">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">Hospitalization Details</h4>
        <button type="button" class="close" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
      </div>
        <div class="modal-body" style="height:450px; overflow: scroll;">
          <div class="col-sm-12" style="margin-bottom: 10px; padding-left:0px;
          padding-right:0px;">
            <div class="form-group col-sm-12">
              <div class="form-group">
                <label class="control-label" style="margin-bottom: 0px;"> Hospital Admitted To</label>
                <input type="text" readonly class="form-control" id="hospital_admitted_to">
              </div>
            </div>
          </div>
          <div class="col-sm-12" style="margin-bottom: 10px; padding-left:0px;
        padding-right:0px;">
            <div class="form-group col-sm-12">
              <div class="form-group">
                <label class="control-label" style="margin-bottom: 0px;"> Hospitilization Date</label>
                <input type="text" readonly class="form-control" id="hospitilization_date" >
              </div>
            </div>
          </div>
          <div class="col-sm-12" style="margin-bottom: 10px; padding-left:0px;
      padding-right:0px;">
            <div class="form-group col-sm-6 remove_edit_form">
              <div class="form-group">
                <label class="control-label" style="margin-bottom: 0px;">Discharge Date</label>
                <input type="text" readonly class="form-control" id="discharge_date">
              </div>
            </div>
          </div>
          <div class="col-sm-12" style="padding: 0px 15px 10px 5px; ">
            <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
              <label class="control-label" style="margin-bottom: 0px;"> Reason</label>
              <textarea readonly rows="4" class="form-control" id="hospital_reason">
        </textarea>
            </div>
          </div>
          <div class="col-sm-12 remove_edit_form" style="padding: 0px 15px 10px 5px; ">
            <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
              <label class="control-label" style="margin-bottom: 0px;"> Treatment Provided</label>
               <input type="text" readonly class="form-control" id="treatment_provided">
            </div>
          </div>
          <div class="col-sm-12 remove_edit_form" style="padding: 0px 15px 10px 5px; ">
            <div class="form-group col-sm-12" style="padding:7px 0 7px 8px;">
              <label class="control-label" style="margin-bottom: 0px;"> Remarks</label>
              <textarea readonly rows="4" cols="500" class="form-control" id="Remarks">
              </textarea>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        </div>
    </div>
  </div>
</div>
</div>


<div class="modal fade" id="recipts_by_id" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
      <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
        <h4 class="modal-title">Fee Recipts  </h4>
      </div>
        <div class="modal-body" style="height:450px; overflow: scroll;">
            <!-- <div id="fees_content">
            </div> -->
          <div id="history_content">
                    
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
        </div>
    </div>
  </div>
</div>

<div class="modal fade" id="approve_single_window" role="dialog" style="padding-right: 4px;">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content" style="width:48%;margin: auto;border-radius: .75rem">
            <div class="modal-header" style="border-top-left-radius: .75rem;border-top-right-radius: .75rem;">
                <h4 class="modal-title" id="modal_header"></h4>
                <button type="button" class="close" style="padding: 0px;" data-dismiss="modal"><i class="fa fa-times" aria-hidden="true" style="color: #d80403;font-size: 21px;"></i></button>
            </div>
            <form action="" id="single_window_form" method="post" data-parsley-validate="">
            <div class="modal-body">
                <input type="hidden" name="" id="team_name">
                <div class="col-md-12" id="single_window_approval_content">

                </div>
            </div>
            </form>
            <div class="panel-footer" style="margin-top: 10px;">
                    <button class="btn btn-success mr-4 pull-right" onclick="submit_approval_process('Approved')" style="width: 10rem;border-radius:0rem" >Approve</button>
                  <button class="btn btn-danger mr-4 pull-right" onclick="submit_approval_process('Rejected')" style="width: 10rem;border-radius:0rem" >Reject</button>
            </div>
        </div>
    </div>
</div>

<style>
#staff_studentidautocomplete-list{
    height: 120px;
    overflow: scroll;
}
</style>