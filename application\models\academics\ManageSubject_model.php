<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ManageSubject_model extends CI_Model
{
	private $yearId;
	public function __construct()
	{
		parent::__construct();
		$this->yearId = $this->acad_year->getAcadYearId();
  }

  public function getClasses(){
    $result = $this->db_readonly->select('cm.class_name, cm.id as class_master_id, c.id as class_id')
    ->from("class_master cm")
    ->join("class c", "c.class_master_id=cm.id")
    ->where("c.acad_year_id",$this->yearId)
    ->order_by('cm.display_order')
    ->get()->result();
      return $result;
  }

  public function get_academic_years(){
    return $this->db_readonly->select("ay.id, ay.acad_year")
    ->from("academic_year ay")
    ->join("subject_master sm","sm.acad_year_id=ay.id")
    ->where("ay.id<", $this->yearId)
    ->group_by("ay.id")
    ->get()->result();
  }

  public function getClassesForClone($data){
    // echo "<pre>"; print_r($data); die();
    return $this->db_readonly->select("class_master_id, class_name")
    ->from("class")
    ->where("acad_year_id",$data["acadYearId"])
    ->get()->result();
  }

  public function clone_previous_class($data){
    // check for semester also while clong subjects of 
    $cloneToSemesterId = 0;
    $cloneFromSemesterId=0;
    $is_semester_scheme = $this->settings->getSetting('is_semester_scheme');
    if(!empty($data["fromSemesterId"]) && !empty($data["toSemesterId"]) && $is_semester_scheme){
      $cloneFromSemesterId = $data["fromSemesterId"];
      $cloneToSemesterId = $data["toSemesterId"];
    }

    // check for empty subjects
    // 1. if subjects for particular class with new acad year is present then return -1 else proceed
    $oldAcadYearId = $data["oldAcadYearId"];
    $newAcadYearId = $data["newAcadYearId"];
    $oldClassMasterId = $data["oldClassMasterId"];
    $newClassMasterId = $data["newClassMasterId"];

    if($cloneToSemesterId>0){
      $is_already_subjects_present_for_new_class = $this->db_readonly->select("s.id")
        ->from("lp_subjects s")
        ->join('subject_master sm', 'sm.id=s.subject_master_id')
        ->where("s.class_master_id", $newClassMasterId)
        ->where("s.acad_year_id", $newAcadYearId)
        ->where("s.semester_id", $cloneToSemesterId)
        ->get()->result();
    }else{
      $is_already_subjects_present_for_new_class=$this->db_readonly->select("s.id")
        ->from("lp_subjects s")
        ->join('subject_master sm', 'sm.id=s.subject_master_id')
        ->where("s.class_master_id",$newClassMasterId)
        ->where("s.acad_year_id",$newAcadYearId)
        ->get()->result();
    }
    
    if(!empty($is_already_subjects_present_for_new_class)){
      return -1;
    }

    $this->db->trans_start();
    // 1. bring all the added "subject master ids" from previous acad year based on class master id and acad year id
    if($cloneFromSemesterId>0){
      $lp_subjects=$this->db_readonly->select("s.*")
        ->from("lp_subjects s")
        ->join('subject_master sm', 'sm.id=s.subject_master_id')
        ->where("s.class_master_id",$oldClassMasterId)
        ->where("s.acad_year_id", $oldAcadYearId)
        ->where("s.semester_id",$cloneFromSemesterId)
        ->get()->result_array();
    }else{
      $lp_subjects = $this->db_readonly->select("s.*")
        ->from("lp_subjects s")
        ->join('subject_master sm', 'sm.id=s.subject_master_id')
        ->where("s.class_master_id", $oldClassMasterId)
        ->where("s.acad_year_id", $oldAcadYearId)
        ->get()->result_array();
    }
    if(empty($lp_subjects)){
      $this->db->trans_rollback();
      return -2;
    }
    // 3. using point 1 -> add these subjects to the current lp_subjects with class_master and subject_master id change
    $store_old_new_lp_subject_ids=[];
    foreach($lp_subjects as $key => $val){
      $store_old_new_lp_subject_ids[$key]['old_lp_subject_id']=$val['id'];
      $storeData=$val;
      $storeData['id']='';
      $storeData['class_master_id'] = $newClassMasterId;
      $storeData['acad_year_id'] = $newAcadYearId;

      if($cloneToSemesterId>0){
        $storeData['semester_id'] = $cloneToSemesterId;
      }else{
        $storeData['semester_id'] = $val["semester_id"];
      }

      $this->db->insert("lp_subjects", $storeData);
      $store_old_new_lp_subject_ids[$key]['new_lp_subject_id'] = $this->db->insert_id();
    }
    // 4. using clone_individual_subject method clone all the lying lessons and topics
    foreach($store_old_new_lp_subject_ids as $key => $val){
      $this->clone_individual_subject(["oldLpSubjectId"=>$val['old_lp_subject_id'],"newLpSubjectId"=>$val['new_lp_subject_id']]);
    }

    $this->db->trans_complete();

    // 5. on success return 1/ on error return 0/ on if exists already return -1 
    if($this->db->trans_status()==false){
      $this->db->trans_rollback();
      return 0;
    }

    $this->db->trans_commit();
    return 1;
  }

  public function getLpSubjectsForClone($data){
    // echo "<pre>"; print_r($data); die();
    return $this->db_readonly->select("id, subject_name")
    ->from("lp_subjects")
    ->where("acad_year_id",$data["acadYearId"])
    ->where("class_master_id",$data["classMasterId"])
    ->get()->result();
  }

  public function clone_individual_subject($data){
    $oldLpSubjectId= $data["oldLpSubjectId"];
    $newLpSubjectId = $data["newLpSubjectId"];
    
    // check for dupliacte data
    $isSubjectDataPresentAlready=$this->db_readonly->select("id")
    ->from("lp_lessons")
    ->where("lp_subject_id",$newLpSubjectId)
    ->get()->result();

    if($isSubjectDataPresentAlready){
      return -1;
    }
    
    $this->db->trans_start();
    $allLessons=$this->db_readonly->select("*")
    ->from("lp_lessons")
    ->where("lp_subject_id",$oldLpSubjectId)
    ->get()->result_array();
    
    $storeOldNewLessonIds=[];
    $lessonCount=0;
    foreach($allLessons as $key2 => $val2){
      $storeOldNewLessonIds[$lessonCount]["old_lesson_id"] = $val2["id"];
      $Objectdata = $val2;
      $Objectdata["id"] = "";
      $Objectdata["lp_subject_id"] = $newLpSubjectId;

      $this->db->insert("lp_lessons", $Objectdata);
      $storeOldNewLessonIds[$lessonCount]["new_lesson_id"] = $this->db->insert_id();
      $lessonCount++;
    }

    foreach ($storeOldNewLessonIds as $key => $val) {
      $allTopics = $this->db_readonly->select("*")
        ->from("lp_sub_topics")
        ->where("lp_lesson_id", $val["old_lesson_id"])
        ->get()->result_array();

      foreach ($allTopics as $key2 => $val2) {
        $Objectdata = $val2;
        $Objectdata["id"] = "";
        $Objectdata["lp_lesson_id"] = $val["new_lesson_id"];
        $this->db->insert("lp_sub_topics", $Objectdata);
      }
    }

    $this->db->trans_complete();
    if($this->db->trans_status()==false){
      $this->db->trans_rollback();
      return 0;
    }

    $this->db->trans_commit();
    return 1;
  }

  public function clone_previous_syllabus($data){
    $checkNextYearSubjectsExists= $this->db_readonly->select("id")
      ->from("lp_subjects")
      ->where("acad_year_id", $this->yearId)
      ->get()->num_rows();

    if($checkNextYearSubjectsExists){
      return -1;
    }

    $from_acad_year_id = $data["fromAcadYearId"];
    $is_data_exists_in_from_acad_year=$this->db->select("id")
    ->from("class")
    ->where("acad_year_id",$from_acad_year_id)
    ->get()->num_rows();

    if($is_data_exists_in_from_acad_year==0){
      return 0;
    }

    $this->db->trans_start();
    $allClasses=$this->db_readonly->select("*")
    ->from("class c")
    ->where("c.acad_year_id",$from_acad_year_id)
    ->get()->result_array();

    foreach($allClasses as $key => $val){
      $data=$val;
      $data["id"]="";
      $data["acad_year_id"]=$this->yearId;
      $this->db->insert("class",$data);

      $oldAcadYearId=$from_acad_year_id;
      $newAcadYearId=$this->yearId;
      $oldClassMasterId=$val["class_master_id"];
      $newClassMasterId=$oldClassMasterId;

      $this->clone_previous_class(["oldAcadYearId"=>$oldAcadYearId, "newAcadYearId" => $newAcadYearId, "oldClassMasterId" => $oldClassMasterId, "newClassMasterId" => $newClassMasterId]);
    }

    $this->db->trans_complete();
    if ($this->db->trans_status() === FALSE) {
      $this->db->trans_rollback();
      return 0;
    }
    
    $this->db->trans_commit();
    return 1;
  }

  public function get_class_section_and_subject_ids_for_lms_access_control(){
    $class_sections_and_subject_ids=$this->db_readonly->select("lp_subjects_id, class_section_id, access_level")
    ->from("lp_subjects_section_staff")
    ->where("staff_id",$this->authorization->getAvatarStakeHolderId())
    ->get()->result();

    $subject_list=[];
    $class_section_list = [];
    foreach($class_sections_and_subject_ids as $key => $val){
      if(!array_key_exists($val->lp_subjects_id,$subject_list)){
        $subject_list[$val->lp_subjects_id]=$val->lp_subjects_id;
      }

      if (!array_key_exists($val->class_section_id, $class_section_list)) {
        $class_section_list[$val->class_section_id] = $val->class_section_id;
      }
    }

    return ["class_section_list"=>$class_section_list,"subject_list"=>$subject_list];
  }

  public function getClassesForLms(){
    $is_staff_access_control_enabled=$this->settings->getSetting("enable_staff_class_subject_access_control_for_lms");
    $is_lms_admin=$this->authorization->isAuthorized('LESSON_PLAN.ADMIN');
    
    if($is_staff_access_control_enabled==1 && !$is_lms_admin){
      $class_section_and_subject_lists=$this->get_class_section_and_subject_ids_for_lms_access_control();
      $class_section_ids=$class_section_and_subject_lists["class_section_list"];

      return $this->db_readonly->select('c.class_name, c.class_master_id, c.id as class_id')
      ->from("class c")
      ->join("class_section cs","cs.class_id=c.id")
      ->where("c.acad_year_id", $this->yearId)
      ->where_in("cs.id", $class_section_ids)
      ->group_by("c.id")
      ->get()->result();
    }else{
      return $this->db_readonly->select('c.class_name, c.class_master_id, c.id as class_id')
      ->from("class c")
      ->join("class_section cs","cs.class_id=c.id")
      ->where("c.acad_year_id", $this->yearId)
      ->group_by("c.id")
      ->get()->result();
    }
  }

  public function getAllSemesters() {
    return $this->db_readonly->select('id, sem_name')->get('semester')->result();
  }

  public function get_class_semesters($class_master_id) {
    return $this->db_readonly->select('s.id, s.sem_name')->from('semester s')->join('class_master_semester cms', 'cms.sem_id=s.id')->where('cms.class_master_id', $class_master_id)->get()->result();
  }

  public function getSelectedPeriodTemplates($data){
    $class_id = $data['class_id'];

    return $this->db_readonly->select("ifnull(c.ttv2_template_id_for_lms_check_in,0) as ttv2_template_id, t.name as ttv2_template_name")
    ->from("class c")
    ->join("ttv2_template t","c.ttv2_template_id_for_lms_check_in=t.id")
    ->join("ttv2_section_templates st", "st.ttv2_template_id=t.id")
    ->join("ttv2_section_template_periods stp", "stp.ttv2_section_template_id=st.id")
    ->where("c.id",$class_id)
    ->where("c.acad_year_id",$this->yearId)
    ->group_by("t.id")
    ->get()->row();
  }

  public function callGetSubjects(){
      $class_master_id = $_POST['class_master_id'];

      if(!empty($_POST['semester_main_screen_id'])){
        $semester_main_screen_id = $_POST['semester_main_screen_id'];
      }else{
        $semester_main_screen_id=0;
      }
      
      $this->db_readonly->distinct()->select('s.*, s.id as subject_id,count(l.id) as TotalLessons, sem.sem_name')
        ->from('lp_subjects s')
        ->join('lp_lessons l','l.lp_subject_id=s.id','left')
        ->join('semester sem', 'sem.id=s.semester_id', 'left')
        ->join('subject_master sm', 'sm.id=s.subject_master_id')
        ->where('s.acad_year_id',$this->yearId)
        ->where('s.class_master_id',$class_master_id);
  
      if ($this->settings->getSetting('is_semester_scheme')) {
        $this->db_readonly->where('s.semester_id',$semester_main_screen_id);
      }

      $result = $this->db_readonly->group_by('s.id')
      ->get()->result();

      // Getting staff details if assigned
      foreach($result as $key => $value) {
        $a= $this->db_readonly->select('concat( ifnull(sm.first_name, ""), " ", ifnull(sm.last_name, "") ) as staff_name, ifnull(ls3.staff_id, "") as staff_id')
              ->from('staff_master sm')
              ->join('lp_subjects_section_staff ls3', 'ls3.staff_id= sm.id', 'left')
              ->where('ls3.staff_type', 'primary')
              ->where('ls3.lp_subjects_id', $value->id)
              ->get();

        $b= $this->db_readonly->select('concat( ifnull(sm.first_name, ""), " ", ifnull(sm.last_name, "") ) as assistant_staff_name, ifnull(ls3.staff_id, "") as ass_staff_id')
              ->from('staff_master sm')
              ->join('lp_subjects_section_staff ls3', 'ls3.staff_id= sm.id', 'left')
              ->where('ls3.staff_type', 'assistant')
              ->where('ls3.lp_subjects_id', $value->id)
              ->get();

              if($a->num_rows() == 1) {
                $result[$key]->staff_name= $a->row()->staff_name;
              } else {
                $result[$key]->staff_name= 'Not Assigned';
              }
              if($b->num_rows() == 1) {
                $result[$key]->assistant_staff_name= $b->row()->assistant_staff_name;
              } else {
                $result[$key]->assistant_staff_name= 'Not Assigned';
              }
      }
      // echo '<pre>'; print_r($result); die();
      return $result;
  }

  public function delete_subject($subject_id) {
    //Check resources
    $resources_count = $this->db_readonly->where('subject', $subject_id)->from('resources')->count_all_results();
    if ($resources_count > 0) return -1;

    //Check Subject attendance
    //No need to check subject attendance as attendance depends on subject_master and not lp_subjects

    //Check if lp_subjects used in lp_tasks or not
    $taskss_count = $this->db_readonly->where('subject_id', $subject_id)->from('lp_tasks')->count_all_results();
    if ($taskss_count > 0) return -2;

    //Check lessons
    $lessons_count = $this->db_readonly->where('lp_subject_id', $subject_id)->from('lp_lessons')->count_all_results();
    if ($lessons_count > 0) return -3;

    //Delete if reached here
    $this->db->where('id',$subject_id);
    $result = $this->db->delete('lp_subjects');

    if ($result) 
      return 1;
    else 
      return 0;
  }

    public function get_lesson(){
        $subject_id=$_POST['subject_id'];
        $sqllesson="select distinct l.*,l.lesson_name as lesson_name,l.id as lesson_id,s.subject_name as subject_name,s.id as subject_id from  lp_lessons l left join lp_subjects s on l.lp_subject_id=s.id where l.lp_subject_id= $subject_id";

        // $sqlsubtopic= "select l.id as lesson_id,st.sub_topic_name as subtopicName, st.id as sub_topic_id from  lp_sub_topics st left join lp_lessons l on st.lp_lesson_id=l.id ";

        $sqlsubtopic= "select st.lp_lesson_id as lesson_id, st.id as sub_topic_id, st.sub_topic_name as sub_topic_name, count(ls.id) as sessions_count from lp_sub_topics st
        left join lp_session ls on ls.lp_topic_id=st.id and ls.status='Active'
        where lp_lesson_id in (select id from lp_lessons where lp_subject_id=$subject_id)
        group by st.id;";

        $masterData = $this->db_readonly->query($sqllesson)->result();
        $slaveData = $this->db_readonly->query($sqlsubtopic)->result();
        foreach ($masterData as &$lesson) {
          $lesson->sub_topic_arr = [];
          foreach ($slaveData as $subtopic) {
            if ($lesson->lesson_id == $subtopic->lesson_id ) {
              $lesson->sub_topic_arr[] = $subtopic;
            }
          } 
        }
        return $masterData;
    }

  public function delete_subtopic_by_id($sub_topic_id){
    $this->db->where('id',$sub_topic_id);
    return $this->db->delete('lp_sub_topics');
  }

  public function delete_lesson_by_id($lesson_id){
    $this->db->where('id',$lesson_id);
    return $this->db->delete('lp_lessons');
  }
  public function get_subtopic(){

    $subject_id=$_POST['subject_id'];
    $result=$this->db_readonly->select("ls.*,ls.lp_lesson_id as lesson_id,l.lp_subject_id as subject_id,s.subject_name as subject_name,l.lesson_name as lesson_name")
    ->from('lp_sub_topics ls')
    ->join('lp_lessons l','l.id=ls.lp_lesson_id', 'left')
    ->join('lp_subjects s','s.id=l.lp_subject_id')
    ->where('l.lp_subject_id',$subject_id)
   
    ->get()->result();
    return $result;
  }
    
  public function submitSubjects(){
    $input = $this->input->post();
    $subject_master_id = $input['subject_master_id'];
    $semester_id = ($input['semester_id'])?$input['semester_id']:NULL;
    $subject = $this->db->select("id, subject_name, short_name, mapping_string, order")->where('id', $subject_master_id)->get('subject_master')->row();
    $data = array(
        'class_name' => $input['class_name'],
        'class_master_id' => $input['class_master_id'],
        'subject_master_id' => $subject->id,
        'subject_name' => $subject->subject_name,
        'short_name' => $subject->short_name,
        'mapping_string' => $subject->mapping_string,
        'order' => $subject->order,
        'semester_id' => $semester_id,
        'acad_year_id'=>$this->yearId
    );
    return $this->db->insert('lp_subjects ',$data);
  }

    public function add_lesson($subject_id, $lesson_name){
        $duplicates = $this->db->query("select count(id) as count from lp_lessons where lp_subject_id=$subject_id and lesson_name='$lesson_name'")->row();
        $lesson_id = 0;
        if($duplicates->count > 0) {
          $lesson_id = -1;
        } else {
          $lesson = array(
            'lp_subject_id' => $subject_id,
            'lesson_name' => $lesson_name
          );
          // echo "<pre>";print_r($lesson);die();
          $this->db->insert('lp_lessons',$lesson);
          $lesson_id = $this->db->insert_id();
        }
        return $lesson_id;
    }

  public function edit_lesson($lesson_id, $lesson_name) {
    $this->db->where('id', $lesson_id)->update('lp_lessons', ['lesson_name' => $lesson_name]);

    return $lesson_id;
  }
    
  public function add_topic($lesson_id, $topic_name) {
    //Check if there is a duplicate topic name
    $duplicates = $this->db->query("select count(id) as count from lp_sub_topics where lp_lesson_id=$lesson_id and sub_topic_name='$topic_name'")->row();
    $topic_id = 0;
    if($duplicates->count > 0) {
      $topic_id = -1;
      return -1;
    }

    $sub_topic = array(
      'lp_lesson_id' => $lesson_id,
      'sub_topic_name' => $topic_name
    );
    $this->db->insert('lp_sub_topics',$sub_topic);
    $topic_id = $this->db->insert_id();

    return $topic_id;
  }

  public function edit_topic($topic_id, $topic_name) {
    $this->db->where('id', $topic_id)->update('lp_sub_topics', ['sub_topic_name' => $topic_name]);

    return $topic_id;
  }

      public function get_lessons($subject_id) {
        return $this->db_readonly->select("id, lesson_name")->where('lp_subject_id', $subject_id)->get('lp_lessons')->result();
      }
    /*  public function get_lessons($subject_id) {
        return $this->db->select("l.id, l.lesson_name,st.sub_topic_name")
        ->from('lp_lessons l')
        ->join('lp_subjects s','l.lp_subject_id=s.id')
        ->join('lp_sub_topics st','st.lp_lesson_id=l.id')

        ->where('lp_subject_id', $subject_id)
        ->get()->result();
      }*/
    
      public function get_sub_topics($lesson_id) {
        return $this->db_readonly->select("id, sub_topic_name")->where('lp_lesson_id', $lesson_id)->get('lp_sub_topics')->result();
      }
 
    public function get_not_added_subjects($class_master_id, $semester_id, $is_semester_scheme){
        if ($is_semester_scheme) {
          return $this->db_readonly->select("id, subject_name")->from('subject_master')->where("id not in (select subject_master_id from lp_subjects where class_master_id=$class_master_id and semester_id=$semester_id and acad_year_id=$this->yearId)")->order_by('order, subject_name')->get()->result();
        } else {
          return $this->db_readonly->select("id, subject_name")->from('subject_master')->where("id not in (select subject_master_id from lp_subjects where class_master_id=$class_master_id and acad_year_id=$this->yearId)")->order_by('order, subject_name')->get()->result();
        }
    }

  public function update_subject_semester($subject_id, $semester_id) {
    return $this->db->where('id', $subject_id)->update('lp_subjects', ['semester_id' => $semester_id]);
  }

  public function get_sections_and_subject_teachers($class_master_id,$lp_subject_id) {
    // get staff assigned to sections
    $staff_assigned_to_sections=$this->db_readonly->select("*")
    ->from("lp_subjects_section_staff")
    ->where("staff_type","section")
    ->where("lp_subjects_id",$lp_subject_id)
    ->get()->result();

    $staff_list=$this->db_readonly->select("id, first_name, last_name")
    ->from("staff_master")
    ->get()->result();
    $staff_assigned_list=[];
    foreach($staff_list as $key => $val){
      $staff_assigned_list[$val->id]=$val->first_name.' '.$val->last_name;
    }
    $staff_assigned_to_specific_section_list=[];
    foreach($staff_assigned_to_sections as $key => $val){
      $val->staff_name=$staff_assigned_list[$val->staff_id];
      $staff_assigned_to_specific_section_list[$val->class_section_id][] = $val;
    }
    
    $sections=$this->db_readonly->select("cs.id as section_id, cs.section_name as section_name, c.id as class_id, c.class_name")
    ->from('class_section cs')
    ->join('class c', 'c.id= cs.class_id', 'left')
    ->join('class_master cm', 'cm.id= c.class_master_id')
    ->where('cm.id', $class_master_id)
    ->where('c.acad_year_id', $this->yearId)
    ->get()->result();

    foreach($sections as $key => $val){
      if(!empty($staff_assigned_to_specific_section_list[$val->section_id])){
        $val->sectionAssigned=$staff_assigned_to_specific_section_list[$val->section_id];
      }
      // echo "<pre>"; print_r($val); die();
    }

    return ['secs' => $sections];
  }

  public function remove_teacher_assigned_to_section($data){
    return $this->db->delete("lp_subjects_section_staff",["id"=>$data["assignedSectionStaffId"]]);
  }

  public function get_subject_staffs($data) {
    // get  all the assigned staff ids
    $staff_is=$this->db_readonly->select("staff_id")
    ->from("lp_subjects_section_staff")
    ->where("class_section_id",$data["sec_id"])
    ->where("lp_subjects_id", $data["subject_id"])
    ->get()->result();

    $staff_list=[];
    foreach($staff_is as $key => $val){
      $staff_list[]=$val->staff_id;
    }

      $this->db_readonly->select('sm.id, concat(ifnull(sm.first_name, ""), " ", ifnull(sm.last_name, "")) as staff_name');
    // ->where('status', 0)
      if(!empty($staff_list)){;
        $this->db_readonly->where_not_in('sm.id', $staff_list);
      }
      $this->db_readonly->where('sm.status', 2);

      if($data["staff_type_id"]!=-1){
        $this->db_readonly->where('sm.staff_type', $data["staff_type_id"]);
      }
      
      $this->db_readonly->where('sm.is_primary_instance', 1);
      $this->db_readonly->order_by('sm.first_name');
    return $this->db_readonly->get('staff_master sm')->result();
  }

  public function assign_teacher_to_the_subject($loggedIn_staff_id, $sub_id, $type, $sec_id, $staff_id, $accessibility) {
    // primary
    // assistant
    $pre_check= $this->db_readonly->select()
      ->where('lp_subjects_id', $sub_id)
      ->where('staff_type', $type)
      ->where('class_section_id', $sec_id)
      ->get('lp_subjects_section_staff');

    if($type=="primary" && $pre_check || $type == "assistant" && $pre_check){
      // update goes here
        $data_update= array(
          'staff_id' => $staff_id,
          'access_level' => $accessibility,
          'created_by_id' => $loggedIn_staff_id
        );

      if ($pre_check->num_rows() == 1) {
        return $this->db->where('lp_subjects_id', $sub_id)
          ->where('staff_type', $type)
          ->where('class_section_id', $sec_id)
          ->update('lp_subjects_section_staff', $data_update);
      }
    }

    // insert goes here
    $data_insert= array(
      'lp_subjects_id' => $sub_id,
      'staff_type' => $type,
      'class_section_id' => $sec_id,
      'staff_id' => $staff_id,
      'access_level' => $accessibility,
      'created_by_id' => $loggedIn_staff_id
    );
    
    return $this->db->insert('lp_subjects_section_staff', $data_insert);
  }

  public function get_details_if_exist($sub_id) {
    $teacher=$this->db_readonly->select('concat(ifnull(sm.first_name, ""), " ", ifnull(sm.last_name, "")) as staff_name, sm.id as staff_id, ls3.access_level, ls3.class_section_id, ls3.staff_type, ls3.id as lp_subjects_section_staff_id')
    ->from('staff_master sm')
    ->join('lp_subjects_section_staff ls3', 'ls3.staff_id= sm.id', 'right')
    ->where('lp_subjects_id', $sub_id)
    ->get()->result();

    return ['teacher' => $teacher];
  }

  public function getPeriodTemplates(){
    return $this->db_readonly->select("id, name")
    ->from("ttv2_template")
    ->where("is_disabled",0)
    ->where("acad_year_id", $this->yearId)
    ->get()->result();
  }

  public function assign_period_template_to_class($data){
    return $this->db->where("id",$data["classId"])
    ->update("class",["ttv2_template_id_for_lms_check_in"=>$data["periodTemplateId"]]);
  }

  public function acad_years_for_subject_clone(){
    return $this->db_readonly->select("ay.id, ay.acad_year")
    ->from("academic_year ay")
    ->join("lp_subjects lps","lps.acad_year_id=ay.id")
    ->where("ay.id<=", $this->yearId)
    ->group_by("ay.id")
    ->get()->result();
  }

  public function getClassesForLmsClone($data){
    $acadYearId=$data["acadYearId"];

    $is_staff_access_control_enabled=$this->settings->getSetting("enable_staff_class_subject_access_control_for_lms");
    $is_lms_admin=$this->authorization->isAuthorized('LESSON_PLAN.ADMIN');
    
    if($is_staff_access_control_enabled==1 && !$is_lms_admin){
      $class_section_and_subject_lists=$this->get_class_section_and_subject_ids_for_lms_access_control();
      $class_section_ids=$class_section_and_subject_lists["class_section_list"];

      return $this->db_readonly->select('c.class_name, c.class_master_id, c.id as class_id')
      ->from("class c")
      ->join("class_section cs","cs.class_id=c.id")
      ->where("c.acad_year_id", $acadYearId)
      ->where_in("cs.id", $class_section_ids)
      ->group_by("c.id")
      ->get()->result();
    }else{
      return $this->db_readonly->select('c.class_name, c.class_master_id, c.id as class_id')
      ->from("class c")
      ->join("class_section cs","cs.class_id=c.id")
      ->where("c.acad_year_id", $acadYearId)
      ->group_by("c.id")
      ->get()->result();
    }
  }

  public function callGetSubjectsForClone($data){
    $acadYearId = $data["acadYearId"];

    $class_master_id = $data['class_master_id'];

    if(!empty($data['semester_main_screen_id'])){
      $semester_main_screen_id = $data['semester_main_screen_id'];
    }else{
      $semester_main_screen_id=0;
    }
     
    $this->db_readonly->distinct()->select('s.*, s.id as subject_id,count(l.id) as TotalLessons, sem.sem_name')
      ->from('lp_subjects s')
      ->join('lp_lessons l','l.lp_subject_id=s.id','left')
      ->join('semester sem', 'sem.id=s.semester_id', 'left')
      ->join('subject_master sm', 'sm.id=s.subject_master_id')
      ->where('s.acad_year_id', $acadYearId)
      ->where('s.class_master_id',$class_master_id);
 
    if ($this->settings->getSetting('is_semester_scheme')) {
      $this->db_readonly->where('s.semester_id',$semester_main_screen_id);
    }

    $result = $this->db_readonly->group_by('s.id')
     ->get()->result();

      // Getting staff details if assigned
    foreach($result as $key => $value) {
      $a= $this->db_readonly->select('concat( ifnull(sm.first_name, ""), " ", ifnull(sm.last_name, "") ) as staff_name, ifnull(ls3.staff_id, "") as staff_id')
            ->from('staff_master sm')
            ->join('lp_subjects_section_staff ls3', 'ls3.staff_id= sm.id', 'left')
            ->where('ls3.staff_type', 'primary')
            ->where('ls3.lp_subjects_id', $value->id)
            ->get();

      $b= $this->db_readonly->select('concat( ifnull(sm.first_name, ""), " ", ifnull(sm.last_name, "") ) as assistant_staff_name, ifnull(ls3.staff_id, "") as ass_staff_id')
            ->from('staff_master sm')
            ->join('lp_subjects_section_staff ls3', 'ls3.staff_id= sm.id', 'left')
            ->where('ls3.staff_type', 'assistant')
            ->where('ls3.lp_subjects_id', $value->id)
            ->get();

            if($a->num_rows() == 1) {
              $result[$key]->staff_name= $a->row()->staff_name;
            } else {
              $result[$key]->staff_name= 'Not Assigned';
            }
            if($b->num_rows() == 1) {
              $result[$key]->assistant_staff_name= $b->row()->assistant_staff_name;
            } else {
              $result[$key]->assistant_staff_name= 'Not Assigned';
            }

    }
    return $result;
  }
}
?>
