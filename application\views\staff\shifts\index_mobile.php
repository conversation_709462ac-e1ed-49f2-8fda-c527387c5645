<div class="col-md-12">
  <div class="card panel_new_style">
    <div class="card-header panel_heading_new_style_padding" style="padding-top: 10px;">
      <div class="row d-flex align-items-center">
        <div class="col-xs-9 p-0">
          <h3 class="card-title panel_title_new_style mb-0">
            <strong>Manage Shifts</strong>
          </h3>
        </div>

        <div class="col-xs-3 p-0">
          <div class="circleButton_noBackColor" style="background-color:#fe970a;float:right;">
            <a class="control-primary" onclick="addShift()" style="cursor:pointer;" title="Add new shift" aria-label="Add new shift">
              <span class="fa fa-plus backgroundColor_organge" style="font-size:19px"></span>
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="card-body px-2 py-1">
      <?php if (empty($shifts)) {
        echo '<div style="color:red;text-align:center;
        color: black;
        border: 2px solid #fffafa;
        text-align: center;
        border-radius: 6px;
        position: relative;
        padding: 10px;
        font-size: 14px;
        margin-top: 14px;
        background: #ebf3ff;">
        Oops! No shifts have been created yet.
        </div>';
      } else {
        foreach ($shifts as $key => $shift) {
          echo '<div class="col-md-12 p-0 mb-3">';
          echo '<div class="shift">';
          echo '<h5 class="card-title" style="display: flex;justify-content: space-between;display: flex;justify-content: space-between;font-size: 1.1rem;font-weight: 600;margin-bottom: 10px;word-break: break-word;padding-right: 51px;"><strong>' . $shift->name . '</strong></h5>';

          if ($this->authorization->isSuperAdmin()) {
            $checked = $shift->is_active == 1 ? 'checked' : '';
            echo '<span style="position:absolute;top: 8px;right: 8px;">
              <label class="switch">
                  <input type="checkbox" name="switch" ' . $checked . ' onclick="handleShiftToggle(this, ' . $shift->id . ', \'' . addslashes($shift->name) . '\', ' . ($shift->is_active == 1 ? '0' : '1') . ')">
                  <span></span>
              </label>
            </span>';
          }

          if ($shift->shift_type == 1) {
            echo '<span>' . $shift->start_time . ' to ' . $shift->end_time . '</span>';
          } else if ($shift->start_time) {
            echo '<span class="text-danger">Non-Working <br>
            ' . $shift->start_time . ' to ' . $shift->end_time . '</span>';
          } else {
            echo '<span class="text-danger">Non-Working</span>';
          }
          echo '</div>';
          echo '</div>';
        }
      }
      ?>

      <div id="staff-shifts">

      </div>
    </div>
  </div>
</div>

<a href="<?php echo site_url('staff/attendance'); ?>" id="backBtn" onclick="loader()"><span
    class="fa fa-mail-reply"></span></a>

<style type="text/css">
  .swal2-actions {
    z-index: 0 !important;
  }

  .shift {
    border-radius: .75rem;
    border: solid 1px #ccc;
    padding: 10px 15px;
  }
</style>


<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js"
  integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous"></script>
<script type="text/javascript">
  var week_days = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
  var staffShifts = [];
  var shifts = [];
  $(document).ready(function () {
    // getStaffShifts();
    shifts = JSON.parse('<?php echo json_encode($shifts) ?>');
  });

  function handleShiftToggle(checkbox, shiftId, shiftName, action) {
    // Prevent immediate change
    Swal.fire({
      title: "Are you sure?",
      text: `Do you want to ${action == 0 ? "disable" : "enable"} the shift "${shiftName}"?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, proceed",
      cancelButtonText: "Cancel"
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: "<?php echo site_url('staff/shifts/delete_staff_shift'); ?>",
          type: "POST",
          data: {
            shiftId,
            action
          },
          success: function (data) {
            if (data == -1) {
              Swal.fire({
                title: "Shift is in use",
                text: "Please un-assign staff shifts before disabling it.",
                icon: "error",
              });
            } else {
              // Only update checkbox state if success
              checkbox.checked = !!action;
              Swal.fire({
                title: "Success",
                text: "Shift status updated.",
                icon: "success",
              }).then(() => {
                window.location.reload();
              });
            }
          }
        });
      } else {
        // Restore checkbox to previous state if cancelled
        checkbox.checked = !checkbox.checked;
      }
    });
  }

  // function toggleShiftTimings() {
  //   var shift_type = $("#shift_type").val();
  //   if (shift_type == 1) {
  //     $(".shift-timings").show();
  //   } else {
  //     $(".shift-timings").hide();
  //   }
  // }

  function addShift() {
    var html = '<table class="table table-bordered text-left">';
    html += '<tr><th>Name <span style="color:red;">*</span></th><td><input type="text" id="shift_name" class="form-control" placeholder="Enter shift name"></td></tr>';
    // html += '<tr><th>Working Shift?</th><td>';
    // html += '<label class="switch">';
    // html += '<input name="shift_type" onchange="toggleShiftTimings()" id="shift_type" type="checkbox" checked/>';
    // html += '<span></span>';
    // html += '</label>';
    // html += '</td></tr>';
    html += '<tr><th>Shift Type <span style="color:red;">*</span></th><td>';
    html += '<select id="shift_type" class="form-control" onchange="toggleShiftTimingsInputs()">';
    html += '<option value="1">Working Shift</option>';
    html += '<option value="2">Week-Off</option>';
    html += '<option value="3">Holiday</option>';
    html += '</select>';
    html += '</td></tr>';
    html += '<tr class="shift-timings"><th>Start Time <span style="color:red;">*</span></th><td><input autocomplete="off" type="text" id="start_time" class="form-control" placeholder="Enter shift start time"></td></tr>';
    html += '<tr class="shift-timings"><th>End Time <span style="color:red;">*</span></th><td><input autocomplete="off" type="text" id="end_time" class="form-control" placeholder="Enter shift end time"></td></tr>';
    html += '<tr><th>Does shift span to next day?</th><td>';
    html += '<label class="switch">';
    html += '<input name="is_shift_over_midnight" id="is_shift_over_midnight" type="checkbox"/>';
    html += '<span></span>';
    html += '</label>';
    html += '</td></tr>';
    html += '</table>';
    Swal.fire({
      title: 'Add New Shift',
      html: html,
      confirmButtonText: 'Save',
      showCancelButton: true,
      showLoaderOnConfirm: true,
      allowOutsideClick: false,
      didOpen: function () {
        $('#start_time, #end_time').datetimepicker({
          format: 'hh:mm A'
        });
        // Initial toggle based on default value
        toggleShiftTimingsInputs();
        // Attach again in case of dynamic content
        $('#shift_type').on('change', toggleShiftTimingsInputs);
      },
      preConfirm: () => {
        var name = $("#shift_name").val().trim();
        var start_time = $("#start_time").val();
        var end_time = $("#end_time").val();
        var is_shift_over_midnight = ($("#is_shift_over_midnight").is(':checked')) ? 1 : 0;
        var shift_type = $("#shift_type").val();

        // Validation: Name required, max 50 chars, no special chars except space, dash, underscore
        if (name === '') {
          Swal.showValidationMessage('Enter the shift name');
          return false;
        }
        if (name.length > 50) {
          Swal.showValidationMessage('Shift name must be at most 50 characters');
          return false;
        }
        if (!/^[a-zA-Z0-9 _\-]+$/.test(name)) {
          Swal.showValidationMessage('Shift name can only contain letters, numbers, spaces, dash, and underscore');
          return false;
        }
        // Validation: Working shift must have start/end time
        if (shift_type == 1) {
          if (start_time === '' || end_time === '') {
            Swal.showValidationMessage('Enter both start and end time for working shift');
            return false;
          }
          // Compare start and end time (assume format hh:mm AM/PM)
          var st = moment(start_time, 'hh:mm A');
          var et = moment(end_time, 'hh:mm A');
          if (!st.isValid() || !et.isValid()) {
            Swal.showValidationMessage('Invalid time format');
            return false;
          }
          // Fix: Use isSameOrAfter only if available, else fallback to comparison
          // moment.js <2.29.0 does not have isSameOrAfter for time, so use .diff()
          if (!is_shift_over_midnight && st.diff(et) >= 0) {
            Swal.showValidationMessage('Start time must be less than end time');
            return false;
          }
        }
        // Validation: For Week-Off/Holiday, no times required
        if (shift_type != 1 && (start_time !== '' || end_time !== '')) {
          Swal.showValidationMessage('Week-Off/Holiday should not have start/end time');
          return false;
        }
        var data = {
          'name': name,
          'shift_type': shift_type,
          'start_time': start_time,
          'end_time': end_time,
          'is_shift_over_midnight': is_shift_over_midnight
        };
        saveShift(data);
      }
    });
  }

  // Disable/enable start/end time inputs based on shift type
  function toggleShiftTimingsInputs() {
    var shiftType = $('#shift_type').val();
    if (shiftType == 1) {
      $('#start_time').prop('disabled', false);
      $('#end_time').prop('disabled', false);
    } else {
      $('#start_time').prop('disabled', true).val('');
      $('#end_time').prop('disabled', true).val('');
    }
  }

  function saveShift(input) {
    $.ajax({
      url: '<?php echo site_url('staff/shifts/saveShift'); ?>',
      type: 'post',
      data: input,
      success: function (data) {
        if (parseInt(data)) {
          Swal.fire({
            title: "Successful",
            text: "Shift created successfully.",
            icon: "success",
          }).then(e => {
            location.reload();
          })
        } else {
          Swal.fire({
            title: "Error",
            text: "Unable to save shift data",
            icon: "error",
          });
        }
      }
    });
  }

  function getStaffShifts() {
    $.ajax({
      url: '<?php echo site_url('staff/shifts/getStaffShifts'); ?>',
      type: 'post',
      data: {},
      success: function (data) {
        var staff_shifts = JSON.parse(data);
        // console.log(staff_shifts);
        var html = '<table class="table table-bordered">';
        html += '<thead><tr><th>#</th><th>Staff Name</th>';
        for (var d in week_days) {
          html += '<th>' + week_days[d] + '</th>';
        }
        html += '<th>Action</th>';
        html += '</tr></thead>';
        html += '<tbody>';
        for (var i = 0; i < staff_shifts.length; i++) {
          html += '<tr id="staff_' + staff_shifts[i].staff_id + '">';
          html += '<td>' + (i + 1) + '</td>';
          html += constructRow(staff_shifts[i]);
          html += '</tr>';
        }
        html += '</tbody>';
        html += '</table>';
        $("#staff-shifts").html(html);
      }
    });
  }

  function constructRow(staff_shift) {
    var staff_id = staff_shift.staff_id;
    staffShifts[staff_id] = staff_shift;
    var html = '<td>' + staff_shift.staff_name;
    if (staff_shift.designation) {
      html += '<br><span class="text-muted">' + staff_shift.designation + '</span>';
    }
    html += '</td>';
    for (var d in week_days) {
      if (week_days[d] in staff_shift) {
        html += '<td>' + staff_shift[week_days[d]].shift_name + '</td>';
      } else {
        html += '<td>-</td>';
      }
    }
    html += '<td><button onclick="assignShifts(' + staff_id + ')" class="btn btn-primary">Assign/Edit</button></td>';
    return html;
  }

  function assignShifts(staff_id) {
    var staff_shift = staffShifts[staff_id];
    var html = '<input type="hidden" id="staff_id" value="' + staff_id + '">';
    html += '<table class="table table-bordered text-left">';
    for (var d in week_days) {
      var day = week_days[d];
      html += '<tr>';
      html += '<th>' + day + '</th>';
      html += '<th>';
      html += '<select class="form-control" id="' + day + '">';
      html += '<option value="0">--- Choose shift ---</option>';
      for (var i in shifts) {
        html += '<option ' + ((day in staff_shift && staff_shift[day].shift_id == shifts[i].id) ? 'selected' : '') + ' value="' + shifts[i].id + '">' + shifts[i].name + '</option>';
      }
      html += '</th>';
      html += '</tr>';
    }
    html += '</table>';
    Swal.fire({
      title: staff_shift.staff_name,
      html: html,
      confirmButtonText: 'Save',
      showCancelButton: true,
      showLoaderOnConfirm: true,
      allowOutsideClick: false,
      onOpen: function () {
        $('#start_time, #end_time').datetimepicker({
          format: 'hh:mm A'
        });
      },
      preConfirm: () => {
        var staff_id = $("#staff_id").val();
        var day_shifts = {};
        for (var d in week_days) {
          var day = week_days[d];
          day_shifts[day] = {
            'shift_id': $("#" + day).val(),
            'shift_name': (($("#" + day).val() != '0') ? $("#" + day + " option:selected").text() : '-')
          };
        }
        var data = {
          'staff_id': staff_id,
          'shifts': day_shifts
        };
        saveStaffShift(data);
      }
    });
  }

  function saveStaffShift(input) {
    $.ajax({
      url: '<?php echo site_url('staff/shifts/saveStaffShift'); ?>',
      type: 'post',
      data: input,
      success: function (data) {
        if (parseInt(data)) {
          // Swal.fire({
          //   title: "Successful",
          //   text: "Assigned shift data successfully",
          //   icon: "success",
          // });
          var staff_shift = staffShifts[input.staff_id];
          var input_shifts = input.shifts;
          for (var day in input_shifts) {
            staff_shift[day] = input_shifts[day];
          }
          staffShifts[input.staff_id] = staff_shift;
        } else {
          Swal.fire({
            title: "Error",
            text: "Unable to assign shift data",
            icon: "error",
          });
        }
        var index = $("#staff_" + input.staff_id + " td:first").html();
        var html = '<td>' + index + '</td>';
        html += constructRow(staffShifts[input.staff_id]);
        $("#staff_" + input.staff_id).html(html);
      }
    });
  }
</script>