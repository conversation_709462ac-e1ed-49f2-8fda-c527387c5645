<?php
// Test script to check entity data structure
// This is a temporary file to debug the ID card entity data

// Include CodeIgniter bootstrap
require_once 'index.php';

// Get CI instance
$CI =& get_instance();

// Load necessary models
$CI->load->model('student/Student_Model');

// Test student ID (replace with actual student ID)
$student_id = 1; // Change this to a valid student ID

echo "<h2>Testing ID Card Entity Data</h2>";
echo "<h3>Student ID: $student_id</h3>";

// Test the get_idcard_entity_data method
echo "<h4>Entity Data:</h4>";
$entity_data = $CI->Student_Model->get_idcard_entity_data($student_id);

if ($entity_data) {
    echo "<pre>";
    print_r($entity_data);
    echo "</pre>";
} else {
    echo "<p>No entity data found for student ID: $student_id</p>";
}

// Test database structure
echo "<h4>Database Table Structure:</h4>";
$query = $CI->db->query("DESCRIBE idcard_template_order_entities");
$columns = $query->result();

echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
foreach ($columns as $column) {
    echo "<tr>";
    echo "<td>{$column->Field}</td>";
    echo "<td>{$column->Type}</td>";
    echo "<td>{$column->Null}</td>";
    echo "<td>{$column->Key}</td>";
    echo "<td>{$column->Default}</td>";
    echo "<td>{$column->Extra}</td>";
    echo "</tr>";
}
echo "</table>";

// Test sample data from the table
echo "<h4>Sample Data from idcard_template_order_entities:</h4>";
$sample_query = $CI->db->limit(5)->get('idcard_template_order_entities');
$sample_data = $sample_query->result();

if ($sample_data) {
    echo "<pre>";
    print_r($sample_data);
    echo "</pre>";
} else {
    echo "<p>No sample data found in idcard_template_order_entities table</p>";
}

?>
