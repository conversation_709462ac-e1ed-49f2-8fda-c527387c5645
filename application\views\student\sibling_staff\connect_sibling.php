<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/student_menu');?>">Student Menu</a></li>
    <li><a href="<?php echo site_url('student/Student_controller/index/');?>">Student Index</a></li>
    <li><a href="<?php echo site_url('student/Student_controller/addMoreStudentInfo/'.$student_uid);?>">Student Detail</a></li>
    <li>Connect Sibling and Staff</li>
</ul>
<hr>
<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="d-flex justify-content-between" style="width:100%;">
                  	<h3 class="card-title panel_title_new_style_staff">
                    <a class="back_anchor" href="<?php echo site_url('student/Student_controller/addMoreStudentInfo/'.$student_uid);?>">
                      <span class="fa fa-arrow-left"></span>
                    </a> 
                    Connect Staff and Siblings Details of <?php echo '<strong>' . strtoupper($stdData->stdName) . '</strong> (Class/Sections ' . $stdData->classSection .') (Admission No: '. $stdData->admission_no.')'?>
                  	</h3> 
					<ul class="panel-controls" style="float:right">
						<a target="_blank" href="<?php echo site_url('student/sibling_staff_controller/viewLinkAccounts');?>" class="btn btn-success">View Links</a>
					</ul>
               </div>
             </div>
          	<div class="card-body mt-1">
          		<p>
				To connect siblings -<br>
				1. Select the siblings one-by-one in the 'Select Students' box and click 'Add'. The siblings gets added to the 'Selected Staff/Siblings' box.<br>
				2. Select one of them as the primary account. This will be the user-login they will use to login. Click 'Link Accounts'.
				</p>
				<p>
				To connect Staff too, select Staff and if s(he) is a father/mother and click 'Add'; before linking the accounts.
				</p>
          	</div>
     	</div>

		<div class="card-body">
			<form class="form-horizontal">
				<div class="form-group">
					<label class="col-md-2 col-xs-12 control-label" for="class_section">Class/Section</label>
					<div class="col-md-8 col-xs-12">
						<select class="form-control" name="class_section[]" id="class_section">
							<option value="0">Select Class/Section</option>
							<?php
							foreach ($classSections as $cl) {
								$option = '<option value="' . $cl->classId . '_' . $cl->sectionId . '"';
								$option .= '>' . $cl->className .  $cl->sectionName .'</option>';
								echo $option;
							}
							?>
						</select>
					</div>
				</div>

				<div class="form-group">
					<label class="col-md-2 col-xs-12 control-label">Student</label>
					<div class="col-md-8 col-xs-12 text-left"> 
						<select class="form-control" name="student" id="student">
							<option value="0">Select Student</option>
						</select>
					</div>
				</div>
			</form>
			<center>
				<button type="button" class="btn btn-primary mt-3 mb-4" onclick="addUsers()">Add Student</button>
			</center>
	
			<form class="form-horizontal">
				<div class="form-group">
					<label class="col-md-2 control-label text-right" for="staff">Staff</label>
					<div class="col-md-8">                                                         
						<select class="form-control" name="staff" id="staff">
							<option value="0">Select Staff</option>
							<?php
							foreach ($staffList as $st) {
								$option = '<option value="' . $st->id . '"';
								$option .= '>' . $st->staffName .'</option>';
								echo $option;
							}
							?>
						</select>
					</div>
				</div>
			</form>
			<center>
				<button id="addStaffBtn" type="button" class="btn btn-primary  mt-3" onclick="addUsers()">Add Staff</button>
			</center>
		</div>

		<div class="card-body mt-3" style="overflow:auto;">
			<table id="studentTable" class="table" style="display: none;">
				<thead>
					<th>Name</th>
					<th>Father (username)</th>
					<th>Mother (username)</th>
					<th>Status</th>
					<th>Select Primary Account</th>
				</thead>
				<tbody id="tab">				
				</tbody>
			</table>

			<table id="staffTable" class="table" style="display: none;">
				<thead>
					<th>Name</th>
					<th>Type</th>
					<th>Username</th>
					<th>Status</th>
					<th>Select Primary Account</th>
				</thead>
				<tbody id="stafftab">				
				</tbody>
			</table>
			<hr>
			<center class="">
				<button type="button" class="btn btn-primary" onclick="connectAccounts()">Link</button>
				<a class="btn btn-danger" href="<?php echo site_url('student/Student_controller/addMoreStudentInfo/'.$student_uid);?>">Cancel</a>
			</center>
		</div>
 	</div>
 </div>

<div class="visible-xs visible-sm">
  <a href="<?php echo site_url('student/student_menu');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<script type="text/javascript">
	var m_avatar_ids = [];
	var f_avatar_ids = [];
	var m_user_ids = [];
	var f_user_ids = [];
	var student_ids = [];
	var staff_avatar_id = '';
	var staff_user_id = '';
	var staff_id = '';
	var staff_gender = '';
	$(document).ready(function(){
		$("#class_section").change(function(){
			var class_section = $(this).val();
			$.ajax({
				url: "<?php echo site_url('student/sibling_staff_controller/getStudents');?>",
				data: {'class_section':class_section},
				type: 'post',
				success: function(data) {
					var stdData = JSON.parse(data);
					var option = '<option value="0">Select Student</option>';
					for(var i=0; i<stdData.length;i++){
						option += `<option id="student_option_id_${stdData[i].id}" value='${stdData[i].id}'>${stdData[i].stdName} ${(stdData[i].admission_status==4 || stdData[i].admission_status==5) && "[Alumni]" || ""}</option>`;
					}
					$("#student").html(option);
					// Hide students who alredy linked
					find_linked_student_ids(section_id);
				},
				error: function(err) {
						console.log(err);
				}
			});
		});
	});

	function find_linked_student_ids(section_id) {
		$.ajax({

			url: '<?php echo site_url('student/sibling_staff_controller/find_linked_student_ids'); ?>',
			type: "post",
			data: {'section_id': section_id},
			success: function(data) {
				var p_data = JSON.parse(data);
				for( var v of p_data) {
					$(`#student_option_id_${v.id}`).append(`<font>(Linked)</font>`).css('color', 'lightgray').attr('disabled', true);
				}

			}

		});
	}
	
	$(document).ready(function(){
		var staffComboValue = '';
		var studentComboValue = '<?php echo $student_uid ?>';
		$.ajax({
			url: "<?php echo site_url('student/sibling_staff_controller/getAccounts');?>",
			data: {'staff':staffComboValue, 'student':studentComboValue},
			type: 'post',
			success: function(data) {
				var data = JSON.parse(data);
				var staff = data.staff;
				var student = data.student;
				if(staff.length != 0){
					$("#staffTable").show();
					$("#addStaffBtn").attr('disabled', true);
					var tab1 = '';
					var status = 'Active';
					if(staff.active == 0)
						status = 'Inactive';
					staff_user_id = staff.userId;
					staff_avatar_id = staff.avatarId;
					staff_id = staff.staffId;
					staff_gender = staff.gender;
					var type = 'Father';
					if(staff.gender == 'F')
						type = 'Mother';

					tab1 += '<tr><td>'+staff.name+'</td><td>Staff('+type+')</td><td>'+staff.username+'</td><td>'+status+'</td><td>Not needed</td></tr>';
					$("#stafftab").append(tab1);
				}
				if(student.length != 0){
					var tab2 = '';
					$("#studentTable").show();
					// for(var i=0; i<student.length;i++){
						f_avatar_ids.push(student.f_avatar_id);
						m_avatar_ids.push(student.m_avatar_id);
						m_user_ids.push(student.u_moth_id);
						f_user_ids.push(student.u_fath_id);
						student_ids.push(student.std_id);
						var f_status = 'Active';
						if(student.u_fath_active == 0)
							f_status = 'Inactive';
							var m_status = 'Active';
						if(student.u_moth_active == 0)
							m_status = 'Inactive';
						tab2 += '<tr><td>'+student.name+' ('+student.csName+')'+'</td><td>'+student.father_name + '(' + student.u_fath_username +')</td><td>'+student.mother_name + '(' + student.u_moth_username +')</td><td>Father:'  + f_status + ', Mother:' + m_status + '</td><td><input type="radio" class="studentPrimary" name="primary" value="'+student.u_moth_id+'_'+student.u_fath_id+'"></td></tr>';
					// }
					$("#tab").append(tab2);
				}
				// if(staff_user_id != '') {
				// 	$(".studentPrimary").attr('disabled', true);
				// }
			},
			error: function(err) {
				console.log(err);
			}
		});
	});

	function addUsers(){
		var staffComboValue = $("#staff").val();
		var studentComboValue = $("#student").val();
		$("#student").val(0);
		if(student_ids.includes(studentComboValue)) {
			bootbox.alert({
				title: "Users",
				message: "Selected student is already added",
				buttons: {
					ok: {
						label: 'Got it',
						className: 'btn-success'
					}
				}
			});

		} else {
			// var relationComboValue = $('input[name="relation"]:checked').val();
			$("#staff").val('0');
			$("#student").val('0');
			$("#class_section").val('0');
			$.ajax({
				url: "<?php echo site_url('student/sibling_staff_controller/getAccounts');?>",
				data: {'staff':staffComboValue, 'student':studentComboValue},
				type: 'post',
				success: function(data) {
					var data = JSON.parse(data);
					var staff = data.staff;
					var student = data.student;
					if(staff.length != 0){
						$("#staffTable").show();
						$("#addStaffBtn").attr('disabled', true);
						var tab1 = '';
						var status = 'Active';
						if(staff.active == 0)
							status = 'Inactive';
						staff_user_id = staff.userId;
						staff_avatar_id = staff.avatarId;
						staff_id = staff.staffId;
						staff_gender = staff.gender;
						var type = 'Father';
						if(staff.gender == 'F')
							type = 'Mother';

						tab1 += '<tr><td>'+staff.name+'</td><td>Staff('+type+')</td><td>'+staff.username+'</td><td>'+status+'</td><td>Not needed</td></tr>';
						$("#stafftab").append(tab1);
					}
					if(student.length != 0){
						var tab2 = '';
						$("#studentTable").show();
						// for(var i=0; i<student.length;i++){
							f_avatar_ids.push(student.f_avatar_id);
							m_avatar_ids.push(student.m_avatar_id);
							m_user_ids.push(student.u_moth_id);
							f_user_ids.push(student.u_fath_id);
							student_ids.push(student.std_id);
							var f_status = 'Active';
							if(student.u_fath_active == 0)
								f_status = 'Inactive';
								var m_status = 'Active';
							if(student.u_moth_active == 0)
								m_status = 'Inactive';
							tab2 += '<tr><td>'+student.name+' ('+student.csName+')'+'</td><td>'+student.father_name + '(' + student.u_fath_username +')</td><td>'+student.mother_name + '(' + student.u_moth_username +')</td><td>Father:'  + f_status + ', Mother:' + m_status + '</td><td><input type="radio" class="studentPrimary" name="primary" value="'+student.u_moth_id+'_'+student.u_fath_id+'"></td></tr>';
						// }
						$("#tab").append(tab2);
					}
					// if(staff_user_id != '') {
					// 	$(".studentPrimary").attr('disabled', true);
					// }
				},
				error: function(err) {
					console.log(err);
				}
			});
		}
		
	}

	function connectAccounts(){
		var user_id = $('input[name="primary"]:checked').val();
		if((student_ids.length === 1 && staff_id === '') || student_ids.length === 0) {
			bootbox.alert({
				title: "Link Accounts",
				message: "Select atleast two users.",
				buttons: {
					ok: {
						label: 'Got it',
						className: 'btn-success'
					}
				}
			});
			return false;
		}
		if(user_id == null && student_ids.length > 1) {
			bootbox.alert({
				title: "Link Accounts",
				message: "Please select a primary account.",
				buttons: {
					ok: {
						label: 'Got it',
						className: 'btn-success'
					}
				}
			});
		}
		else {
			$.ajax({
	            url: "<?php echo site_url('student/sibling_staff_controller/linkAccounts');?>",
	            data: {'m_user_ids':m_user_ids, 'm_avatar_ids':m_avatar_ids, 'f_user_ids': f_user_ids, 'f_avatar_ids':f_avatar_ids, 'student_ids':student_ids, 'staff_user_id': staff_user_id, 'staff_avatar_id':staff_avatar_id, 'staff_id':staff_id, 'staff_gender':staff_gender, 'primary_user_ids': user_id},
	            type: 'post',
	            success: function(data) {
	            	console.log(data);
	            	if(data) {
	            		$(function(){
				          new PNotify({
				              title: 'Success',
				              text: 'Accounts Linked Successfully',
				              type: 'success',
				          });
				        });
	            		$("#tab").html('<tr><td>Accounts Linked Successfully</td></tr>');
	            		$("#staffTable").hide();
	            	} else {
	            		bootbox.alert({
				            title: "Link Accounts",
				            message: "Something went wrong. Unable to link accounts.",
				            buttons: {
				                ok: {
				                    label: 'OK',
				                    className: 'btn-warning'
				                }
				            }
				        });
	            	}
					location.reload();
	            },
	            error: function(err) {
	                console.log(err);
	            }
	        });
		}
	}
</script>

<style type="text/css">
	table thead th{
		width:20%;
	}
	@media only screen and (max-width: 768px){
		input[type=radio], input[type=checkbox] {
	    	margin: 1px 14px -6px;
	}}
</style>