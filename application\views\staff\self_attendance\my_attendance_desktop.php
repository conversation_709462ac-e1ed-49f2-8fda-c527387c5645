<ul class="breadcrumb">
  <li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
  <li><a href="<?php echo site_url('staff/attendance'); ?>">Staff Attendance</a></li>
  <li>My Attendance</li>
</ul>

<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border">
      <div class="row" style="margin: 0px;">
        <div class="d-flex justify-content-between" style="width:100%;">
          <h3 class="card-title panel_title_new_style_staff">
            <a class="back_anchor" href="<?php echo site_url('staff/attendance'); ?>">
              <span class="fa fa-arrow-left"></span>
            </a>
            My Attendance
          </h3>
          <strong class="pull-right"></strong>
        </div>
      </div>
    </div>
    <div class="card-body pt-1">
      <input type="hidden" name="staff-shift-id" id="staff-shift-id" value="<?php echo !empty($shifts) ? $shifts[0]->staff_shift_id : 0; ?>">
      <div class="">
        <?php if (!$checkin_disabled) { ?>
          <div class="">
            <h3>Mark Attendance</h3>
            <div id="location"></div>
            <input type="hidden" id="is_location_captured">
            <input type="hidden" id="location_capture_error">
            <input type="hidden" id="latitude">
            <input type="hidden" id="longitude">
            <input type="hidden" id="location_id">
            <input type="hidden" id="is_outside">
            <input type="hidden" id="distance">
            <input type="hidden" id="isStaffLate">
            <input type="hidden" id="check_in_out_location_name">
            <?php if (empty($shifts)) {
              echo '<div style="color:red;text-align:center;
              color: black;
              border: 2px solid #fffafa;
              text-align: center;
              border-radius: 6px;
              position: relative;
              padding: 10px;
              font-size: 14px;
              margin-top: 14px;
              background: #ebf3ff;">
                Shift is not allocated for the day.
              </div>';
            } else {
              $shift = $shifts[0]; ?>
              
              <div class="att-card" style="width: 50%;">
                <div style="display: flex;justify-content: space-between;">
                  <h4>Shift Timings</h4>
                  <h4>
                    <?php echo date('d-m-Y'); ?>
                  </h4>
                </div>
                <div class="d-flex justify-content-between">
                  <span>Start Time:
                    <?php echo date('h:i A', strtotime($shift->start_time)) ?>
                  </span>
                  <span>To</span>
                  <span>End Time:
                    <?php echo date('h:i A', strtotime($shift->end_time)) ?>
                  </span>
                </div>
              </div>

              <div>
                <?php echo '<input type="hidden" id="shift_start_time_' . $shift->staff_shift_id . '" value="' . $shift->start_time . '" />
                          <input type="hidden" id="shift_date_' . $shift->staff_shift_id . '" value="' . $shift->date . '" />
                          <input type="hidden" id="shift_end_time_' . $shift->staff_shift_id . '" value="' . $shift->end_time . '" />
                          <input type="hidden" id="shift_minute_diff_' . $shift->staff_shift_id . '" value="' . $shift->minute_diff . '" />'; ?>
                <div class="transactions"></div>
                <div id="duration"></div>
              </div>
            <?php } ?>
          </div>
        <?php } else{ ?>
          <div class="transactions"></div>
          <div id="duration"></div>
        <?php } ?>
        <div class="">
          <div class="d-flex justify-content-between">
            <h3>Previous Attendance</h3>
            <div id="loader" class="loaderclass" style="display:none;"></div>
          </div>
          <select style="width: 18%;margin-bottom: 10px" onchange="change_month()" class="select form-control" id="month_list"
            name="month_list">
            <?php
            $current_month = date('F', strtotime(date('d-m-Y')));
            foreach ($months as $key => $type) { ?>
              <option value="<?php echo $key ?>" <?php echo ($current_month == $type) ? 'selected' : ''; ?>><?php echo $type ?></option>
            <?php } ?>
          </select>
          <div class="staffAttDetails">
          </div>
            <div id="showAttStatistics" style="display: none;">
            </div>
            <div id="attendance-data" style="display: none;" class="table-responsive" style="margin-bottom: 10px;">
              
            </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div id="info-modal" data-backdrop="static" data-keyboard="false" class="modal fade" role="dialog" style="top:2%;">
  <div class="modal-dialog" style="width: 40%;margin:auto;top:15%">
    <div class="modal-content" style="border-radius: 8px;">
      <div class="modal-header">
        <h4 class="modal-title" id="modalHeader">Attendance Confirmation</h4>
        <h4>Timeout in <span style="color:red;font-weight: 600;" id="att_timer">60</span>s</h4>
      </div>
      <div class="modal-body form-horizontal">
        <div id="location-data">

        </div>
      </div>
      <div class="modal-footer" id="info-footer">

      </div>
    </div>
  </div>
</div>

<style type="text/css">
  .att-card {
    border-radius: 8px;
    padding: 12px 20px 12px 12px !important;
    background-color: #f5f5f5;
    margin-bottom: 1.2rem;
  }

  .unread_box_no_style_new {
    min-height: 4.6rem;
    border-radius: 8px;
    padding: 12px 20px 0px 12px !important;
    background-color: #f5f5f5;
    margin-bottom: 1.2rem;
  }

  .card-body {
    padding: 0 1.25rem;
  }

  .btn-success {
    border-radius: 8px;
  }

  .absent {
    color: #fa0000 !important;
    font-weight: bold;
    /*color: white !important;*/
  }

  .weekoff,
  .holiday {
    color: #ffb203 !important;
    font-weight: bold;
    /*color: white !important;*/
  }

  .halfday,
  .present {
    color: green !important;
    font-weight: bold;
    /*color: white !important;*/
  }

  .onleave {
    color: #ff8201 !important;
    font-weight: bold;
    /*color: white !important;*/
  }

  .upper-row {
    text-align: center;
    padding-top: 10px;
  }

  .upper-row>h4 {
    font-weight: 500;
    font-size: 20px;
  }

  .lower-row {
    display: flex;
    justify-content: space-evenly;
    margin-bottom: 10px;
  }

  .transactions,
  .extra {
    margin: 2rem auto;
  }

  .transactions {
    display: flex;
  }

  .transaction {
    cursor: default;
    padding: 10px 20px;
    display: flex;
    cursor: pointer;
    /*justify-content: space-between;*/
    align-items: center;
    background-color: white;
    box-shadow: 0 14px 28px rgba(0, 0, 0, 0.02), 0 10px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    margin-bottom: 20px;
  }

  .check-icon {
    font-size: 2rem;
    align-self: center;
    width: 12%;
  }

  .check-text {
    width: 88%;
  }

  .check-icon>span {
    font-weight: 500;
  }

  .check-icon.in {
    color: #60d360;
  }

  .check-icon.out {
    color: #ce6565;
  }

  .Check-in {
    background-color: #60d360;
    /* color: white; */
  }

  .Check-out {
    background-color: #ce6565;
    /* color: white; */
  }

  .Check-in>.check-icon,
  .Check-out>.check-icon {
    color: white;
  }

  .check-title {
    font-size: 1.2rem;
    font-weight: 600;
  }

  .Check-in,
  .Check-out {
    color: white;
    width: 100%;
    ;
  }

  .drawer {
    display: flex;
    background-color: white;
    height: 50px;
    position: absolute;
    bottom: 0px;
    width: 100%;
    border-top-left-radius: 50px;
    border-top-right-radius: 50px;
    z-index: 5;
    justify-content: space-evenly;
    align-items: center;
    box-shadow: 0px 0px 6px 2px #5a6e9133;
  }


  .drawer a {
    display: inline-block;
    font-size: 1rem;
    /*color: #FFC168;*/
    color: #3F4E69;
    transition: all 0.4s;
    cursor: pointer;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
  }

  .drawer a i {
    font-size: 1.6rem;
  }

  .drawer a:hover {
    border-bottom: 2px solid #3F4E69;
  }

  .transaction:hover {
    box-shadow: 0 1px 5px -1px rgba(0, 0, 0, 0.3), inset 0 0 0 1px rgba(0, 0, 0, 0.15);
  }

  .page-card {
    display: flex;
    justify-content: center;
  }

  .container-card {
    /*height: 100vh;*/
    width: 100%;
    /*background-color: #F7F7F7;*/
    /*box-shadow: 0 1px 5px -1px rgba(0, 0, 0, 0.3), inset 0 0 0 1px rgba(0, 0, 0, 0.15);*/
    overflow: hidden;
    position: relative;
  }

  .header {
    /*height: 15%;*/
    /*background-color: #44B0A0;*/
    /*background-color: #3F4E69;*/
    border-bottom-right-radius: 40px;
    border-bottom-left-radius: 40px;
    display: flex;
    justify-content: space-around;
    /*align-items: center;*/
    position: relative;
    z-index: 1;
    overflow: hidden;
    padding-top: 15px;
  }

  .header-summary {
    display: flex;
    flex-direction: column;
    color: grey;
    z-index: 3;
  }

  .header-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 5px;
    letter-spacing: 1.5px;
  }

  .header-sub-title {
    font-size: 1.5rem;
    letter-spacing: 4px;
    margin-bottom: 0.8rem;
  }

  .user-profile {
    height: 70px;
    width: 70px;
    align-self: center;
    position: relative;
    z-index: 20;
  }

  .user-photo {
    height: 100%;
    width: 100%;
    border-radius: 50%;
    border: 1px solid white;
    box-shadow: 5px 5px 25px 0px rgba(0, 0, 0, 0.2);
  }

  .content {
    z-index: 4;
    /*position: absolute;*/
    /*top: 20%;*/
    /*left: 7.5%;*/
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .loaderclass {
    border: 8px solid #eee;
    border-top: 8px solid #7193be;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    position: fixed;
    z-index: 1;
    animation: spin 2s linear infinite;
    margin-top: 30%;
    margin-left: 40%;
    position: absolute;
    z-index: 99999;
  }

  .bootstrap-select.form-control:not([class*="span"]){
    width: 18%;
    margin-bottom: 10px;
  }
</style>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@10.12.5/dist/sweetalert2.all.min.js"
  integrity="sha256-vT8KVe2aOKsyiBKdiRX86DMsBQJnFvw3d4EEp/KRhUE=" crossorigin="anonymous"></script>
<script>
  var is_on_leave = 0;
  var disallow_attendance_outside = '<?php echo $this->settings->getSetting('staff_attendance_disallow_outside_checkin'); ?>';
  var mandate_late_remarks = '<?php echo $this->settings->getSetting('staff_attendance_mandate_late_remarks'); ?>';
  if (!mandate_late_remarks) mandate_late_remarks = '0';
  let reporting_manager;
  let reporting_manager_available;

  const isAttendanceCheckInDisabled="<?php echo $checkin_disabled; ?>";

  function getPlatformInfo() {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;

    // 1. Check if it's a Mobile Device (Android/iOS)
    const isMobile = /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());

    // 2. Detect if it's running inside WebView / WebKit
    const isWebView = (() => {
      // iOS WebView
      const iOSWebView = /(iphone|ipod|ipad).*applewebkit(?!.*safari)/i.test(userAgent);
      // Android WebView
      const androidWebView = /version\/[\d.]+(?!.*chrome).*mobile safari/i.test(userAgent);
      return iOSWebView || androidWebView;
    })();

    // 3. Identify if it's a desktop browser
    const isDesktop = !isMobile;

    // 4. Determine the platform and return information
    if (isWebView) {
      return "Mobile App";
    } else if (isMobile) {
      return "Mobile Browser";
    } else if (isDesktop) {
      return "Desktop Browser";
    } else {
      return "Unknown Platform";
    }
  }

  $(document).ready(function () {
    reporting_manager = '<?php echo $rep_manager_name; ?>';
    reporting_manager_available = '<?php echo $reporting_manager_available; ?>';
    change_month();
    shifts = JSON.parse('<?php echo json_encode($shifts) ?>');
    is_on_leave = <?php echo $is_on_leave; ?>;

    if (is_on_leave == 0) {
      $(".transactions").html('<div class="transaction" style="display:block;background: #f5c9c9;"><h5 class="text-danger text-center"><b>You have taken leave today. It is either Approved or in the process of Approval. Cancel your leave to check in.</b></h5></div>');
    } else if (is_on_leave == 1 || is_on_leave == 2) {
      $(".transactions").html('<div class="transaction" style="display:block;background: #f5c9c9;"><h5 class="text-danger text-center"><b>You are on leave today.</b></h5></div>');
    } else {
      callAttendanceTransactions();
    }
    // getLocation();
  });
  
  
  const reasons=async function getLeaveRegularizeReasons(){
    let LeaveRegularizeReasons=[];
    await $.ajax({
      url: "<?php echo site_url('staff/attendance/getLeaveRegularizeReasons'); ?>",
      type: "POST",
      data: {},
      success:function(data){
        let reasons=$.parseJSON(data);
        LeaveRegularizeReasons=reasons;
      }
    })
    return LeaveRegularizeReasons;
  }

  function callAttendanceTransactions(index = 0) {
    if (index < shifts.length) {
      var attendance_id = shifts[index].attendance_id;
      var staff_shift_id = shifts[index].staff_shift_id;
      index++;
      if (attendance_id == 0) {
        var html = getEntryButton(attendance_id, staff_shift_id, 'Check-in');
        $(".transactions").html(html);
      } else {
        getTransactions(attendance_id, staff_shift_id, index);
        // getTransactions(attendance_id, staff_shift_id, index);
      }
    }
  }

  const isCheckedOutBefore = async function (attendanceId) {
    let isCheckedOut = 0;
    await $.ajax({
      url: "<?php echo site_url('staff/attendance/getAttendanceCheckoutStatus'); ?>",
      type: "POST",
      data: { attendanceId },
      success(s) {
        if(!$.parseJSON(s)?.length){
          return isCheckedOut;
        }

        [s] = $.parseJSON(s);
        if (s && Object.keys(s)?.length) {
          isCheckedOut = s.event_time;
        }
      }
    })
    return isCheckedOut;
  }

  function getEntryButton(attendance_id, staff_shift_id, type) {
    if(type=="Check-in") attendance_id=0;

    var html='';
    if(+isAttendanceCheckInDisabled){
      html += '<div class="transaction" onclick="" style="background-color: #e0e0e0;color: #a0a0a0;border: 1px solid #ccc;cursor: not-allowed;">';
    }else{
      html += '<div class="transaction transaction-' + type + '" onclick="sendAttendance(' + attendance_id + ', ' + staff_shift_id + ',\'' + type + '\')">';
    }

    var check_class = 'in';
    var fa_class = 'fa-arrow-up';
    let message="loading...";

    if (type == 'Check-out') {
      const res = isCheckedOutBefore(attendance_id);
      res.then(s => {
        if (s) {
          const msg = `Check-out Again<br><small>Last check-out time ${s}</small>`;
          $("#att_msg").html(msg);

          $(`.transaction-${type}`).css("background", "#ffa0a0");
        }else{
          $("#att_msg").html(`${type} now`);
        }
      });
      check_class = 'out';
      fa_class = 'fa-arrow-down';
    }else{
      message=`${type} now`;
    }

    html += '<div class="check-icon ' + check_class + '"><span class="fa ' + fa_class + '"></span></div>';
    html += '<div class="check-text"><div class="check-title" id="att_msg">'+message+'</div></div>';
    html += '</div>';
    return html;
  }

  function getTransactions(attendance_id, staff_shift_id, index = 0) {
    $(".transactions").html('<div class="d-flex justify-content-center align-items-center" style="height: 150px;margin:auto;"><i class="fa fa-spinner fa-spin" style="font-size: 5rem;"></i></div>');
    $.ajax({
      url: "<?php echo site_url('staff/attendance/getAttendanceTransactions'); ?>",
      data: {
        'attendance_id': attendance_id
      },
      type: 'post',
      success: function (data) {
        var transactions = JSON.parse(data);
        const status= ["AB","HD","P","WO","H"];
        // console.log(typeof transactions, status.includes(transactions));
        if(typeof transactions=="string" && status.includes(transactions)){
          $(".transactions").html("Your Attendance has Been Updated by the Reporting Manager.");
          return;
        }

        var html = '';
        var type = '';
        var check_in_time, check_out_time;
        
        // set checkout time
        check_out_time=transactions[0].last_checkout_time;

        for (var i in transactions) {
          if(+isAttendanceCheckInDisabled){
            html += '<div class="transaction" style="background-color: #e0e0e0;color: #a0a0a0;border: 1px solid #ccc;cursor: not-allowed;">';
          }else{
            html += '<div class="transaction ' + transactions[i].event_type + '">';
          }

          html += '<div class="check-text"><div class="check-title">';
          inside_outside = (transactions[i].is_outside != null) ? ((transactions[i].is_outside == '1') ? ' from outside campus' : ' from inside campus') : '';
          if (transactions[i].event_type == 'Check-in') {
            html += 'Checked-in ' + inside_outside;
            check_in_time = transactions[i].event_date_time;
          } else {
            html += 'Checked-out ' + inside_outside;
            check_out_time = transactions[i].event_date_time;
          }
          html += '</div>';
          html += '<div class="check-time"><i class="fa fa-clock-o"></i>&nbsp;&nbsp;' + transactions[i].event_time + '</div></div>';
          if (transactions[i].event_type == 'Check-in') {
            html += '<div class="check-icon in"><span class="fa fa-arrow-up"></span></div>';
          } else {
            html += '<div class="check-icon out"><span class="fa fa-arrow-down"></span></div>';
          }
          html += '</div>';
          type = transactions[i].event_type;
        }
        // type = (type == 'Check-in')?'Check-out':'Check-in';
        // html += getEntryButton(attendance_id, staff_shift_id, type);
        check_in_time = moment(check_in_time);
        // check_out_time = moment(check_out_time);
        var duration = 0;
        if (type == 'Check-in') {
          // check_out_time = moment();
          html += getEntryButton(attendance_id, staff_shift_id, 'Check-out');
        }else{
          html += getEntryButton(attendance_id, staff_shift_id, 'Check-in');
        }

        $("#duration").html('');
        var duration = getDuration(check_in_time, moment(check_out_time));
        if (duration != '') {
          $("#duration").html('<h4 class="text-center"><i class="fa fa-clock-o"></i>&nbsp;&nbsp;' + duration + '</h4>');
        }


        $(".transactions").html(html);
        if (index != 0) {
          callAttendanceTransactions(index);
        }
      },
      error: function (err) {
        console.log(err);
        Swal.fire({
          icon: 'error',
          title: 'Failed to remove data!!'
        })
      }
    });
  }

  function getDuration(check_in_time, check_out_time) {
    var duration = moment.duration(check_out_time.diff(check_in_time));
    var milliseconds = duration.asMilliseconds();
    var hours = moment.utc(milliseconds).format("H");
    var mins = moment.utc(milliseconds).format("m");
    var dur = '';
    if (hours > 0) {
      dur += hours + ' hr ';
    }
    if (mins > 0) {
      dur += mins + ' mins';
    }
    return dur;
  }

  function getLocation() {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(showPosition, showError);
    } else {
      $("#location").html("Geolocation is not supported by this browser.");
    }
  }

  function showPosition(position) {
    $("#latitude").val(position.coords.latitude);
    $("#longitude").val(position.coords.longitude);
    return {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude
    };
    // $("#location").html("Latitude: " + position.coords.latitude +"<br>Longitude: " + position.coords.longitude);
  }

  function showError(error) {
    alert(error)
    var html = '<span class="text-danger">';
    switch (error.code) {
      case error.PERMISSION_DENIED:
        html += "Permission to access the location is not provided<br>Provide location permission by going to app settings."
        break;
      case error.POSITION_UNAVAILABLE:
        html += "Location information is unavailable."
        break;
      case error.TIMEOUT:
        html += "Your device GPS is not turned on"
        break;
      case error.UNKNOWN_ERROR:
        html += "An unknown error occurred."
        break;
    }
    html += '</span>';
    $("#location").html(html);
  }

  function checkLocation() {
    return new Promise(function (resolve, reject) {
      if (navigator.geolocation) {
        var options = {
          enableHighAccuracy: true,
          timeout: 2000,
          maximumAge: 0
        };
        navigator.geolocation.getCurrentPosition((position) => {
          $("#latitude").val(position.coords.latitude);
          $("#longitude").val(position.coords.longitude);
          $("#is_location_captured").val(1);
          $("#location_capture_error").val('');
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          })
        }, (error) => {
          var html = '<span class="text-danger">';
          switch (error.code) {
            case error.PERMISSION_DENIED:
              html += "Permission to access the location is not provided<br>Provide location permission by going to app settings."
              $("#location_capture_error").val('PERMISSION_DENIED');
              break;
            case error.POSITION_UNAVAILABLE:
              html += "Location information is unavailable."
              $("#location_capture_error").val('POSITION_UNAVAILABLE');
              break;
            case error.TIMEOUT:
              html += "Your device GPS is not turned on."
              $("#location_capture_error").val('TIMEOUT');
              break;
            case error.UNKNOWN_ERROR:
              html += "An unknown error occurred. Unable to get your location."
              $("#location_capture_error").val('UNKNOWN_ERROR');
              break;
          }
          $("#is_location_captured").val(0);
          html += '</span>';
          reject(html);
        }, options);
      } else {
        $("#location").html("Geolocation is not supported by this browser.");
        reject('Location permission is denied by you.');
      }
    });

  }

  function isMeaningfulRemark(remark) {
    // Trim spaces at the beginning and end
    const trimmedRemark = remark.trim();

    // 1. Check if the remark is empty or just spaces
    if (trimmedRemark.length === 0) {
      return false;
    }

    // 2. Check if the remark is too short (e.g., < 3 meaningful characters)
    if (trimmedRemark.length < 3) {
      return false;
    }

    // 3. Check if the remark contains only repeated characters (e.g., "aaa")
    const repeatedChars = /^([a-zA-Z0-9])\1*$/;
    if (repeatedChars.test(trimmedRemark)) {
      return false;
    }

    // 4. Check for gibberish (only symbols or numbers, no words)
    const hasValidWords = /[a-zA-Z]/.test(trimmedRemark);
    if (!hasValidWords) {
      return false;
    }

    // If all validations pass, the remark is considered meaningful
    return true;
  }

  function confirmLocation(attendance_id, staff_shift_id, type) {
    // console.log(type);
    const isStaffLate=$("#isStaffLate").val();
    const attendanceRemarks=$("#attendance-remarks").val();

    if (mandate_late_remarks == '1') {
      if(+isStaffLate && type!=="Check-out" && !attendanceRemarks){
        return bootbox.alert("<h3>Late Login</h3> <br> <p>Please fill the remarks for late login</p>");
      }

      // It will come over here
      const isMeaningFul=isMeaningfulRemark(attendanceRemarks);
      if(+isStaffLate && type!=="Check-out" && !isMeaningFul){
        return bootbox.alert("<h3>Late Login</h3> <br> <p>Please fill meaningful remarks</p>");
      }
    }

    if(attendanceRemarks?.length){
      const isMeaningFul=isMeaningfulRemark(attendanceRemarks);
      if(!isMeaningFul){
        return bootbox.alert("<p>Please fill meaningful remarks</p>");
      }
    }

    $("#att-confirm-btn").attr('disabled', true);
    $("#btn_" + staff_shift_id).prop('disabled', true);

    const staffFullDay = $(`#shift_minute_diff_${staff_shift_id}`).val();
    const staffHalfDay = staffFullDay * 1.0 / 2.0;

    const userAgent = window.navigator?.userAgent;
    const platform = window.navigator?.platform;
    const screenWidth = window.screen?.width;
    const screenHeight = window.screen?.height;
    const language = window.navigator?.language;
    const deviceMemory = window.navigator?.deviceMemory;
    const isTouchDevice = window.navigator?.maxTouchPoints > 0;
    const connection = window.navigator?.connection || window.navigator?.mozConnection || window.navigator?.webkitConnection;

    let device_id = '';
    if((typeof MyFunction !== "undefined" && MyFunction !== null)) {
      try {
        device_id = MyFunction.getDeviceID();
      }
      catch(err) {
        device_id = '';
      }
    }

    let device_name = '';
    if((typeof MyFunction !== "undefined" && MyFunction !== null)) {
      try {
        device_name = MyFunction.getDeviceName();
      }
      catch(err) {
        device_name = '';
      }
    }

    const userDeviceInfo={
      userAgent:userAgent,
      platform:platform,
      screenWidth:screenWidth,
      screenHeight:screenHeight,
      language:language,
      deviceMemory:deviceMemory,
      isTouchDevice:isTouchDevice,
      connection:connection,
      deviceId: device_id,
      deviceName: device_name
    }

    var input = {
      'latitude': $("#latitude").val(),
      'longitude': $("#longitude").val(),
      'is_location_captured': $("#is_location_captured").val(),
      'location_id': $("#location_id").val(),
      'is_outside': $("#is_outside").val(),
      'distance': $("#distance").val(),
      'location_capture_error': $("#location_capture_error").val(),
      'shift_start_time': $("#shift_start_time_" + staff_shift_id).val(),
      'shift_end_time': $("#shift_end_time_" + staff_shift_id).val(),
      'shift_date': $("#shift_date_" + staff_shift_id).val(),
      'attendance_id': attendance_id,
      'staff_shift_id': staff_shift_id,
      'event_type': type,
      'remarks': $("#attendance-remarks").val(),
      'location_name': $("#check_in_out_location_name").val() || "",
      staffFullDay,
      staffHalfDay,
      'userDeviceInfo':JSON.stringify(userDeviceInfo),
    };

    // alert(input?.location_id)
    $.ajax({
      url: "<?php echo site_url('staff/attendance/addAttendance'); ?>",
      data: input,
      type: 'post',
      success: function (data) {
        var attendance_id = parseInt(data);
        if (attendance_id) {
          if (attendance_id == '-100') {
            Swal.fire({
              icon: 'error',
              title: 'You have already checked in. Failed!!'
            });
          }
          change_month();
          getTransactions(attendance_id, staff_shift_id);

          $(function () {
            new PNotify({
              title: 'Success',
              text: `${type} successful`,
              type: 'success',
            });
          });

        } else {
          $("#btn_" + staff_shift_id).prop('disabled', false);
          Swal.fire({
            icon: 'error',
            title: 'Failed!!'
          })
        }
      },
      error: function (err) {
        console.log(err);
        Swal.fire({
          icon: 'error',
          title: 'Failed!!'
        })
      }
    });
    $("#info-modal").modal('hide');
  }

  function refreshLocation(attendance_id, staff_shift_id, type) {
    $("#location-data").html('');
    if(type=="Check-in") attendance_id=0;
    sendAttendance(attendance_id, staff_shift_id, type);
  }

  let timer;

  const backButton = () => {
    clearInterval(timer);
    $("#att_timer").text("");
  };

  function sendAttendance(attendance_id, staff_shift_id, type) {
    clearInterval(timer);
    let seconds = 59;
    timer = setInterval(() => {
      $("#att_timer").text(seconds);
      if (seconds < 1) {
        $("#attendance_back_btn").trigger('click');
        clearInterval(timer);
      }
      seconds--;
    }, 1000)

    $("#att-confirm-btn").attr('disabled', false);
    $("#info-modal").modal('show');
    $("#location-data").html('<i class="fa fa-spinner fa-spin" style="font-size: 40px;margin-top: 2%;"></i>');

    let footer = `
        <button onclick="refreshLocation(${attendance_id}, ${staff_shift_id}, '${type}')" class="btn btn-warning my-0">Refresh Location</button>
        <button data-dismiss="modal" class="btn btn-danger my-0" id="attendance_back_btn" onClick="backButton()">Close</button>
        `;

    checkLocation()
      .then(position => {
        const {latitude,longitude}=position;
        const staffShiftId=$("#staff-shift-id").val();

        $.ajax({
          url: "<?php echo site_url('staff/attendance/checkPositionOutside'); ?>",
          data: {latitude,longitude,staffShiftId},
          type: 'post',
          success: function (data) {
            data = JSON.parse(data);

            // console.log(data);

            $("#location_id").val(data.location_id);
            $("#is_outside").val(data.is_outside);
            $("#distance").val(data.distance);
            $("#check_in_out_location_name").val(data.location_name);
            $("#isStaffLate").val(data.isStaffLate);

            var info = `<div class="row"><div class="col-md-12">`;
            if (data.is_outside == 1) {
              if (disallow_attendance_outside == '1') {
                info += `<div><h5>Location detected to be <b class="text-danger">outside school zone</b> <span style="background: #ffdcdc;">(${data.location_name})</span> (${data.distance} km).</h5><h6>You need to be inside the location to check-in or check-out. 'Refresh Location' to retry.</h6></div>`;
              } else {
                info += `<div><h5>Location Captured and detected to be <b class="text-danger">outside school zone</b> <span style="background: #ffdcdc;">(${data.location_name})</span> (${data.distance} km).</h5></div>`;
              }
            } else {
              info += `<div><h5>Location Captured and detected to be <b class="text-success">inside school zone</b> <span style="background: #ffdcdc;">(${data.location_name})</span> (${data.distance} km).</h5></div>`;
            }
            info += '</div></div>';

            if (!(disallow_attendance_outside == '1' && data.is_outside == 1)) {
              info += `<div class="row"><div class="col-md-12"><textarea required placeholder="Remarks if any" class="form-control" rows="5" id="attendance-remarks"></textarea></div></div>`;
              footer += `<button id="att-confirm-btn" onclick="confirmLocation(${attendance_id}, ${staff_shift_id}, '${type}')" class="btn btn-primary my-0">Confirm</button>`
            }

            $("#location-data").html(info);
            $("#info-footer").html(footer);

            // if (disallow_attendance_outside == '1' && data.is_outside == 1) {
            //     $("#att-confirm-btn").prop('disabled', true).hide();
            // }
          },
          error: function (err) {
            console.log(err);
          }
        });
      })
      .catch((err) => {
        $("#location-data").html(err);
        $("#info-footer").html(footer);
      });

    /*Swal.fire({
        title: type,
        text: "Are you sure?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Confirm'
    }).then((result) => {
        if (result.isConfirmed) {
            checkLocation();
            $("#btn_"+staff_shift_id).prop('disabled', true);
            // $("#entry_"+staff_shift_id).prop('disabled', true);
            var input = {
                'latitude' : $("#latitude").val(),
                'longitude' : $("#longitude").val(),
                'shift_start_time' : $("#shift_start_time_"+staff_shift_id).val(),
                'shift_end_time' : $("#shift_end_time_"+staff_shift_id).val(),
                'shift_date' : $("#shift_date_"+staff_shift_id).val(),
                'attendance_id': attendance_id,
                'staff_shift_id': staff_shift_id,
                'event_type': type
            };
            $.ajax({
                url: "<?php // echo site_url('staff/attendance/addAttendance'); 
                ?>",
                data: input,
                type: 'post',
                success: function(data) {
                    var attendance_id = parseInt(data);
                    if (attendance_id) {
                        getTransactions(attendance_id, staff_shift_id)
                    } else {
                        $("#btn_"+staff_shift_id).prop('disabled', false);
                        // $("#entry_"+staff_shift_id).prop('checked', false);
                        // $("#entry_"+staff_shift_id).prop('disabled', false);
                        Swal.fire({
                            icon: 'error',
                            title: 'Failed!!'
                        })
                    }
                },
                error: function(err) {
                    console.log(err);
                    Swal.fire({
                        icon: 'error',
                        title: 'Failed!!'
                    })
                }
            });
        } else {
            $("#btn_"+staff_shift_id).prop('disabled', false);
            // $("#entry_"+staff_shift_id).prop('checked', false);
        }
    });*/
  }

  async function change_month() {
    $("#showAttStatistics").hide();
    $("#attendance-data").hide();
    
    var year_month = $('#month_list').val();
    //document.write(year_month);
    await $.ajax({
      url: '<?php echo site_url('staff/attendance/changeMonth') ?>',
      type: 'post',
      data: {
        'year_month': year_month
      },
      beforeSend() {
        $(".staffAttDetails").html("<p style='text-align: center'>Loading...</p>");
      },
      success: function (data) {
        $(".staffAttDetails").html("");
        $("#attendance-data").show();

        var attendance = JSON.parse(data);
        if (attendance.length) {
          $("#showAttStatistics").show();
          var html = '<table class="table table-bordered">';
          html += constructHeader();
          html += constructReport(attendance);
          html += '</table>';

          setTimeout(() => {
            $("#attendance-data").html(html);
          }, 700);
        } else {
          let msg = `
              <div style="color:red;text-align:center;
                color: black;
                border: 2px solid #fffafa;
                text-align: center;
                border-radius: 6px;
                position: relative;
                padding: 10px;
                font-size: 14px;
                margin-top: 14px;
                background: #ebf3ff;">
                  No Data to show
                </div>
                `;
          $("#attendance-data").html(msg);
        }
      },
      error: function (err) {
        console.log(err);
      }
    });
  }

  function constructHeader() {
    var html = '';
    html += '<thead>';
    html += '<tr>';
    html += '<th>#</th>';
    html += '<th>Date</th>';
    html += '<th>Shift start time</th>';
    html += '<th>Shift end time</th>';
    html += '<th>Attendance Status</th>';
    html += '<th>Leave Info</th>';
    html += '<th>Check-in</th>';
    html += '<th>Check-out</th>';
    <?php if ($this->settings->getSetting('enable_staff_regularize_attendance')) { ?>
        html += '<th>Approved/Rejected Reason</th>';
        html += '<th>Action</th>';
    <?php } ?>
      html += '</tr>';
    html += '</thead>';
    return html;
  }

  function regularizeOptions(){
    if($("#regularize_reasons").val()==0){
      $("#regularize_textarea").show();
    }else{
      $("#regularize_textarea").hide();
    }
  }

  async function regularizeLeave(attendance_id, staff_id, date, btn_id, attendance_status, staff_shift_id) {
    var html = `
      <div class="">
        <label for="">Reporting Manager</label>
        ${reporting_manager}
      <div>
      <div>
          <div class="regularize_reason" data-regularize_type="options" id="regularize_options" onchange="regularizeOptions()">
            <select class="form-control" name="regularize_reasons" id="regularize_reasons">`;

            const leaveRegularizeReasons=await reasons();
            if(leaveRegularizeReasons?.length){
              leaveRegularizeReasons.forEach(r=>{
                html+=`<option value="${r.name}">${r.name}</option>`;
              });
            }else{
                html+=`<option value="NA">Options not available</option>`;
            }
            html+=`<option value="0">Other</option>`
            
            html+=`</select>
          </div>
      </div>

      <div class="regularize_reason" data-regularize_type="textarea" id="regularize_textarea" style="margin-top:10px;display:none;">
          <textarea placeholder="Enter reason here..." class="form-control" id="reason" rows="5"></textarea>
        </div>
      `;

    let staff_name = $("#staff_name").text();
    let reason;

    await Swal.fire({
      title: `Regularize Attendance <br> ${date}`,
      html: html,
      confirmButtonText: 'Confirm',
      showCancelButton: true,
      showLoaderOnConfirm: true,
      allowOutsideClick: false,
      preConfirm: function () {
        if($("#regularize_reasons").val()==0){
          reason = $("#reason").val().trim();
        }else{
          reason = $("#regularize_reasons").val().trim();
        }

        if (!reason) {
          Swal.showValidationMessage('Enter the reason');
        }
      }
    }).then((result) => {
      if (result.isConfirmed) {
        var input = {
          attendance_id,
          staff_id,
          date,
          reason,
          attendance_status,
          staff_shift_id
        };

        $(`#${btn_id}`).text("Request Sent").removeClass('btn-success').removeClass('btn-danger').prop("disabled", true);

        $.ajax({
          url: "<?php echo site_url('staff/attendance/regularizeLeaveApplication') ?>",
          type: "POST",
          data: input,
          success: function (data) {
            Swal.fire({
              title: "Success",
              text: "Regularize Request Sent",
              icon: "success",
            });
          }
        })
      }
    });
  }

  const leaveStatusObject={
    0:"Pending",
    1:"Approved",
    2:"Auto Approved",
    3:"Rejected",
    4:"Cancelled",
  }

  function viewOverriddenReason(index,attendanceId,staffName,date){
    let overriddenReason = '<div class="mb-2"><h4>'+staffName+' ('+date+')</h4></div>';
    overriddenReason+=$(`#overridden-reason-${index}`).data("overridden-reason");

    Swal.fire({
      title: `<strong><u>Override Reason</u></strong>`,
      icon: "info",
      html: `${overriddenReason}`,
      showCloseButton: true,
      focusConfirm: false,
      confirmButtonText: `Go back!`,
    }).then(e=>{
      getAttendanceOverriddenInfo(attendanceId,staffName,date);
    });

  }

  function getAttendanceOverriddenInfo(attendanceId,staffName,date){
    const attendanceOverridenInfo=$(`#leave-overridden-info-${attendanceId}`).data("leave-over-info");
    var html = '<div class="mb-2"><h4>'+staffName+' ('+date+')</h4></div>';
    for(var i=0; i<attendanceOverridenInfo.length; i++) {
      // Format
      // <Action By name> has <Added Overridden status> on <Action on time> from <Source>
      html+=`
          <div style="border-bottom: 2px solid grey;padding: 3px;margin: 1px;text-align: left;">
            ${attendanceOverridenInfo[i].action_by} has ${attendanceOverridenInfo[i].action} on ${attendanceOverridenInfo[i].action_at}
            <br>
            <button class="btn btn-primary" id="overridden-reason-${i}" data-overridden-reason="${attendanceOverridenInfo[i].reason}" onclick="viewOverriddenReason(${i},${attendanceId},'${staffName}','${date}')">View Reason</button>
          </div>
      `;
    }

    Swal.fire({
      title: `<strong><u>Override information</u></strong>`,
      html: `${html}`,
      showCloseButton: true,
      focusConfirm: false,
      confirmButtonText: `Close!`,
    });
  }

  function constructReport(attendance) {
    // let isLeaveResolved = false;
    var html = '<tbody>';
    var j = 1;
    const staff_attendance_algorithm	= "<?php echo $this->settings->getSetting('staff_attendance_algorithm') ?>";

    var total = attendance.length;
    var p = 0;
    var ab = 0;
    var hd = 0;
    var wo = 0;
    var ho = 0;
    var ol = 0;
    let extra_p_wo = 0;
    let extra_p_ho = 0;

    for (let i in attendance) {
      let leave_filed_no_of_days_without_consider_present=0;
      // if (!attendance[i].attendance_id) continue;
      function isDateToday(checkIndate) {
        const checkDate = new Date(checkIndate);
        const today = new Date();

        const checkInDate = checkDate.getDate();
        const checkInMonth = checkDate.getMonth();
        const checkInFullYear = checkDate.getFullYear();

        const todayDate = today.getDate();
        const todaMonth = today.getMonth();
        const todayFullYear = today.getFullYear();

        return checkInDate == todayDate && checkInMonth == todaMonth && checkInFullYear == todayFullYear;
      }

      if(isDateToday(attendance[i].date)){
        if(total){
          total=total-1;
        }
        continue;
      }

      let isOnLeave=0;
      if(attendance[i]?.leave_details){
        if(Object.values(attendance[i]?.leave_details)[0]?.total_leaves_taken_count>=0.5){
          const leaveDetails=Object.values(attendance[i]?.leave_details);
        
          if(leaveDetails[0]){
            const leavesTaken=leaveDetails[0];
            for(let leave in leavesTaken){
              if(typeof leavesTaken[leave]==="object" && +leavesTaken[leave].status>=0 && +leavesTaken[leave].status<=2){
                if(+leavesTaken[leave].consider_present==0){
                  leave_filed_no_of_days_without_consider_present+= +leavesTaken[leave].leave_no_of_days;
                }
              }
            }
          }
        }
      }

      const isToday = new Date(attendance[i].date).toDateString() === new Date().toDateString();
      const shiftType=Number(attendance[i].type);
      const attendanceStatus=attendance[i].attendance_status;
      // we will take all the Attendance which are happend on week-off or holiday
      if(shiftType===2 || shiftType===3){
        // 2: week-off shift
        // 2: holiday shift
        if (shiftType===2) {
          wo += 1;
        } else if (shiftType===3) {
          ho += 1;
        }

         // Track if present or half-day on off-days
        if (attendanceStatus === "P" || attendanceStatus === "FD") {
          if (shiftType === 2) extra_p_wo += 1;
          if (shiftType === 3) extra_p_ho += 1;
        } else if (attendanceStatus === "HD") {
          if (shiftType === 2) extra_p_wo += 0.5;
          if (shiftType === 3) extra_p_ho += 0.5;
        }
      }else if(shiftType===1){
        // 1: week-day or working day shift
        if (attendanceStatus == "P" || attendanceStatus == "FD") {
          p += 1;
        } else if (attendanceStatus == "HD") {
          p += 0.5;
          if (leave_filed_no_of_days_without_consider_present == 0.5) {
            ol += 0.5;
          } else {
            ab += 0.5;
          }
        } else if (attendanceStatus == "AB") {
          if (leave_filed_no_of_days_without_consider_present == 0) {
            ab += 1;
          } else if (leave_filed_no_of_days_without_consider_present == 0.5) {
            ol += 0.5;
            ab += 0.5;
          } else if (leave_filed_no_of_days_without_consider_present >= 1) {
            ol += 1;
          }
        }
      }

      var bck = "";
      var status = "NA";

      const overriddenInfoBtn=`<br>
      <span id=leave-overridden-info-${attendance[i].attendance_id} data-leave-over-info='${JSON.stringify(attendance[i].check_in_out_info)}' style="cursor:pointer;" onclick="getAttendanceOverriddenInfo(${attendance[i].attendance_id},'${attendance[i].staff_name}','${attendance[i].date}')">(<u>Overridden</u>)<span>`;

      if (attendance[i].type == 2) {
        bck = "weekoff";
        status = `Week-Off ${attendance[i].is_manually_changed==1 ? overriddenInfoBtn : ""} ${(attendance[i].attendance_status=="P" || attendance[i].attendance_status=="HD") ? `<span style='color:green !important'>(Present ${attendance[i].attendance_status})</span>` : ""}`;
      } else if (attendance[i].type == 3) {
        status = `Holiday ${attendance[i].is_manually_changed==1 ? overriddenInfoBtn : ""} ${(attendance[i].attendance_status=="P" || attendance[i].attendance_status=="HD") ? `<span style='color:green !important'>(Present ${attendance[i].attendance_status})</span>` : ""}`;
        bck = "holiday";
      }else{
          if (attendance[i].attendance_status == "AB") {
            bck = "absent";
            status = `Absent ${attendance[i].is_manually_changed==1 ? overriddenInfoBtn : ""}`;
          } else if (attendance[i].attendance_status == "HD") {
            bck = "halfday";
            status = `Half-day ${attendance[i].is_manually_changed==1 ? overriddenInfoBtn : ""}`;
          } else if (attendance[i].attendance_status == "P" || attendance[i].attendance_status == "FD") {
            bck = "present";
            status = `Present ${attendance[i].is_manually_changed==1 ? overriddenInfoBtn : ""}`;
          }
      }

      var checkin = ' - ';
      var checkout = ' - ';
      var is_checked_in_outside = '';
      var is_checked_out_outside = '';
      var checked_in_location_name='';
      var checked_out_location_name='';

      if (attendance[i].first_check_in_time != '') {
        checkin = attendance[i].first_check_in_time;
      }

      if (attendance[i].last_check_out_time != '') {
        checkout = `${attendance[i].last_check_out_time} ${attendance[i].is_auto_check_out}`;
      }

      if(attendance[i].check_in_out_info?.length){
        if(attendance[i].first_check_in_time != '') {
          is_checked_in_outside=attendance[i].check_in_out_info[0].is_outside_campus==1 ? "<br> Found Outside check-in" : "";

          if(attendance[i].check_in_out_info[0].location_name){
            checked_in_location_name=`Loc: ${attendance[i].check_in_out_info[0].location_name}`;
          }
        } 

        if(attendance[i].check_in_out_info?.length>1 && attendance[i].last_check_out_time != ''){
          is_checked_out_outside=attendance[i].check_in_out_info.at(-1).is_outside==1 ? "<br> Found Outside check-out" : "";

          if(attendance[i].check_in_out_info.at(-1).location_name){
            checked_out_location_name=`Loc: ${attendance[i].check_in_out_info.at(-1).location_name}`;
          }
        }
      }

      html += '<tr>';
      html += '<td>' + (j++) + '</td>';
      html += '<td>' + attendance[i].date + '</td>';

      html += `<td>${attendance[i].shift_start_time || "-"}</td>`;
      html += `<td>${attendance[i].shift_end_time || "-"}</td>`;
      
      
      // Display leave status if any here
      if(Object.entries(attendance[i]?.leave_details)?.length && +Object.values(attendance[i]?.leave_details)[0]?.total_leaves_taken_count!==0){
        const leaveDetails=Object.values(attendance[i]?.leave_details);
          // If there are any leaves, the we need to display leave taken names in place of attendancee status
          if(leaveDetails[0]){
            const totalLeavesTakenNames={};
            const leavesTaken=leaveDetails[0];
            for(let leave in leavesTaken){
              if(typeof leavesTaken[leave]==="object" && leavesTaken[leave].status>=0 && leavesTaken[leave].status<=2 ){
                totalLeavesTakenNames[leavesTaken[leave]?.category_short_name]=leavesTaken[leave]?.category_short_name;
              }
            }

            let leaveTakenNames="";
            if(Object.keys(totalLeavesTakenNames)?.length){
              leaveTakenNames=`On ${Object.keys(totalLeavesTakenNames).join(",")} ${(attendance[i].attendance_status=="P" || attendance[i].attendance_status=="HD") ? `<span style='font-weight: bold;color: green;'>(Present ${attendance[i].attendance_status}) ${attendance[i].is_manually_changed==1 ? overriddenInfoBtn : ""}</span>` : ""}`;
            }

            html += `<td>${leaveTakenNames}</td>`;
          }
      }else{
          html += `<td class="${bck}">${staff_attendance_algorithm=="consider_check_in_only" && status || isDateToday(attendance[i].date) && attendance[i].last_check_out_time && status || !isDateToday(attendance[i].date) && status || "NA"}</td>`;
      }

      // Displaying leave information below
      if(Object.entries(attendance[i]?.leave_details)?.length){
        const leaveDetails=Object.values(attendance[i]?.leave_details)[0];
        html += `<td>
                      <a href="javascript:void(0)" class="link-primary" id="leave-info-${i}" data-leave-info='${JSON.stringify(leaveDetails)}' onclick="viewLeaveInfo(${i},'${attendance[i].staff_name}','${attendance[i].date}')">View info</a>
                </td>`;
      }else{
        html += '<td style=""> - </td>';
      }
      // Displaying leave info above

      html += `<td>${checkin} ${is_checked_in_outside} <br> ${checked_in_location_name}</td>`;
      html += `<td>${checkout} ${is_checked_out_outside} <br> ${checked_out_location_name}</td>`;

      <?php if ($this->settings->getSetting('enable_staff_regularize_attendance')) { ?>
                  if (attendance[i].leave_resolved != null) {
            html += `<td id="${i}_approved_reason">${attendance[i]?.approved_reason || "-"}</td>`;
            if (attendance[i].leave_resolved == 1) {
              html += `<td id="${i}_approved_reason" disabled="true">Corrected</td>`;
            } else if (attendance[i].leave_resolved == 2) {
              html += `<td id="${i}_approved_reason" disabled="true">Rejected</td>`;
            } else {
              html += `<td id="${i}_approved_reason" disabled="true">Request Sent</td>`;
            }
          } else {
            html += `<td id="${i}_approved_reason">-</td>`;

            const isRegularizeAvail = isDateToday(attendance[i].date);

            if (!isRegularizeAvail) {
              if (attendance[i].on_leave==0 && (bck == "halfday" || bck == "absent")) {
                if (!+reporting_manager_available) {
                  html += `<td >
                    Manager not assigned
                    </td>`;
                } else {
                  html += `<td><button id="${i}" style="font-size: 10px;" class="btn btn-info" onClick="regularizeLeave('${attendance[i].attendance_id}','${attendance[i].staff_id}','${attendance[i].date}','${i}','${attendance[i].attendance_status}',${attendance[i].shift_id})">Regularize Attendance</button></td>`;
                }
              } else {
                html += `<td>-</td>`;
              }
            } else {
              html += `<td>-</td>`;
            }
          }
      <?php } ?>
        html += '</tr>';
    }
    html += '</tbody>';

    let statistics='';

    statistics += '<div class="unread_box_no_style_new" >';
    statistics += '<div class="d-flex justify-content-between">';
    statistics += ' <table class="table">';

    statistics += ' <tr>';
    statistics += ' <td>';
    statistics += '<div display: inline; style="font-weight: bold;">';
    statistics += 'Total days: ';
    statistics += total;
    statistics += '</div>';
    statistics += '</td>';
    statistics += '</tr>';

    statistics += ' <tr>';
    statistics += ' <td>';
    statistics += '<div style="color:green; font-weight: bold; display: inline;">';
    statistics += 'Present: ';
    statistics += p;
    statistics += '</div>';
    statistics += '</td>';
    statistics += '</tr>';

    statistics += ' <tr>';
    statistics += ' <td>';
    statistics += '<div style="color:red; font-weight: bold; display: inline;">';
    statistics += 'Absent: ';
    statistics += `${ab+ol} (Leaves filed:${ol}, Leaves not filed:${ab})`;
    statistics += '</div>';
    statistics += '</td>';
    statistics += '</tr>';

    statistics += ' <tr>';
    statistics += ' <td>';
    statistics += '<div style="color: #ffb203; font-weight: bold; display: inline;">';
    statistics += 'Week-Off/Holidays: ';
    statistics += wo+ho;
    statistics += '</div>';
    statistics += '</td>';
    statistics += '</tr>';

    statistics += ' <tr>';
    statistics += ' <td>';
    statistics += '<div style="color: #3e3e3e; font-weight: bold; display: inline;">';
    statistics += 'Extra Present on Week-Off/Holidays: ';
    statistics += `${extra_p_wo+extra_p_ho}`;
    statistics += '</div>';
    statistics += '</td>';
    statistics += '</tr>';
    
    statistics += '</table>';
    statistics += '</div>';
    statistics += '</div>';
    // set statistics data in showAttStatistics
    $("#showAttStatistics").html(statistics);

    return html;
  }

  function viewLeaveInfo(index,staffName,date){
    const leaveDetails=$(`#leave-info-${index}`).data("leave-info");
    let html='';

    if(Object.entries(leaveDetails)?.length){
      html += `<div>`;
      html += '<div class="mb-2"><h4>'+staffName+' ('+date+')</h4></div>';
      for(leave of Object.values(leaveDetails)){
        const leaveDurationType=+leave.leave_no_of_days>0.5 ? "Full" : "Half";
        const isLeaveConsideredAsPresent=`${+leave.consider_present==1 ? `<br> This ${leave.category_name} will be considered as Present` : ""}`;
        const leaveStaus=leaveStatusObject[leave.status];
        if(typeof leave=="object"){
          html += `
          <div style="border-bottom: 2px solid grey;padding: 3px;margin: 1px;text-align:left;">
            <span>${leave.leave_filed_by_name} have filed ${leaveDurationType} Day ${leave.category_name} (<span>${leaveStaus}</span>)
            <small>${isLeaveConsideredAsPresent}</small>`;

          let leaveRemarks="";
          if(leave.status==0){
            leaveRemarks=leave.leave_applied_remarks;
          }

          if(leave.status==1 || leave.status==2 || leave.status==3){
            leaveRemarks=leave.leave_approved_remarks;
          }

          if(leave.status==4){
            leaveRemarks=leave.leave_cancelled_remarks;
          }

          if(leaveRemarks?.length){
            html += `<br>
            <button style="cursor:pointer;" class="btn btn-primary" id="leave-remarks-${leave.leave_id}" data-leave-remarks="${leaveRemarks}" onClick="viewLeaveRemarks(${leave.leave_id},'${staffName}','${date}',${index})">View Remarks</button>`;
          }
          
          html+=`</div>`;
        }
      }
      html += `</div>`;
    }

    Swal.fire({
      title: `<strong><u>Information</u></strong>`,
      // icon: "info",
      html: `${html}`,
      showCloseButton: true,
      focusConfirm: false,
      confirmButtonText: `Close!`,
    });
  }

  function viewLeaveRemarks(leaveId,staffName,date,index){
    let leaveRemarks = '<div class="mb-2"><h4>'+staffName+' ('+date+')</h4></div>';
    leaveRemarks+=$(`#leave-remarks-${leaveId}`).data("leave-remarks");

    Swal.fire({
      title: `<strong><u>Leave remarks</u></strong>`,
      icon: "info",
      html: `${leaveRemarks}`,
      showCloseButton: true,
      focusConfirm: false,
      confirmButtonText: `Go back!`,
    }).then(e=>{
      viewLeaveInfo(index,staffName,date)
    })
  }
</script>

<style type="text/css">
  .bootbox .modal-dialog {
    width: 50%;
    margin: auto;
  }

  .transaction {
    width: 50%;
  }

  .swal2-popup{
    width:50%;
  }
</style>