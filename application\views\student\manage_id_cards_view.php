<style>
/* ID Card Details Page Styling */
.person-card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border-radius: 10px;
    overflow: hidden;
}

.person-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.person-card .card-header {
    border-bottom: none;
    padding: 15px 20px;
    font-weight: 600;
}

.person-card .card-body {
    padding: 20px;
    min-height: 280px;
}

.profile-img-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.profile-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.person-details {
    padding-left: 10px;
}

.detail-item {
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
}

.detail-item strong {
    color: #2c3e50;
    font-weight: 600;
}

.view-idcard-btn {
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.view-idcard-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.no-data-container {
    min-height: 200px;
    text-align: center;
}

.no-data-display {
    font-size: 16px;
    font-weight: 500;
}

.no-data-icon {
    opacity: 0.6;
}

.action-buttons .btn {
    border-radius: 20px;
    padding: 8px 16px;
    font-weight: 500;
    margin: 2px;
}

.g-4 {
    --bs-gutter-x: 1.5rem;
    --bs-gutter-y: 1.5rem;
}

/* Bootstrap 4 compatibility - replace me- classes with mr- */
.me-1 { margin-right: 0.25rem !important; }
.me-2 { margin-right: 0.5rem !important; }
.me-3 { margin-right: 1rem !important; }

/* Additional Bootstrap utilities for better compatibility */
.text-muted { color: #6c757d !important; }
.h-100 { height: 100% !important; }

@media (max-width: 768px) {
    .person-card .card-body {
        min-height: auto;
        padding: 15px;
    }

    .profile-img {
        width: 60px;
        height: 60px;
    }

    .detail-item {
        font-size: 13px;
    }
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .row.g-4 > * {
        margin-bottom: 1rem;
    }
}
</style>

<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('student/student_menu');?>">Student Menu</a></li>
    <li><a href="<?php echo site_url('student/Student_controller/index/');?>">Student Index</a></li>
    <li><a href="<?php echo site_url('student/Student_controller/addMoreStudentInfo/'.$student_uid);?>">Student Detail</a></li>
    <li>ID Cards</li>
</ul>
<hr>

<?php 
    $father_pic = base_url().'assets/img/icons/profile.png';
    $mother_pic = base_url().'assets/img/icons/profile.png';
    $guardian_pic = base_url().'assets/img/icons/profile.png';
    $guardian_pic_2 = base_url().'assets/img/icons/profile.png';
    $driver_pic = base_url().'assets/img/icons/profile.png';
    $driver_pic_2 = base_url().'assets/img/icons/profile.png';
    $father_name = '';
    $mother_name = '';
    $guardian_name = '';
    $guardian_name_2 = '';
    $driver_name = '';
    $driver_name_2 = ''; 
    $father_firstname = '';
    $mother_firstname = '';
    $guardian_firstname = '';
    $guardian_firstname_2 = '';
    $driver_firstname = '';
    $driver_firstname_2 = '';
    $father_lastname = '';
    $mother_lastname = '';
    $guardian_lastname = '';
    $guardian_lastname_2 = '';
    $driver_lastname = '';
    $driver_lastname_2 = '';
    $father_email = '';
    $mother_email = '';
    $guardian_email = '';
    $guardian_email_2 = '';
    $driver_email = '';
    $driver_email_2 = '';
    $father_contact_no = '';
    $mother_contact_no = '';
    $guardian_contact_no = '';
    $guardian_contact_no_2 = '';
    $driver_contact_no = '';
    $driver_contact_no_2 = '';
    $father_occupation = '';
    $mother_occupation = '';
    $guardian_occupation = '';
    $guardian_occupation_2 = '';
    $driver_occupation = '';
    $driver_occupation_2 = '';
    $father_home_city = '';
    $mother_home_city = '';
    $guardian_home_city = '';
    $guardian_home_city_2 = '';
    $driver_home_city = '';
    $driver_home_city_2 = '';
    $father_id=0;
    $mother_id=0;
    $guardian_id=0;
    $guardian_id_2=0;
    $driver_id=0;
    $driver_id_2=0;
    $father_rfid = '';
    $mother_rfid = '';
    $guardian_rfid = '';
    $guardian_2_rfid = '';
    $driver_rfid = '';
    $driver_2_rfid = '';
    $driver_dl_pic = '';
    $driver_dl_pic_2 = '';
    $sibling_id = 0;

    $has_student_idcard = false;
    $has_parent_idcard = false;
    $student_idcard_id = 0;
    $parent_idcard_id = 0;

    // Initialize entity status arrays
    $entity_status = array();
    $entity_ids = array();

    // Process entity data to map relation types to their status and IDs
    if (isset($entity_data) && is_array($entity_data)) {
        foreach ($entity_data as $entity) {
            if (isset($entity->avatar_type)) {
                $relation_type = strtolower($entity->avatar_type);
                if ($relation_type == 'student') {
                    $entity_status['student'] = array(
                        'is_active' => isset($entity->is_active) ? $entity->is_active : 0,
                        'status' => isset($entity->status) ? $entity->status : 'in review',
                        'id' => isset($entity->id) ? $entity->id : 0
                    );
                    $entity_ids['student'] = isset($entity->id) ? $entity->id : 0;
                } else if (in_array($relation_type, array('father', 'mother', 'guardian', 'driver'))) {
                    $entity_status[$relation_type] = array(
                        'is_active' => isset($entity->is_active) ? $entity->is_active : 0,
                        'status' => isset($entity->status) ? $entity->status : 'in review',
                        'id' => isset($entity->id) ? $entity->id : 0
                    );
                    $entity_ids[$relation_type] = isset($entity->id) ? $entity->id : 0;
                }
            }
        }
    }

    if (isset($id_card_data) && is_array($id_card_data)) {
        foreach ($id_card_data as $idc) {
            if (isset($idc->id_card_for)) {
                if ($idc->id_card_for == 'Student') {
                    $has_student_idcard = true;
                    $student_idcard_id = $idc->id;
                }
                if ($idc->id_card_for == 'Parent') {
                    $has_parent_idcard = true;
                    $parent_idcard_id = $idc->id;
                }
            }
        }
    }
?>

<div class="col-md-12">
        <div class="card cd_border">
            <div class="card-header panel_heading_new_style_staff_border">
                <div class="row" style="margin: 0px;">
                    <div class="d-flex justify-content-between" style="width:100%;">
                        <h3 class="card-title panel_title_new_style_staff">
                            <a class="back_anchor" href="<?php echo site_url('student/Student_controller/addMoreStudentInfo/'.$student_uid);?>">
                                <span class="fa fa-arrow-left"></span>
                            </a> 
                            ID Card details of <?php echo '<strong>' . ($name_to_caps?strtoupper($stdData->stdName):($stdData->stdName)) . '</strong> (Class: ' . $stdData->className . ' / Section: '.$stdData->sectionName. ') (Admission No: '. $stdData->admission_no.')'?>
                        </h3>   
                    </div>
                </div>
            </div>
            <?php $siblings = $sibling_data; 
            if($siblings){
                $sibling_id = $siblings->sibling_id;
            }?>           
            <div class="card-body">
                <div class="row g-4">
                    <!-- Student Card -->
                    <div class="col-lg-6 col-md-6 col-sm-12">
                        <div class="card h-100 person-card">
                            <div class="card-header bg-primary text-white">
                                <h4 class="mb-0"><i class="fa fa-user-graduate mr-2"></i>Student</h4>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <div class="row align-items-center mb-3">
                                    <div class="col-4">
                                        <div class="profile-img-container">
                                            <img src="<?= isset($stdData->picture_url) && $stdData->picture_url ? $this->filemanager->getFilePath($stdData->picture_url) : base_url().'assets/img/icons/profile.png'; ?>" alt="Student Photo" class="profile-img" onerror="this.onerror=null; this.src='<?= base_url().'assets/img/icons/profile.png'; ?>';">
                                        </div>
                                    </div>
                                    <div class="col-8">
                                        <div class="person-details">
                                            <p class="detail-item"><strong>Name:</strong> <?= $name_to_caps ? strtoupper($stdData->stdName) : $stdData->stdName; ?></p>
                                            <p class="detail-item"><strong>Admission No:</strong> <?= $stdData->admission_no; ?></p>
                                            <p class="detail-item"><strong>Class:</strong> <?= $stdData->className; ?> / Section: <?= $stdData->sectionName; ?></p>
                                            <p class="detail-item"><strong>ID Code:</strong> <?= $stdData->identification_code; ?></p>
                                        </div>
                                    </div>
                                </div>
                                <div style="text-align: center; margin-top: 15px;">
                                    <button class="btn btn-primary view-idcard-btn" onclick="view_id_card('Student', <?= $stdData->id ?>, <?= $student_idcard_id ?>)" <?= $has_student_idcard ? '' : 'disabled title="ID card not generated for this particular person."' ?>>
                                        <i class="fa fa-eye mr-1"></i>View ID Card
                                    </button>

                                    <?php
                                    $student_entity_exists = isset($entity_status['student']);
                                    $student_is_active = $student_entity_exists ? $entity_status['student']['is_active'] : 0;
                                    $student_entity_id = $student_entity_exists ? $entity_status['student']['id'] : 0;
                                    ?>

                                    <?php if ($student_entity_exists): ?>
                                        <?php if ($student_is_active == 1): ?>
                                            <button class="btn btn-warning btn-sm ml-2" onclick="toggleEntityStatus(<?= $stdData->id ?>, 0, 'student', <?= $stdData->id ?>, <?= $student_entity_id ?>)" title="Click to deactivate this ID card">
                                                <i class="fa fa-toggle-on mr-1"></i>Active
                                            </button>
                                        <?php else: ?>
                                            <button class="btn btn-success btn-sm ml-2" onclick="toggleEntityStatus(<?= $stdData->id ?>, 1, 'student', <?= $stdData->id ?>, <?= $student_entity_id ?>)" title="Click to activate this ID card">
                                                <i class="fa fa-toggle-off mr-1"></i>Inactive
                                            </button>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <button class="btn btn-secondary btn-sm ml-2" disabled title="No ID card entity found">
                                            <i class="fa fa-ban mr-1"></i>No Data
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Father Card -->
                    <div class="col-lg-6 col-md-6 col-sm-12 ">
                        <div class="card h-100 person-card">
                            <div class="card-header bg-success text-white">
                                <h4 class="mb-0"><i class="fa fa-male mr-2"></i>Father</h4>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <?php if(!empty($father_data)) {
                                    if($father_data->picture_url != '' || $father_data->picture_url != NULL) {
                                        $father_pic = $this->filemanager->getFilePath($father_data->picture_url);
                                    }
                                    $father_id = $father_data->id;
                                    $father_name = $father_data->name;
                                    $father_firstname = $father_data->first_name;
                                    $father_lastname = $father_data->last_name;
                                    $father_email = $father_data->email;
                                    $father_contact_no = $father_data->mobile_no;
                                    $father_occupation = $father_data->occupation;
                                    $father_home_city = $father_data->home_city;
                                    $father_rfid = $father_data->rfid_number;
                                ?>
                                    <div class="row align-items-center mb-3">
                                        <div class="col-4">
                                            <div class="profile-img-container">
                                                <img src="<?= !empty($father_pic) ? $father_pic : base_url().'assets/img/icons/profile.png'; ?>" alt="Father Photo" class="profile-img" onerror="this.onerror=null; this.src='<?= base_url().'assets/img/icons/profile.png'; ?>';">
                                            </div>
                                        </div>
                                        <div class="col-8">
                                            <div class="person-details">
                                                <p class="detail-item"><strong>Name:</strong> <?= $father_name; ?></p>
                                                <p class="detail-item"><strong>Email:</strong> <?= $father_email; ?></p>
                                                <p class="detail-item"><strong>Phone No:</strong> <?= $father_contact_no; ?></p>
                                                <p class="detail-item"><strong>RFID No:</strong> <?= $father_rfid ? $father_rfid : '-'?></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="text-align: center; margin-top: 15px;">
                                        <button class="btn btn-primary view-idcard-btn" onclick="view_id_card('Father', <?= $father_id ?>, <?= $parent_idcard_id ?>)" <?= $has_parent_idcard ? '' : 'disabled title="ID card not generated for this particular person."' ?>>
                                            <i class="fa fa-eye mr-1"></i>View ID Card
                                        </button>

                                        <?php
                                        $father_entity_exists = isset($entity_status['father']);
                                        $father_is_active = $father_entity_exists ? $entity_status['father']['is_active'] : 0;
                                        $father_entity_id = $father_entity_exists ? $entity_status['father']['id'] : 0;
                                        ?>

                                        <?php if ($father_entity_exists): ?>
                                            <?php if ($father_is_active == 1): ?>
                                                <button class="btn btn-warning btn-sm ml-2" onclick="toggleEntityStatus(<?= $father_id ?>, 0, 'father', <?= $stdData->id ?>, <?= $father_entity_id ?>)" title="Click to deactivate this ID card">
                                                    <i class="fa fa-toggle-on mr-1"></i>Active
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-success btn-sm ml-2" onclick="toggleEntityStatus(<?= $father_id ?>, 1, 'father', <?= $stdData->id ?>, <?= $father_entity_id ?>)" title="Click to activate this ID card">
                                                    <i class="fa fa-toggle-off mr-1"></i>Inactive
                                                </button>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <button class="btn btn-secondary btn-sm ml-2" disabled title="No ID card entity found">
                                                <i class="fa fa-ban mr-1"></i>No Data
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                <?php }else{ ?>
                                    <div class="no-data-container d-flex flex-column justify-content-center align-items-center h-100">
                                        <div class="no-data-icon mb-3">
                                            <i class="fa fa-user-plus fa-3x text-muted"></i>
                                        </div>
                                        <span class="no-data-display text-muted mb-3">Father details are not added</span>
                                        <div class="action-buttons">
                                            <button class="btn btn-warning mb-2" onclick="construct_data_fields('Father', <?= $father_id ?>)">
                                                <i class="fa fa-plus me-1"></i>Add
                                            </button>
                                            <?php if($siblings){ ?>
                                                <button class="btn btn-outline-warning" onclick="copy_from_sibling('Father')">
                                                    <i class="fa fa-copy me-1"></i>Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?>
                                                </button>
                                            <?php } ?>
                                        </div>
                                    </div>
                                <?php }?>
                            </div>
                        </div>
                    </div>
                    <!-- Mother Card -->
                    <div class="col-lg-6 col-md-6 col-sm-12 pt-5">
                        <div class="card h-100 person-card">
                            <div class="card-header bg-info text-white">
                                <h4 class="mb-0"><i class="fa fa-female mr-2"></i>Mother</h4>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <?php if(!empty($mother_data)) {
                                    if($mother_data->picture_url != '' || $mother_data->picture_url != NULL) {
                                        $mother_pic = $this->filemanager->getFilePath($mother_data->picture_url);
                                    }
                                    $mother_id = $mother_data->id;
                                    $mother_name = $mother_data->name;
                                    $mother_firstname = $mother_data->first_name;
                                    $mother_lastname = $mother_data->last_name;
                                    $mother_email = $mother_data->email;
                                    $mother_contact_no = $mother_data->mobile_no;
                                    $mother_occupation = $mother_data->occupation;
                                    $mother_home_city = $mother_data->home_city;
                                    $mother_rfid = $mother_data->rfid_number;
                                ?>
                                    <div class="row align-items-center mb-3">
                                        <div class="col-4">
                                            <div class="profile-img-container">
                                                <img src="<?= !empty($mother_pic) ? $mother_pic : base_url().'assets/img/icons/profile.png'; ?>" alt="Mother Photo" class="profile-img" onerror="this.onerror=null; this.src='<?= base_url().'assets/img/icons/profile.png'; ?>';">
                                            </div>
                                        </div>
                                        <div class="col-8">
                                            <div class="person-details">
                                                <p class="detail-item"><strong>Name:</strong> <?= $mother_name; ?></p>
                                                <p class="detail-item"><strong>Email:</strong> <?= $mother_email; ?></p>
                                                <p class="detail-item"><strong>Phone No:</strong> <?= $mother_contact_no; ?></p>
                                                <p class="detail-item"><strong>RFID No:</strong> <?= $mother_rfid? $mother_rfid:'-'; ?></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="text-align: center; margin-top: 15px;">
                                        <button class="btn btn-primary view-idcard-btn" onclick="view_id_card('Mother', <?= $mother_id ?>, <?= $parent_idcard_id ?>)" <?= $has_parent_idcard ? '' : 'disabled title="ID card not generated for this particular person."' ?>>
                                            <i class="fa fa-eye mr-1"></i>View ID Card
                                        </button>

                                        <?php
                                        $mother_entity_exists = isset($entity_status['mother']);
                                        $mother_is_active = $mother_entity_exists ? $entity_status['mother']['is_active'] : 0;
                                        $mother_entity_id = $mother_entity_exists ? $entity_status['mother']['id'] : 0;
                                        ?>

                                        <?php if ($mother_entity_exists): ?>
                                            <?php if ($mother_is_active == 1): ?>
                                                <button class="btn btn-warning btn-sm ml-2" onclick="toggleEntityStatus(<?= $mother_id ?>, 0, 'mother', <?= $stdData->id ?>, <?= $mother_entity_id ?>)" title="Click to deactivate this ID card">
                                                    <i class="fa fa-toggle-on mr-1"></i>Active
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-success btn-sm ml-2" onclick="toggleEntityStatus(<?= $mother_id ?>, 1, 'mother', <?= $stdData->id ?>, <?= $mother_entity_id ?>)" title="Click to activate this ID card">
                                                    <i class="fa fa-toggle-off mr-1"></i>Inactive
                                                </button>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <button class="btn btn-secondary btn-sm ml-2" disabled title="No ID card entity found">
                                                <i class="fa fa-ban mr-1"></i>No Data
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                <?php } else{?>
                                    <div class="no-data-container d-flex flex-column justify-content-center align-items-center h-100">
                                        <div class="no-data-icon mb-3">
                                            <i class="fa fa-user-plus fa-3x text-muted"></i>
                                        </div>
                                        <span class="no-data-display text-muted mb-3">Mother details are not added</span>
                                        <div class="action-buttons">
                                            <button class="btn btn-warning mb-2" onclick="construct_data_fields('Mother', <?= $mother_id ?>)">
                                                <i class="fa fa-plus mr-1"></i>Add
                                            </button>
                                            <?php if($siblings){ ?>
                                                <button class="btn btn-outline-warning" onclick="copy_from_sibling('Mother')">
                                                    <i class="fa fa-copy mr-1"></i>Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?>
                                                </button>
                                            <?php } ?>
                                        </div>
                                    </div>
                                <?php }?>
                            </div>
                        </div>
                    </div>
                    <!-- Guardian Card -->
                    <div class="col-lg-6 col-md-6 col-sm-12 pt-5">
                        <div class="card h-100 person-card">
                            <div class="card-header bg-warning text-dark">
                                <h4 class="mb-0"><i class="fa fa-user-shield mr-2"></i>Guardian</h4>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <?php if(!empty($guardian_data)) {
                                    if($guardian_data->picture_url != '' || $guardian_data->picture_url != NULL) {
                                        $guardian_pic = $this->filemanager->getFilePath($guardian_data->picture_url);
                                    }
                                    $guardian_id = $guardian_data->id;
                                    $guardian_name = $guardian_data->name;
                                    $guardian_firstname = $guardian_data->first_name;
                                    $guardian_lastname = $guardian_data->last_name;
                                    $guardian_email = $guardian_data->email;
                                    $guardian_contact_no = $guardian_data->mobile_no;
                                    $guardian_occupation = $guardian_data->occupation;
                                    $guardian_home_city = $guardian_data->home_city;
                                    $guardian_rfid = $guardian_data->rfid_number;
                                ?>
                                    <div class="row align-items-center mb-3">
                                        <div class="col-4">
                                            <div class="profile-img-container">
                                                <img src="<?= !empty($guardian_pic) ? $guardian_pic : base_url().'assets/img/icons/profile.png'; ?>" alt="Guardian Photo" class="profile-img" onerror="this.onerror=null; this.src='<?= base_url().'assets/img/icons/profile.png'; ?>';">
                                            </div>
                                        </div>
                                        <div class="col-8">
                                            <div class="person-details">
                                                <p class="detail-item"><strong>Name:</strong> <?= $guardian_name; ?></p>
                                                <p class="detail-item"><strong>Email:</strong> <?= $guardian_email?$guardian_email:'-'; ?></p>
                                                <p class="detail-item"><strong>Phone No:</strong> <?= $guardian_contact_no; ?></p>
                                                <p class="detail-item"><strong>RFID No:</strong> <?= $guardian_rfid?$guardian_rfid:'-'; ?></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="text-align: center; margin-top: 15px;">
                                        <button class="btn btn-primary view-idcard-btn" onclick="view_id_card('Guardian', <?= $guardian_id ?>, <?= $parent_idcard_id ?>)" <?= $has_parent_idcard ? '' : 'disabled title="ID card not generated for this particular person."' ?>>
                                            <i class="fa fa-eye mr-1"></i>View ID Card
                                        </button>

                                        <?php
                                        $guardian_entity_exists = isset($entity_status['guardian']);
                                        $guardian_is_active = $guardian_entity_exists ? $entity_status['guardian']['is_active'] : 0;
                                        $guardian_entity_id = $guardian_entity_exists ? $entity_status['guardian']['id'] : 0;
                                        ?>

                                        <?php if ($guardian_entity_exists): ?>
                                            <?php if ($guardian_is_active == 1): ?>
                                                <button class="btn btn-warning btn-sm ml-2" onclick="toggleEntityStatus(<?= $guardian_id ?>, 0, 'guardian', <?= $stdData->id ?>, <?= $guardian_entity_id ?>)" title="Click to deactivate this ID card">
                                                    <i class="fa fa-toggle-on mr-1"></i>Active
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-success btn-sm ml-2" onclick="toggleEntityStatus(<?= $guardian_id ?>, 1, 'guardian', <?= $stdData->id ?>, <?= $guardian_entity_id ?>)" title="Click to activate this ID card">
                                                    <i class="fa fa-toggle-off mr-1"></i>Inactive
                                                </button>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <button class="btn btn-secondary btn-sm ml-2" disabled title="No ID card entity found">
                                                <i class="fa fa-ban mr-1"></i>No Data
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                <?php } else{?>
                                    <div class="no-data-container d-flex flex-column justify-content-center align-items-center h-100">
                                        <div class="no-data-icon mb-3">
                                            <i class="fa fa-user-plus fa-3x text-muted"></i>
                                        </div>
                                        <span class="no-data-display text-muted mb-3">Guardian details are not added</span>
                                        <div class="action-buttons">
                                            <button class="btn btn-warning mb-2" onclick="construct_data_fields('Guardian', <?= $guardian_id ?>)">
                                                <i class="fa fa-plus me-1"></i>Add
                                            </button>
                                            <?php if($siblings){ ?>
                                                <button class="btn btn-outline-warning" onclick="copy_from_sibling('Guardian')">
                                                    <i class="fa fa-copy me-1"></i>Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?>
                                                </button>
                                            <?php } ?>
                                        </div>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <!-- Guardian 2 Card -->
                    <div class="col-lg-6 col-md-6 col-sm-12 pt-5">
                        <div class="card h-100 person-card">
                            <div class="card-header bg-secondary text-white">
                                <h4 class="mb-0"><i class="fa fa-user-shield mr-2"></i>Guardian 2</h4>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <?php if(!empty($guardian_2_data)) {
                                    if($guardian_2_data->picture_url != '' || $guardian_2_data->picture_url != NULL) {
                                        $guardian_pic_2 = $this->filemanager->getFilePath($guardian_2_data->picture_url);
                                    }
                                    $guardian_id_2 = $guardian_2_data->id;
                                    $guardian_name_2 = $guardian_2_data->name;
                                    $guardian_firstname_2 = $guardian_2_data->first_name;
                                    $guardian_lastname_2 = $guardian_2_data->last_name;
                                    $guardian_email_2 = $guardian_2_data->email;
                                    $guardian_contact_no_2 = $guardian_2_data->mobile_no;
                                    $guardian_occupation_2 = $guardian_2_data->occupation;
                                    $guardian_home_city_2 = $guardian_2_data->home_city;
                                    $guardian_2_rfid = $guardian_2_data->rfid_number;
                                ?>
                                    <div class="row align-items-center mb-3">
                                        <div class="col-4">
                                            <div class="profile-img-container">
                                                <img src="<?= !empty($guardian_pic_2) ? $guardian_pic_2 : base_url().'assets/img/icons/profile.png'; ?>" alt="Guardian 2 Photo" class="profile-img" onerror="this.onerror=null; this.src='<?= base_url().'assets/img/icons/profile.png'; ?>';">
                                            </div>
                                        </div>
                                        <div class="col-8">
                                            <div class="person-details">
                                                <p class="detail-item"><strong>Name:</strong> <?= $guardian_name_2; ?></p>
                                                <p class="detail-item"><strong>Email:</strong> <?= $guardian_email_2?$guardian_email_2:'-'; ?></p>
                                                <p class="detail-item"><strong>Phone No:</strong> <?= $guardian_contact_no_2; ?></p>
                                                <p class="detail-item"><strong>RFID No:</strong> <?= $guardian_2_rfid?$guardian_2_rfid:'-'; ?></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="text-align: center; margin-top: 15px;">
                                        <button class="btn btn-primary view-idcard-btn" onclick="view_id_card('Guardian_2', <?= $guardian_id_2 ?>, <?= $parent_idcard_id ?>)" <?= $has_parent_idcard ? '' : 'disabled title="ID card not generated for this particular person."' ?>>
                                            <i class="fa fa-eye mr-1"></i>View ID Card
                                        </button>

                                        <?php
                                        // Note: Guardian_2 might not have separate entity data, using guardian for now
                                        $guardian2_entity_exists = isset($entity_status['guardian']);
                                        $guardian2_is_active = $guardian2_entity_exists ? $entity_status['guardian']['is_active'] : 0;
                                        $guardian2_entity_id = $guardian2_entity_exists ? $entity_status['guardian']['id'] : 0;
                                        ?>

                                        <?php if ($guardian2_entity_exists): ?>
                                            <?php if ($guardian2_is_active == 1): ?>
                                                <button class="btn btn-warning btn-sm ml-2" onclick="toggleEntityStatus(<?= $guardian_id_2 ?>, 0, 'guardian', <?= $stdData->id ?>, <?= $guardian2_entity_id ?>)" title="Click to deactivate this ID card">
                                                    <i class="fa fa-toggle-on mr-1"></i>Active
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-success btn-sm ml-2" onclick="toggleEntityStatus(<?= $guardian_id_2 ?>, 1, 'guardian', <?= $stdData->id ?>, <?= $guardian2_entity_id ?>)" title="Click to activate this ID card">
                                                    <i class="fa fa-toggle-off mr-1"></i>Inactive
                                                </button>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <button class="btn btn-secondary btn-sm ml-2" disabled title="No ID card entity found">
                                                <i class="fa fa-ban mr-1"></i>No Data
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                <?php } else{?>
                                    <div class="no-data-container d-flex flex-column justify-content-center align-items-center h-100">
                                        <div class="no-data-icon mb-3">
                                            <i class="fa fa-user-plus fa-3x text-muted"></i>
                                        </div>
                                        <span class="no-data-display text-muted mb-3">Guardian 2 details are not added</span>
                                        <div class="action-buttons">
                                            <button class="btn btn-warning mb-2" onclick="construct_data_fields('Guardian_2', <?= $guardian_id_2 ?>)">
                                                <i class="fa fa-plus me-1"></i>Add
                                            </button>
                                            <?php if($siblings){ ?>
                                                <button class="btn btn-outline-warning" onclick="copy_from_sibling('Guardian_2')">
                                                    <i class="fa fa-copy me-1"></i>Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?>
                                                </button>
                                            <?php } ?>
                                        </div>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <!-- Driver Card -->
                    <div class="col-lg-6 col-md-6 col-sm-12 pt-5">
                        <div class="card h-100 person-card">
                            <div class="card-header bg-dark text-white">
                                <h4 class="mb-0"><i class="fa fa-car mr-2"></i>Driver</h4>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <?php if(!empty($driver_data)) {
                                    if($driver_data->picture_url != '' || $driver_data->picture_url != NULL) {
                                        $driver_pic = $this->filemanager->getFilePath($driver_data->picture_url);
                                    }
                                    if($driver_data->dl_file != '' || $driver_data->dl_file != NULL) {
                                        $driver_dl_pic = $this->filemanager->getFilePath($driver_data->dl_file);
                                    }
                                    $driver_id = $driver_data->id;
                                    $driver_name = $driver_data->name;
                                    $driver_firstname = $driver_data->first_name;
                                    $driver_lastname = $driver_data->last_name;
                                    $driver_dl = $driver_data->dl_number;
                                    $driver_contact_no = $driver_data->mobile_no;
                                    $driver_occupation = $driver_data->occupation;
                                    $driver_home_city = $driver_data->home_city;
                                    $driver_rfid = $driver_data->rfid_number;
                                ?>
                                    <div class="row align-items-center mb-3">
                                        <div class="col-4">
                                            <div class="profile-img-container">
                                                <img src="<?= !empty($driver_pic) ? $driver_pic : base_url().'assets/img/icons/profile.png'; ?>" alt="Driver Photo" class="profile-img" onerror="this.onerror=null; this.src='<?= base_url().'assets/img/icons/profile.png'; ?>';">
                                            </div>
                                        </div>
                                        <div class="col-8">
                                            <div class="person-details">
                                                <p class="detail-item"><strong>Name:</strong> <?= $driver_name; ?></p>
                                                <p class="detail-item"><strong>DL Number:</strong> <?= $driver_dl?$driver_dl:'-'; ?></p>
                                                <p class="detail-item"><strong>Phone No:</strong> <?= $driver_contact_no; ?></p>
                                                <p class="detail-item"><strong>RFID No:</strong> <?= $driver_rfid?$driver_rfid:'-'; ?></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="text-align: center; margin-top: 15px;">
                                        <button class="btn btn-primary view-idcard-btn" onclick="view_id_card('Driver', <?= $driver_id ?>, <?= $parent_idcard_id ?>)" <?= $has_parent_idcard ? '' : 'disabled title="ID card not generated for this particular person."' ?>>
                                            <i class="fa fa-eye mr-1"></i>View ID Card
                                        </button>

                                        <?php
                                        $driver_entity_exists = isset($entity_status['driver']);
                                        $driver_is_active = $driver_entity_exists ? $entity_status['driver']['is_active'] : 0;
                                        $driver_entity_id = $driver_entity_exists ? $entity_status['driver']['id'] : 0;
                                        ?>

                                        <?php if ($driver_entity_exists): ?>
                                            <?php if ($driver_is_active == 1): ?>
                                                <button class="btn btn-warning btn-sm ml-2" onclick="toggleEntityStatus(<?= $driver_id ?>, 0, 'driver', <?= $stdData->id ?>, <?= $driver_entity_id ?>)" title="Click to deactivate this ID card">
                                                    <i class="fa fa-toggle-on mr-1"></i>Active
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-success btn-sm ml-2" onclick="toggleEntityStatus(<?= $driver_id ?>, 1, 'driver', <?= $stdData->id ?>, <?= $driver_entity_id ?>)" title="Click to activate this ID card">
                                                    <i class="fa fa-toggle-off mr-1"></i>Inactive
                                                </button>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <button class="btn btn-secondary btn-sm ml-2" disabled title="No ID card entity found">
                                                <i class="fa fa-ban mr-1"></i>No Data
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                <?php } else{?>
                                    <div class="no-data-container d-flex flex-column justify-content-center align-items-center h-100">
                                        <div class="no-data-icon mb-3">
                                            <i class="fa fa-user-plus fa-3x text-muted"></i>
                                        </div>
                                        <span class="no-data-display text-muted mb-3">Driver details are not added</span>
                                        <div class="action-buttons">
                                            <button class="btn btn-warning mb-2" onclick="construct_data_fields('Driver', <?= $driver_id ?>)">
                                                <i class="fa fa-plus me-1"></i>Add
                                            </button>
                                            <?php if($siblings){ ?>
                                                <button class="btn btn-outline-warning" onclick="copy_from_sibling('Driver')">
                                                    <i class="fa fa-copy me-1"></i>Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?>
                                                </button>
                                            <?php } ?>
                                        </div>
                                    </div>
                                <?php }?>
                            </div>
                        </div>
                    </div>
                    <!-- Driver 2 Card -->
                    <div class="col-lg-6 col-md-6 col-sm-12 pt-5">
                        <div class="card h-100 person-card">
                            <div class="card-header bg-danger text-white">
                                <h4 class="mb-0"><i class="fa fa-car mr-2"></i>Driver 2</h4>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <?php if(!empty($driver_2_data)) {
                                    if($driver_2_data->picture_url != '' || $driver_2_data->picture_url != NULL) {
                                        $driver_pic_2 = $this->filemanager->getFilePath($driver_2_data->picture_url);
                                    }
                                    if($driver_2_data->dl_file != '' || $driver_2_data->dl_file != NULL) {
                                        $driver_dl_pic_2 = $this->filemanager->getFilePath($driver_2_data->dl_file);
                                    }
                                    $driver_id_2 = $driver_2_data->id;
                                    $driver_name_2 = $driver_2_data->name;
                                    $driver_firstname_2 = $driver_2_data->first_name;
                                    $driver_lastname_2 = $driver_2_data->last_name;
                                    $driver_dl_2 = $driver_2_data->dl_number;
                                    $driver_contact_no_2 = $driver_2_data->mobile_no;
                                    $driver_occupation_2 = $driver_2_data->occupation;
                                    $driver_home_city_2 = $driver_2_data->home_city;
                                    $driver_2_rfid = $driver_2_data->rfid_number;
                                ?>
                                    <div class="row align-items-center mb-3">
                                        <div class="col-4">
                                            <div class="profile-img-container">
                                                <img src="<?= !empty($driver_pic_2) ? $driver_pic_2 : base_url().'assets/img/icons/profile.png'; ?>" alt="Driver 2 Photo" class="profile-img" onerror="this.onerror=null; this.src='<?= base_url().'assets/img/icons/profile.png'; ?>';">
                                            </div>
                                        </div>
                                        <div class="col-8">
                                            <div class="person-details">
                                                <p class="detail-item"><strong>Name:</strong> <?= $driver_name_2; ?></p>
                                                <p class="detail-item"><strong>DL Number:</strong> <?= $driver_dl_2?$driver_dl_2:'-'; ?></p>
                                                <p class="detail-item"><strong>Phone No:</strong> <?= $driver_contact_no_2; ?></p>
                                                <p class="detail-item"><strong>RFID No:</strong> <?= $driver_2_rfid?$driver_2_rfid:'-'; ?></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div style="text-align: center; margin-top: 15px;">
                                        <button class="btn btn-primary view-idcard-btn" onclick="view_id_card('Driver_2', <?= $driver_id_2 ?>, <?= $parent_idcard_id ?>)" <?= $has_parent_idcard ? '' : 'disabled title="ID card not generated for this particular person."' ?>>
                                            <i class="fa fa-eye mr-1"></i>View ID Card
                                        </button>

                                        <?php
                                        // Note: Driver_2 might not have separate entity data, using driver for now
                                        $driver2_entity_exists = isset($entity_status['driver']);
                                        $driver2_is_active = $driver2_entity_exists ? $entity_status['driver']['is_active'] : 0;
                                        $driver2_entity_id = $driver2_entity_exists ? $entity_status['driver']['id'] : 0;
                                        ?>

                                        <?php if ($driver2_entity_exists): ?>
                                            <?php if ($driver2_is_active == 1): ?>
                                                <button class="btn btn-warning btn-sm ml-2" onclick="toggleEntityStatus(<?= $driver_id_2 ?>, 0, 'driver', <?= $stdData->id ?>, <?= $driver2_entity_id ?>)" title="Click to deactivate this ID card">
                                                    <i class="fa fa-toggle-on mr-1"></i>Active
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-success btn-sm ml-2" onclick="toggleEntityStatus(<?= $driver_id_2 ?>, 1, 'driver', <?= $stdData->id ?>, <?= $driver2_entity_id ?>)" title="Click to activate this ID card">
                                                    <i class="fa fa-toggle-off mr-1"></i>Inactive
                                                </button>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <button class="btn btn-secondary btn-sm ml-2" disabled title="No ID card entity found">
                                                <i class="fa fa-ban mr-1"></i>No Data
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                <?php } else{?>
                                    <div class="no-data-container d-flex flex-column justify-content-center align-items-center h-100">
                                        <div class="no-data-icon mb-3">
                                            <i class="fa fa-user-plus fa-3x text-muted"></i>
                                        </div>
                                        <span class="no-data-display text-muted mb-3">Driver 2 details are not added</span>
                                        <div class="action-buttons">
                                            <button class="btn btn-warning mb-2" onclick="construct_data_fields('Driver_2', <?= $driver_id_2 ?>)">
                                                <i class="fa fa-plus me-1"></i>Add
                                            </button>
                                            <?php if($siblings){ ?>
                                                <button class="btn btn-outline-warning" onclick="copy_from_sibling('Driver_2')">
                                                    <i class="fa fa-copy me-1"></i>Copy From <?= ($name_to_caps?strtoupper($siblings->first_name):($siblings->first_name))  ?>
                                                </button>
                                            <?php } ?>
                                        </div>
                                    </div>
                                <?php }?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>

<form id="det_form">
    <div class="modal modal-fade" tabindex="-1" id="form_modal" style="width:50%;margin-left:25%;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" >
                <div class="modal-header">
                    <h3 class="modal-title"><strong>Add/Edit <span id="disp_name_det"></span> of <?php echo '' . ($name_to_caps?strtoupper($stdData->stdName):($stdData->stdName)) . ''?></strong></h3>
                    <button type="button" class="btn-close fa fa-times" style="font-size:20px"></button>
                </div>
                <div class="modal-body col-md-12" style="height: 40vh;overflow:scroll;">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-close" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="store_relation_data()">Save changes</button>
                </div>
            </div>
        </div>
    </div>
</form>

<form id="photo_form">
    <div class="modal modal-fade" tabindex="-1" id="photo_modal" style="width: 30%; margin-left:40%;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title"><strong>Add/Edit Photo for <span id="disp_name"></span></strong></h3>
                    <button type="button" class="btn-close fa fa-times" style="font-size:20px"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="relation_type_id">
                    <input type="hidden" id="relation_type">
                    <input type="file" accept="image/*" name="photo_url" id="photo_url" title="Browse file" style="left: -150.8px; top: 11.425px;">
                    <span class="no_photo" style="display: none; color:red">Select Photo to upload</span>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="photo_btn" onclick="save_photo()">Upload Photo</button>
                </div>
            </div>
        </div>
    </div>
</form>

<div class="modal modal-fade" tabindex="-1" id="rfid_modal" style="width: 30%; margin-left:40%;">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title"><strong>Add/Edit RFID for <span id="disp_name_rfid"></span></strong></h3>
                <button type="button" class="btn-close fa fa-times" style="font-size:20px"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6 col-md-offset-3">
                        <div class="col-md-12 col-xs-12">                                            
                            <div class="input-group">
                                <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
                                <input type="text" class="form-control rfid_txtbox">
                            </div>
                            <span class="no_rfid" style="display: none; color:red">Enter RFID</span>
                        </div>
                    </div>
                    <div class="col-md-1 delIcon"><span class="fa fa-trash-o" style="display: none;"></span></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="photo_btn" onclick="store_rfid()">Submit RFID</button>
            </div>
        </div>
    </div>
</div>

<form id="dl_form">
    <div class="modal modal-fade" tabindex="-1" id="dl_modal" style="width: 30%; margin-left:40%;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title"><strong>Add/Edit DL file for <span id="disp_name_dl"></span></strong></h3>
                    <button type="button" class="btn-close fa fa-times" style="font-size:20px"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="relation_type_id">
                    <input type="hidden" id="relation_type">
                    <input type="file" accept="image/*" name="dl_url" id="dl_url" title="Browse file" style="left: -150.8px; top: 11.425px;">
                    <span class="no_dl" style="display: none; color:red">Select Photo to upload</span>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="dl_btn" onclick="save_dl()">Upload Photo</button>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- ID Card Preview Modal - Exact copy from orderDetailsVerification.php -->
<div id="idCardModal" class="id-card-modal">
    <div class="id-card-modal-content">
        <!-- Close button at the top right corner -->
        <span class="id-card-close">&times;</span>
        <h3 id="idCardModalTitle" class="my-2">ID Card Preview</h3>

        <!-- Modal content loading indicator -->
        <div id="modalLoadingIndicator" style="display: none;">
            <div class="modal-loading-spinner">
                <i class="fa fa-spinner fa-spin fa-3x"></i>
                <p>Loading ID card data...</p>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <div class="row" id="cardPreviewRow">
                    <div class="col-md-6 text-center mb-4">
                        <h5 class="mb-3">Front</h5>
                        <div id="frontCardPreview" class="card-preview mx-auto">
                            <!-- Front preview will be rendered here -->
                        </div>
                    </div>
                    <div class="col-md-6 text-center mb-4">
                        <h5 class="mb-3">Back</h5>
                        <div id="backCardPreview" class="card-preview mx-auto">
                            <!-- Back preview will be rendered here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Bar Container (hidden by default) -->
        <div id="approvalProgressContainer" class="approval-progress-container" style="display: none;">
            <div class="progress-header">
                <h5><i class="fa fa-check-circle text-success"></i> Approving ID Card...</h5>
            </div>
            <div class="progress mb-3">
                <div id="approvalProgressBar" class="progress-bar progress-bar-striped progress-bar-animated bg-success"
                     role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                    <span id="progressText">0%</span>
                </div>
            </div>
            <div id="progressStatus" class="text-center text-muted">
                <small>Initializing approval process...</small>
            </div>
        </div>

        <!-- Action Button Container -->
        <div class="id-card-actions">
            <button id="approveBtn" class="id-card-action-btn id-card-approve" disabled onclick="approveIdCard()">
                <i class="fa fa-spinner fa-spin"></i> LOADING...
            </button>
            <!-- Success message (hidden by default) -->
            <div id="approvalSuccess" class="approval-success-message" style="display: none;">
                <i class="fa fa-check-circle text-success"></i>
                <span class="text-success font-weight-bold">ID Card Approved Successfully!</span>
            </div>
        </div>
    </div>
</div>

<style>
   
    .img-container {
        display: flex;
        justify-content: left; 
        align-items: baseline; 
        height: 150px; 
        overflow: hidden; 
    }

    .img-container img {
        max-height: 100%;
        width: auto;
        max-width: 100%;
    }

    /* ID Card Modal Styles - Exact copy from orderDetailsVerification.php */
    .id-card-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 1000;
        overflow: auto;
    }

    .id-card-modal-content {
        background-color: #fff;
        margin: 2% auto;
        padding: 20px;
        border-radius: 8px;
        width: 90%;
        max-width: 900px;
        position: relative;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        overflow-y: auto;
        max-height: 90vh;
        display: flex;
        flex-direction: column;
    }

    .id-card-close {
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        z-index: 20; /* Ensure it's above other elements */
        width: 30px;
        height: 30px;
        text-align: center;
        line-height: 30px;
        background-color: #f8f9fa;
        border-radius: 50%;
        border: 1px solid #dee2e6;
        transition: all 0.2s ease;
    }

    .id-card-close:hover {
        background-color: #e9ecef;
        border-color: #ced4da;
        color: #dc3545; /* Red color on hover */
    }

    .id-card-preview-container {
        display: flex;
        justify-content: center;
        gap: 30px;
        margin: 20px 0;
        flex-wrap: wrap;
    }

    .id-card-preview-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 20px;
    }

    .status-button {
        display: inline-flex;
        align-items: center;
        background-color: #f3f3f3;
        border: 1px solid #d0d0d0;
        border-radius: 16px;
        padding: 4px 12px;
        font-family: Arial, sans-serif;
        font-size: 14px;
        color: #666;
    }

    .status-button .dot {
        width: 8px;
        height: 8px;
        background-color: #666;
        border-radius: 50%;
        margin-right: 8px;
    }

    .card-preview {
        border: 1px solid #ccc;
        position: relative;
        overflow: hidden;
        margin: 0 auto;
        background: #fff;
        width: 215px;
        height: 335px;
        display: block;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
    }

    .card-size-portrait {
        width: 215px;
        height: 335px;
    }

    .card-size-landscape {
        width: 335px;
        height: 215px;
    }

    .element {
        position: absolute;
    }

    .element-content {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .loading-indicator {
        text-align: center;
        padding: 40px;
        color: #666;
    }

    .loading-indicator .spinner-border {
        margin-bottom: 15px;
    }

    .modal-loading-spinner {
        text-align: center;
        padding: 40px;
        color: #666;
    }

    .modal-loading-spinner i {
        margin-bottom: 15px;
        color: #007bff;
    }

    .modal-loading-spinner p {
        margin: 0;
        font-size: 16px;
    }

    .preview-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #666;
    }

    .preview-loading i {
        margin-bottom: 10px;
    }

    .id-card-actions {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        padding: 15px 0;
        border-top: 1px solid #eee;
        width: 100%;
    }

    .id-card-action-btn {
        min-width: 200px;
        padding: 12px 24px;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.2s ease;
        border-radius: 6px;
        position: relative;
    }

    /* Progress Bar Styles */
    .approval-progress-container {
        margin: 20px 0;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .progress-header {
        text-align: center;
        margin-bottom: 15px;
    }

    .progress-header h5 {
        margin: 0;
        color: #28a745;
        font-weight: 600;
    }

    .progress {
        height: 25px;
        border-radius: 12px;
        background-color: #e9ecef;
        overflow: hidden;
    }

    .progress-bar {
        font-size: 12px;
        font-weight: 600;
        line-height: 25px;
        transition: width 0.3s ease;
    }

    #progressStatus {
        margin-top: 10px;
        font-size: 13px;
    }

    /* Success Message Styles */
    .approval-success-message {
        text-align: center;
        padding: 15px;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 6px;
        color: #155724;
    }

    .approval-success-message i {
        font-size: 18px;
        margin-right: 8px;
    }

    .id-card-approve {
        background-color: #28a745;
        color: white;
    }

    .id-card-approve:hover:not(:disabled) {
        background-color: #218838;
        color: white;
    }

    .id-card-modify {
        background-color: #ffc107;
        color: #212529;
    }

    .id-card-modify:hover:not(:disabled) {
        background-color: #e0a800;
        color: #212529;
    }

    .id-card-remove {
        background-color: #dc3545;
        color: white;
    }

    .id-card-remove:hover:not(:disabled) {
        background-color: #c82333;
        color: white;
    }

    /* Active state for all buttons */
    .id-card-action-btn:active {
        background-color: #343a40 !important;
        color: white !important;
    }

    /* Disabled state for all buttons */
    .id-card-action-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background-color: #f8f9fa !important;
        color: #6c757d !important;
        position: relative;
    }

    /* Add a diagonal line through disabled buttons */
    .id-card-action-btn:disabled::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            45deg,
            transparent 40%,
            #ccc 40%,
            #ccc 60%,
            transparent 60%
        );
        border-radius: 4px;
    }

    /* Status badge styles */
    .status-approved {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }

    .status-in-review {
        background-color: #fff3cd;
        border-color: #ffeaa7;
        color: #856404;
    }

    .status-rejected {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }

    .status-modified {
        background-color: #d1ecf1;
        border-color: #bee5eb;
        color: #0c5460;
    }

    /* Single card layout - center the single card */
    .single-card-layout .col-md-6:last-child {
        display: none;
    }

    .single-card-layout .col-md-6:first-child {
        flex: 0 0 50%;
        max-width: 50%;
        margin: 0 auto;
    }

    /* Responsive adjustments for modal */
    @media (max-width: 768px) {
        .id-card-modal-content {
            width: 95%;
            margin: 5% auto;
            padding: 15px;
        }

        .card-preview {
            width: 180px;
            height: 280px;
        }

        .id-card-action-btn {
            min-width: 180px;
            padding: 10px 20px;
            font-size: 13px;
        }

        .approval-progress-container {
            padding: 15px;
        }

        .progress {
            height: 20px;
        }

        .col-md-6 {
            margin-bottom: 20px;
        }
    }

    .card-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        text-align: center;
    }



    .element {
        position: absolute;
    }

    .element-content {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: visible;
    }

    .shape-circle {
        border-radius: 50%;
    }

    img.element-content {
        object-fit: cover;
        max-width: 100%;
        max-height: 100%;
    }

    .shape-container {
        position: relative;
        overflow: hidden;
    }

    /* Enhanced card preview alignment and spacing */
    #cardPreviewRow {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        gap: 30px;
    }

    #cardPreviewRow .col-md-6 {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
    }

    #cardPreviewRow h5 {
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        font-size: 18px;
    }

    /* Status button styles - Target Design */
    .status-button {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        text-transform: capitalize;
        letter-spacing: 0.3px;
        border: none;
        cursor: default;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .status-button .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        flex-shrink: 0;
    }

    /* Status colors - Target Design */
    .status-button.in-review {
        background-color: #ffc107;
        color: #212529;
        border: 1px solid #ffc107;
    }

    .status-button.in-review .dot {
        background-color: #212529;
    }

    .status-button.approved {
        background-color: #28a745;
        color: white;
        border: 1px solid #28a745;
    }

    .status-button.approved .dot {
        background-color: white;
    }

    .status-button.removed {
        background-color: #dc3545;
        color: white;
        border: 1px solid #dc3545;
    }

    .status-button.removed .dot {
        background-color: white;
    }

    /* Enhanced card container styling */
    .card {
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border: 1px solid #e0e0e0;
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
        border-radius: 12px 12px 0 0 !important;
    }

    .card-body {
        background-color: #fff;
        border-radius: 0 0 12px 12px;
    }



    .loading-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        z-index: 1000;
        background: rgba(255, 255, 255, 0.9);
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .loading-indicator p {
        margin-top: 15px;
        color: #666;
        font-weight: 500;
    }

    .preview-loading {
        text-align: center;
        padding: 40px;
        color: #666;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .preview-loading i {
        margin-bottom: 15px;
    }



    .card-size-custom {
        margin: 0 auto;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        position: relative;
        display: block;
    }

    /* Ensure all card sizes maintain consistent alignment */
    .card-size-portrait,
    .card-size-landscape,
    .card-size-custom {
        background: #ffffff;
        border-radius: 4px;
        overflow: hidden;
    }

    /* Element styles */
    .element {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .element-content {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        box-sizing: border-box;
    }

   
    .photo-in-circle {
        border-radius: 50%;
    }

    .photo-in-shape {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
    }

    /* Text element alignment */
    .element[data-type="text"] .element-content {
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    /* Field element alignment */
    .element[data-type="field"] .element-content {
        align-items: center;
        justify-content: center;
        text-align: center;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    /* Shape element alignment */
    .element[data-type="shape"] .element-content {
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    /* Image element alignment */
    .element[data-type="image"] .element-content {
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    /* Responsive design - maintain consistent alignment */
    @media (max-width: 768px) {
        .id-card-modal-content {
            width: 95%;
            margin: 5% auto;
            padding: 15px;
        }

        .card-preview {
            width: 215px;
            height: 335px;
        }

        #cardPreviewRow {
            flex-direction: column;
            gap: 20px;
        }

        #cardPreviewRow .col-md-6 {
            width: 100%;
            margin-bottom: 20px;
        }



        .card-size-portrait,
        .card-size-landscape {
            transform: scale(0.8);
            transform-origin: center;
            margin: 0 auto;
        }
    }

    @media (max-width: 480px) {
        .card-preview {
            width: 180px;
            height: 280px;
        }

        .card-size-portrait {
            width: 180px;
            height: 280px;
        }

        .card-size-landscape {
            width: 280px;
            height: 180px;
        }

        .card-body {
            padding: 15px;
        }

        #cardPreviewRow {
            gap: 15px;
        }

        #cardPreviewRow h5 {
            font-size: 16px;
            margin-bottom: 10px;
        }
    }

    /* Action Buttons Container - Target Design */
    .action-buttons-container {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 15px;
        padding: 20px 0;
        border-top: 1px solid #e9ecef;
        margin-top: 20px;
        width: 100%;
    }

    /* Action buttons styling - Target design */
    .id-card-action-btn {
        min-width: 120px;
        height: 40px;
        font-weight: 500;
        font-size: 14px;
        border-radius: 4px;
        padding: 8px 20px;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .id-card-action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .id-card-action-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .id-card-approve {
        background-color: #28a745;
        color: white;
    }

    .id-card-approve:hover {
        background-color: #218838;
        color: white;
    }

    .id-card-modify {
        background-color: #ffc107;
        color: #212529;
    }

    .id-card-modify:hover {
        background-color: #e0a800;
        color: #212529;
    }

    .id-card-remove {
        background-color: #dc3545;
        color: white;
    }

    .id-card-remove:hover {
        background-color: #c82333;
        color: white;
    }

    /* Responsive Design for Target Layout */
    @media (max-width: 768px) {
        .card-preview-container {
            flex-direction: column;
            gap: 20px;
        }

        .card-preview {
            width: 215px;
            height: 335px;
        }

        .action-buttons-container {
            flex-direction: column;
            gap: 10px;
        }

        .id-card-action-btn {
            width: 100%;
            max-width: 200px;
        }
    }

    @media (max-width: 480px) {
        .id-card-preview-wrapper {
            padding: 15px;
        }

        .card-preview {
            width: 180px;
            height: 280px;
        }

        .card-title {
            font-size: 16px;
        }
    }

    /* Status Badge Styles */
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-approved {
        background-color: #28a745;
        color: white;
    }

    .status-in-review {
        background-color: #ffc107;
        color: #212529;
    }

    .status-rejected {
        background-color: #dc3545;
        color: white;
    }

    .status-modified {
        background-color: #17a2b8;
        color: white;
    }

    /* Preview loading indicator styles - Enhanced */
    .preview-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 100%;
        min-height: 200px;
        padding: 30px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 2px dashed #dee2e6;
    }

    .preview-loading i {
        color: #007bff;
        margin-bottom: 15px;
        font-size: 24px;
    }

    .preview-loading p {
        margin: 0;
        color: #6c757d;
        font-size: 16px;
        font-weight: 500;
    }

    /* Modal loading indicator */
    #modalLoadingIndicator {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        border-radius: 12px;
    }

    #modalLoadingIndicator .loading-content {
        text-align: center;
        padding: 20px;
    }

    #modalLoadingIndicator i {
        font-size: 32px;
        color: #007bff;
        margin-bottom: 15px;
    }

    #modalLoadingIndicator p {
        margin: 0;
        font-size: 16px;
        color: #333;
        font-weight: 500;
    }
</style>

<script>

    var display_fields = <?= ($selected_enabled_fields); ?>;
    var fatherData = <?= json_encode($father_data); ?>;
    var motherData = <?= json_encode($mother_data); ?>;
    var guardianData = <?= json_encode($guardian_data); ?>;
    var guardian2Data = <?= json_encode($guardian_2_data); ?>;
    var driverData = <?= json_encode($driver_data); ?>;
    var driver2Data = <?= json_encode($driver_2_data); ?>;

    function show_modal_to_map_rfid(relation_type, id, rfid_number){
        $("#rfid_modal").modal('show');
        $(".rfid_txtbox").focus();
        $("#disp_name_rfid").html(relation_type);
        $("#relation_type_id").val(id);
        $(".no_rfid").css('display', 'none');
        if(rfid_number){
            $(".delIcon span").css({
                display: '',
                fontSize: '24px',
                color: 'red'
            });
        }
        else{
            $(".delIcon span").hide();
        }
        $(".rfid_txtbox").val(rfid_number);
    }

    var timeout;
    var globalRfidValue;

    $('.rfid_txtbox').on('input', function() {
        var rfidValue = $(this).val();
        clearTimeout(timeout);
        if (rfidValue !== '') {
            timeout = setTimeout(function() {
                globalRfidValue = rfidValue;
            }, 500);
        }
    });

    $(".delIcon span").on('click', function(){
        var id = $("#relation_type_id").val();
        $.ajax({
            url: '<?= base_url("student/Student_controller/delete_rfid_from_table")?>',
            type: 'post',
            data: {'id': id, 'table': 'parent'},
            success: function(data) {
                if(data == 1){
                    location.href="<?php echo site_url('student/Student_controller/manage_id_cards/'.$student_uid);?>";
                }
            }
        });
    });

    function store_rfid(value){
        var rfid_value= globalRfidValue;
        var id = $("#relation_type_id").val();
        var relation_type = $("#disp_name_rfid").html();
        if (rfid_value){
            $.ajax({
                url: '<?php echo site_url("student/Student_controller/store_rfid_to_table")?>',
                type: 'post',
                data: {'id': id, 'rfid': rfid_value, 'table': 'parent', 'relation_type': relation_type,'student_id': <?= $student_uid ?>},
                success: function(data) {
                    if(data == 1){
                        timeout = setTimeout(function(){
                            location.href="<?php echo site_url('student/Student_controller/manage_id_cards/'.$student_uid);?>";
                        },500)
                    }                
                }
            });
        }
        else{
            $(".no_rfid").css('display','');
        }
    }

    function construct_data_fields(relation_type, id) {
        $("#form_modal .modal-body").empty();
        $("#disp_name_det").html(relation_type);
        let found = false;
        const relationData = {
            'Father': fatherData,
            'Mother': motherData,
            'Guardian': guardianData,
            'Guardian_2': guardian2Data,
            'Driver': driverData,
            'Driver_2': driver2Data
        };
        const displayFieldsMapping = {
            'Father': 'parent_info',
            'Mother': 'parent_info',
            'Guardian': 'guardian_info',
            'Guardian_2': 'guardian_info',
            'Driver': 'driver_info',
            'Driver_2': 'driver_info'
        };
        
        if (relationData.hasOwnProperty(relation_type)) {
            const values = display_fields[displayFieldsMapping[relation_type]];
            const data = relationData[relation_type];
            var html = `<input type="hidden" id="student_id" name="student_id" value="<?php echo $student_uid; ?>"><input type="hidden" id="relation_type" name="relation_type" value='${relation_type}'><input type="hidden" id="relation_id" name="relation_id" value=${id}>`;
            html += `<input type="hidden" name="old_value" value="" id="old_value">
        <input type="hidden" name="new_value" value="" id="new_value">`;
            if(values){
                values.forEach(value => {
                    if(value == 'id_card_issued_by'){
                        return;
                    }
                    const formattedValue = processString(value);
                    let inputType = 'text';
                    let class_disp="<span class='input-group-addon'><span class='fa fa-pencil'></span></span>";
                    const inputValue = data?.[value] ?? '';
                    if (value === 'dob' || value === 'id_card_issued_on') {
                        inputType = 'date';
                        class_disp = "";
                    }
                    html += `<div class="form-group row mt-3">
                                <label class="col-md-4 col-xs-12 text-right control-label" for="${value}">${formattedValue}</label>
                                <div class="col-md-6 col-xs-12">
                                    <div class="input-group">
                                        ${class_disp}
                                        <input id="${value}" placeholder="Enter ${formattedValue}" name="${value}" type="${inputType}" value="${inputValue}" class="form-control">
                                    </div>
                                </div>
                            </div>`;
                });
            }
            else{
                html += `<div class="form-group row mt-3">
                                <label class="col-md-3 col-xs-12 text-right control-label" for="Name">First Name</label>  
                                <div class="col-md-6 col-xs-12">
                                    <div class="input-group">
                                        <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
                                        <input id="first_name" placeholder="Enter First Name" name="first_name" type="text" class="form-control">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row mt-3">
                                <label class="col-md-3 col-xs-12 text-right control-label" for="name">Last Name</label>
                                <div class="col-md-6 col-xs-12">
                                    <div class="input-group">
                                        <span class="input-group-addon"><span class="fa fa-pencil"></span></span>
                                        <input id="last_name" placeholder="Enter Last Name" name="last_name" type="text" class="form-control">
                                    </div>
                                </div>
                            </div>`;
            }
            
            $("#form_modal .modal-body").append(html);
            initialData = $("#det_form").serializeArray();
            found = true;
        }
        show_form();
    }

    function show_form(){
        $("#form_modal").modal("show");        
    }

    function processString(input) {
        const parts = input.split('_');
        if (parts.length === 2) {
            const [firstPart, secondPart] = parts;
            const formattedString = `${firstPart} ${secondPart}`;
            return formattedString.charAt(0).toUpperCase() + formattedString.slice(1).toLowerCase();
        } else {
            var result = input.charAt(0).toUpperCase() + input.slice(1).toLowerCase();
            return result.replaceAll("_", " ");
        }
    }

    function store_relation_data(){
        var formData = new FormData(document.getElementById("det_form"));
        var isValid = true;
        if(formData.get("aadhar_no")){
            var AADHARNumber = formData.get("aadhar_no");
            if (AADHARNumber && !isValidAADHARNumber(AADHARNumber)) {
                alert("Invalid mobile number. Please enter a 12-digit AADHAR number.");
                isValid = false;
            }
        }
        if(formData.get('dob')){
            var dob = formData.get("dob");
            if (typeof dob === "string" && dob.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
                var parts = dob.split('/');
                var month = parts[0];
                var day = parts[1];
                var year = parts[2];
                var newDate = new Date(year, month - 1, day);
                var formattedDate = newDate.toISOString().slice(0, 10);
                formData.set("dob", formattedDate);
            } else if (dob instanceof Date) {
                var formattedDate = dob.toISOString().slice(0, 10);
                formData.set("dob", formattedDate);
            }
        }
        if(isValid){
            $.ajax({
                url: '<?= site_url('student/student_controller/saveRelationData'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data){
                    if(data){
                        $("#form_modal").modal("hide");
                        location.href="<?php echo site_url('student/Student_controller/manage_id_cards/'.$student_uid);?>"
                    }
                }
            })
        }
    }

    function isValidAADHARNumber(AADHARNumber) {
        const AADHARNumberRegex = /^\d{12}$/;
        return AADHARNumberRegex.test(AADHARNumber);
    }

    $(".btn-close").on('click', function(){
        $("#form_modal").modal("hide");
        $("#photo_modal").modal("hide");
        $("#rfid_modal").modal("hide");
        $("#dl_modal").modal("hide");
    })

    function add_photo(relation_type, id){
        $("#photo_modal").modal("show");
        $("#disp_name").html(relation_type);
        $("#relation_type").val(relation_type);
        $("#relation_type_id").val(id);
    }

    function save_photo(){
        $('#photo_btn').text('Please wait ...').attr('disabled','disabled');
        var formData = new FormData(document.getElementById("photo_form"));
        var relation_id = $("#relation_type_id").val();
        var relation_type = $("#disp_name").html();
        formData.append('relation_type_id',relation_id);
        formData.append('photo_url',$("#photo_url").val());
        formData.append('relation_type',relation_type);
        formData.append('student_id',<?= $student_uid ?>);
        if($("#photo_url").val() == ''){
            $(".no_photo").css('display','');
            $("#photo_btn").text('Upload Photo').removeAttr('disabled','disabled');
        }
        else{
            $(".no_photo").css('display','none');
            $.ajax({
                url: '<?php echo site_url('student/student_controller/save_photo'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data){
                    if(data){
                        $("#photo_modal").modal("hide");
                        location.href="<?php echo site_url('student/Student_controller/manage_id_cards/'.$student_uid);?>"
                    }
                }
            })
        }
    }

    function add_dl(relation_type, id){
        $("#dl_modal").modal("show");
        $("#disp_name_dl").html(relation_type);
        $("#relation_type").val(relation_type);
        $("#relation_type_id").val(id);
    }

    function save_dl(){
        $('#dl_btn').text('Please wait ...').attr('disabled','disabled');
        var formData = new FormData(document.getElementById("dl_form"));
        var relation_id = $("#relation_type_id").val();
        formData.append('relation_type_id',relation_id);
        formData.append('dl_url',$("#dl_url").val());
        if($("#dl_url").val() == ''){
            $(".no_dl").css('display','');
            $("#dl_btn").text('Upload Photo').removeAttr('disabled','disabled');
        }
        else{
            $(".no_dl").css('display','none');
            $.ajax({
                url: '<?php echo site_url('student/student_controller/save_dl'); ?>',
                type: 'post',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data){
                    if(data){
                        $("#dl_modal").modal("hide");
                        location.href="<?php echo site_url('student/Student_controller/manage_id_cards/'.$student_uid);?>"
                    }
                }
            })
        }
    }

    function copy_from_sibling(relation_type){
        $.ajax({
            url: '<?= site_url('student/Student_controller/copy_from_sibling')?>',
            type: 'post',
            data: {'relation_type':relation_type, 'sibling_id': <?= $sibling_id ?>, 'student_id': <?= $student_uid ?>},
            success: function(data){
                if(data){
                    location.href="<?php echo site_url('student/Student_controller/manage_id_cards/'.$student_uid);?>"
                }
            }
        })
    }
    
var changedData = {};
var initial_val = {};
var initialData = {};
// Consistent use of #det_form throughout
$(document).ready(function () {

// Initialize when modal is shown
$(document).on('shown.bs.modal', '#form_modal', function () {
    initialData = {};
    $('#det_form :input').each(function () {
        var name = $(this).attr('name');
        if (name) {
            initialData[name] = $(this).val();
        }
    });
});

// Track changes
$(document).on('input change', '#det_form :input', function () {
    var name = $(this).attr('name');
    var currentVal = $(this).val();
    var originalVal = initialData[name];

    if (name && originalVal !== currentVal) {
        initial_val[name] = originalVal;
        changedData[name] = currentVal;
    }

    if (name && originalVal === currentVal) {
        delete initial_val[name];
        delete changedData[name];
    }

    $('#old_value').val(JSON.stringify(initial_val));
    $('#new_value').val(JSON.stringify(changedData));
});
});

// EXACT COPY of FIELD_MAPPINGS constant from orderDetailsVerification.php (top-level)
const FIELD_MAPPINGS = [
    { field: '[[NAME]]', key: 'name' },
    { field: '[[ID]]', key: 'employee_code' },
    { field: '[[DEPARTMENT]]', key: 'department' },
    { field: '[[DESIGNATION]]', key: 'designation' },
    { field: '[[CONTACT]]', key: 'contact' },
    { field: '[[PARENT_NAME]]', key: 'parent_name' },
    { field: '[[FATHER_NAME]]', key: 'father_name' },
    { field: '[[MOTHER_NAME]]', key: 'mother_name' },
    { field: '[[FATHER_CONTACT]]', key: 'father_contact' },
    { field: '[[MOTHER_CONTACT]]', key: 'mother_contact' },
    { field: '[[BLOOD_GROUP]]', key: 'blood_group' },
    { field: '[[EMERGENCY_CONTACT]]', key: 'emergency_contact' },
    { field: '[[DATE_OF_BIRTH]]', key: 'date_of_birth' },
    { field: '[[DOB]]', key: 'dob' },
    { field: '[[QR_CODE]]', key: 'qr_code' },
    { field: '[[BAR_CODE]]', key: 'bar_code' },
    { field: '[[SIGNATURE]]', key: 'signature' },
    { field: '[[LOGO]]', key: 'logo' },
    { field: '[[ADDRESS]]', key: 'address' },
    { field: '[[EMAIL]]', key: 'email' },
    { field: '[[PHONE]]', key: 'phone' },
    { field: '[[WEBSITE]]', key: 'website' },
    { field: '[[SOCIAL_MEDIA]]', key: 'social_media' },
    { field: '[[QR_CODE_URL]]', key: 'qr_code_url' },
    { field: '[[BAR_CODE_URL]]', key: 'bar_code_url' },
    { field: '[[SIGNATURE_URL]]', key: 'signature_url' },
    { field: '[[LOGO_URL]]', key: 'logo_url' },
    { field: '[[ADDRESS_URL]]', key: 'address_url' },
    { field: '[[EMAIL_URL]]', key: 'email_url' },
    { field: '[[PHONE_URL]]', key: 'phone_url' },
    { field: '[[WEBSITE_URL]]', key: 'website_url' },
    { field: '[[SOCIAL_MEDIA_URL]]', key: 'social_media_url' },
    { field: '[[PHOTO]]', key: 'photo' },
    { field: '[[GRADE_SECTION]]', key: 'grade_section' },
    { field: '[[STUDENT_ADDRESS]]', key: 'address' },
    { field: '[[FATHER_ADDRESS]]', key: 'father_address' },
    { field: '[[MOTHER_ADDRESS]]', key: 'mother_address' },
    { field: '[[FATHER_PHOTO]]', key: 'father_photo' },
    { field: '[[MOTHER_PHOTO]]', key: 'mother_photo' },
    { field: '[[GRADE]]', key: 'grade' },
    { field: '[[COMBINATION]]', key: 'combination' },
    { field: '[[ENROLLMENT_NO]]', key: 'enrollment_no' },
    { field: '[[ALPHA_ROLL_NO]]', key: 'alpha_rollnum' },
    { field: '[[ADMISSION_NO]]', key: 'admission_no' },
    { field: '[[RELATION_TYPE]]', key: 'relation_type' },
    { field: '[[SIBLING_NAME]]', key: 'sibling_name' },
    { field: '[[NAME_CLASS]]', key: 'name_class' },
    { field: '[[STUDENT_PICKING_ROUTE]]', key: 'picking_route' },
    { field: '[[STUDENT_DROPPING_ROUTE]]', key: 'dropping_route' },
    { field: '[[SPOUSE_NAME]]', key: 'spouse_name' },
    { field: '[[STAFF_TYPE]]', key: 'staff_type' },

];

// Exact implementation like info-btn click handler from orderDetailsVerification.php
function view_id_card(relation_type, id, template_id) {
    // console.log('Opening ID card modal with params:', {
    //     relation_type: relation_type,
    //     id: id,
    //     template_id: template_id,
    //     student_uid: <?= $student_uid ?>
    // });

    // Store current entity information for approval process
    window.currentEntityType = relation_type;
    window.currentEntityId = id;
    window.currentStudentId = <?= $student_uid ?>;
    window.currentTemplateId = template_id;

    // Show modal immediately
    $('#idCardModal').fadeIn(300);

    // Update modal title
    $('#idCardModalTitle').text('ID Card Preview - ' + relation_type);

    // Hide progress bar and success message initially
    $('#approvalProgressContainer').hide();
    $('#approvalSuccess').hide();

    // Show loading indicator
    $('#modalLoadingIndicator').show();

    // Clear previous content and reset layout
    $('#frontCardPreview').empty();
    $('#backCardPreview').empty();
    $('#cardPreviewRow').removeClass('single-card-layout');

    // Hide loading indicator initially
    $('#modalLoadingIndicator').hide();

    // Reset status badge
    $('#entityStatusBadge').hide();

    // Create simple entity data object - actual data will be fetched in renderPreview
    var entityData = {
        relation_type: relation_type,
        id: id
    };

    // Initialize window variables for compatibility
    window.visibleEntityIds = [id];
    window.currentEntityIndex = 0;
    window.currentTemplateId = template_id;

    // Check existing approval status and set initial button states
    $.ajax({
        url: '<?php echo site_url("student/Student_controller/getIdCardApprovalStatus"); ?>',
        type: 'POST',
        data: {
            student_id: <?= $student_uid ?>,
            relation_type: relation_type,
            template_id: template_id
        },
        dataType: 'json',
        success: function(response) {
            let status = 'in review'; // default status

            if (response && response.success && response.approval) {
                status = response.approval.status || 'in review';
            }

            // Update status badge
            updateStatusBadge(status);
        },
        error: function() {
            // Fallback to default status
            updateStatusBadge('in review');
        }
    });

    // Load the preview - exact same approach as orderDetailsVerification.php
    renderPreview(entityData);
}

// EXACT COPY of renderPreview function from orderDetailsVerification.php with actual data fetching
function renderPreview(entityData) {
    // Show loading indicator in the preview areas
    $('#frontCardPreview').html('<div class="preview-loading"><i class="fa fa-spinner fa-spin fa-2x"></i><p>Loading preview...</p></div>');
    $('#backCardPreview').html('<div class="preview-loading"><i class="fa fa-spinner fa-spin fa-2x"></i><p>Loading preview...</p></div>');

    // Reset shape element counters
    window.shapeElementsLoading = 0;
    window.shapeElementsLoaded = 0;

    // Disable action buttons during loading
    disableActionButtons();

    // Function to hide modal loading indicator
    function hideModalLoadingIndicator() {
        $('#modalLoadingIndicator').fadeOut(300, function() {
            // Enable buttons after loading is complete
            enableActionButtons('in review');
        });
    }

    // Function to validate and adjust layout based on back card presence - EXACT COPY from orderDetailsVerification.php
    function validateAndAdjustLayout(frontDesign, backDesign) {
        // Check if back design exists and has meaningful content
        const hasBackCard = backDesign &&
                           backDesign.elements &&
                           backDesign.elements.length > 0 &&
                           $('#backCardPreview').length > 0;

        // Adjust layout based on card presence
        if (!hasBackCard) {
            // Only front card exists - center it
            $('#cardPreviewRow').addClass('single-card-layout');
            // Hide the back card column
            $('#cardPreviewRow .col-md-6:last-child').hide();
        } else {
            // Both cards exist - show normal layout
            $('#cardPreviewRow').removeClass('single-card-layout');
            $('#cardPreviewRow .col-md-6:last-child').show();
        }

        return hasBackCard;
    }

    // Set a timeout to ensure buttons are enabled even if there's an issue with loading
    window.buttonEnableTimeout = setTimeout(function() {
        if ($('.id-card-approve').prop('disabled')) {
            console.warn('Timeout reached for loading ID card elements, enabling buttons');
            const entityId = window.visibleEntityIds[window.currentEntityIndex];
            const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
            const currentStatus = entityRow.attr('data-status') || 'in review';
            enableActionButtons(currentStatus);
        }
    }, 5000); // 5 second timeout


    $.ajax({
        url: '<?php echo site_url("student/Student_controller/getEntityDataForIdCard"); ?>',
        type: 'post',
        data: {
            student_id: <?= $student_uid ?>,
            relation_type: entityData.relation_type,
            include_qr_code: true,  // Request QR code data from backend
            include_parent_data: true,  // Request parent data
            include_transport_data: true,  // Request transport routes
            include_comprehensive_data: true  // Request all fields from comprehensive query
        },
        success: function(entityResponse) {
            try {
                const entityResult = JSON.parse(entityResponse);
                console.log('Entity Data Received:', entityResult);
                if (entityResult && entityResult.success && entityResult.entity) {
                    // Use actual entity data from database - EXACT COPY from orderDetailsVerification.php structure
                    const entity = entityResult.entity;

                    // Build entity data structure exactly like orderDetailsVerification.php
                    let actualEntityData;
                    if (entityData.relation_type === 'Student') {
                        // EXACT COPY from orderDetailsVerification.php lines 2867-2899
                        let parent_contact_info = (entity.mother_contact || '') + ',' + (entity.father_contact || '');
                        let parent_name = entity.father_name || '';
                        if(entity.point_of_contact != null && entity.point_of_contact != ''){
                            parent_contact_info = entity.contact;
                            parent_name = entity.parent_name;
                        }

                        actualEntityData = {
                            id: entity.sa_id,
                            name: entity.name,
                            admission_no: entity.admission_no,
                            combination: entity.combination,
                            enrollment_no: entity.enrollment_no,
                            alpha_rollnum: entity.alpha_rollnum,
                            grade_section: entity.grade_section,
                            grade: entity.grade,
                            dob: entity.dob,
                            date_of_birth: entity.dob,
                            contact: parent_contact_info,
                            blood_group: entity.blood_group,
                            address: entity.address,
                            parent_name: entity.parent_name,
                            father_name: entity.father_name,
                            mother_name: entity.mother_name,
                            father_contact: entity.father_contact,
                            mother_contact: entity.mother_contact,
                            email: entity.email,
                            phone: entity.preferred_contact_no,
                            photo: entity.father_photo,
                            type: entity.type,
                            father_address : entity.father_address,
                            mother_address : entity.mother_address,
                            qr_code : entity.qr_code,
                            relation_type : entity.relation_type,
                            sibling_name : entity.sibling_name,
                            name_class : entity.name_class,
                            picking_route : entity.picking_route,
                            dropping_route : entity.dropping_route
                        };
                    } else {
                        // For staff/parent entities - EXACT COPY from orderDetailsVerification.php lines 2840-2859
                        let parent_contact_info = (entity.mother_contact || '') + ',' + (entity.father_contact || '');
                        let parent_name = entity.father_name || '';
                        if(entity.point_of_contact != null && entity.point_of_contact != ''){
                            parent_contact_info = entity.contact;
                            parent_name = entity.parent_name;
                        }
                        actualEntityData = {
                            id: entity.sa_id,
                            name: entity.name,
                            admission_no: entity.admission_no,
                            combination: entity.combination,
                            enrollment_no: entity.enrollment_no,
                            alpha_rollnum: entity.alpha_rollnum,
                            grade_section: entity.grade_section,
                            grade: entity.grade,
                            dob: entity.dob,
                            date_of_birth: entity.dob,
                            contact: parent_contact_info,
                            blood_group: entity.blood_group,
                            address: entity.address,
                            parent_name: entity.parent_name,
                            father_name: entity.father_name,
                            mother_name: entity.mother_name,
                            father_contact: entity.father_contact,
                            mother_contact: entity.mother_contact,
                            email: entity.email,
                            phone: entity.preferred_contact_no,
                            photo: entity.photo,
                            father_photo: entity.father_photo,
                            mother_photo: entity.mother_photo,
                            type: entity.type,
                            father_address : entity.father_address,
                            mother_address : entity.mother_address,
                            qr_code : entity.qr_code,
                            relation_type : entity.relation_type,
                            sibling_name : entity.sibling_name,
                            name_class : entity.name_class,
                            picking_route : entity.picking_route,
                            dropping_route : entity.dropping_route
                        };
                    }

                    // Ensure we have all required data - fill from PHP variables if missing

                    // Add missing parent data from PHP variables if not present
                    if (entityData.relation_type === 'Student') {
                        if (!actualEntityData.father_name) {
                            actualEntityData.father_name = '<?= !empty($father_data) ? addslashes($father_data->name) : "" ?>';
                            actualEntityData.father_contact = '<?= !empty($father_data) ? $father_data->mobile_no : "" ?>';
                            actualEntityData.father_photo = '<?= !empty($father_pic) ? $father_pic : base_url()."assets/img/icons/profile.png" ?>';
                            actualEntityData.father_address = '<?= !empty($father_data) && isset($father_data->address) ? addslashes($father_data->address) : "" ?>';
                        }
                        if (!actualEntityData.mother_name) {
                            actualEntityData.mother_name = '<?= !empty($mother_data) ? addslashes($mother_data->name) : "" ?>';
                            actualEntityData.mother_contact = '<?= !empty($mother_data) ? $mother_data->mobile_no : "" ?>';
                            actualEntityData.mother_photo = '<?= !empty($mother_pic) ? $mother_pic : base_url()."assets/img/icons/profile.png" ?>';
                            actualEntityData.mother_address = '<?= !empty($mother_data) && isset($mother_data->address) ? addslashes($mother_data->address) : "" ?>';
                        }

                        // Update parent contact info if we added parent data
                        if (actualEntityData.father_contact || actualEntityData.mother_contact) {
                            actualEntityData.contact = (actualEntityData.mother_contact || '') + ',' + (actualEntityData.father_contact || '');
                            actualEntityData.parent_name = actualEntityData.father_name || actualEntityData.mother_name || '';
                        }
                    }

                    // Now fetch template data
                    $.ajax({
                        url: '<?php echo site_url("student/Student_controller/getIdCardTemplate"); ?>',
                        type: 'post',
                        data: { relation_type: entityData.relation_type },
                        success: function(response) {
                            try {
                                const templateData = JSON.parse(response);
                                console.log('Template Data Received:', templateData);

                                if (templateData && templateData.template) {
                                    const template = templateData.template;

                                    let frontDesign = JSON.parse(template.front_design);
                                    let backDesign = JSON.parse(template.back_design);

                                    // Validate layout and adjust containers
                                    const hasBackCard = validateAndAdjustLayout(frontDesign, backDesign);

                                    $('#frontCardPreview').empty();
                                    renderDesign('#frontCardPreview', frontDesign, actualEntityData);

                                    if (hasBackCard) {
                                        $('#backCardPreview').empty();
                                        renderDesign('#backCardPreview', backDesign, actualEntityData);
                                    } else {
                                        // Clear back card preview if no back design
                                        $('#backCardPreview').empty();
                                    }

                                    // Hide modal loading indicator
                                    hideModalLoadingIndicator();

                                    // If no shape elements were found, enable buttons immediately
                                    if (window.shapeElementsLoading === 0) {
                                        const entityId = window.visibleEntityIds[window.currentEntityIndex];
                                        const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                                        const currentStatus = entityRow.attr('data-status') || 'in review';
                                        enableActionButtons(currentStatus);
                                    }
                                } else {
                                    hideModalLoadingIndicator();
                                    renderSimplePreview(actualEntityData);
                                }
                            } catch (e) {
                                console.error('Error parsing template data:', e);
                                hideModalLoadingIndicator();
                                renderSimplePreview(actualEntityData);
                                enableActionButtons('in review');
                            }
                        },
                        error: function(err) {
                            console.error('Error fetching template data:', err);
                            hideModalLoadingIndicator();
                            renderSimplePreview(actualEntityData);
                            enableActionButtons('in review');
                        }
                    });
                } else {
                    console.error('Error fetching entity data:', entityResult.error);
                    hideModalLoadingIndicator();
                    // Fallback to sample data if entity data fetch fails
                    renderPreviewWithSampleData(entityData);
                }
            } catch (e) {
                console.error('Error parsing entity data:', e);
                hideModalLoadingIndicator();
                // Fallback to sample data if parsing fails
                renderPreviewWithSampleData(entityData);
            }
        },
        error: function(err) {
            console.error('Error fetching entity data:', err);
            hideModalLoadingIndicator();
            // Fallback to sample data if AJAX fails
            renderPreviewWithSampleData(entityData);
        }
    });
}

function renderPreviewWithSampleData(entityData) {
    if (!entityData.qr_code) {
        entityData.qr_code = 'SAMPLE_QR_CODE';
    }

    $.ajax({
        url: '<?php echo site_url("student/Student_controller/getIdCardTemplate"); ?>',
        type: 'post',
        data: { relation_type: entityData.relation_type },
        success: function(response) {
            try {
                const templateData = JSON.parse(response);

                if (templateData && templateData.template) {
                    const template = templateData.template;

                    let frontDesign = JSON.parse(template.front_design);
                    let backDesign = JSON.parse(template.back_design);

                    // Validate layout and adjust containers
                    const hasBackCard = validateAndAdjustLayout(frontDesign, backDesign);

                    $('#frontCardPreview').empty();
                    renderDesign('#frontCardPreview', frontDesign, entityData);

                    if (hasBackCard) {
                        $('#backCardPreview').empty();
                        renderDesign('#backCardPreview', backDesign, entityData);
                    }

                    // If no shape elements were found, enable buttons immediately
                    if (window.shapeElementsLoading === 0) {
                        enableActionButtons('in review');
                    }
                } else {
                    renderSimplePreview(entityData);
                }
            } catch (e) {
                console.error('Error parsing template data:', e);
                renderSimplePreview(entityData);
                enableActionButtons('in review');
            }
        },
        error: function(err) {
            console.error('Error fetching template data:', err);
            renderSimplePreview(entityData);
            enableActionButtons('in review');
        }
    });
}

// EXACT COPY of renderDesign function from orderDetailsVerification.php
function renderDesign(container, design, entityData) {
    const $container = $(container);
    $container.addClass(`card-size-${design.styles?.size || 'portrait'}`);

    if (design.styles && design.styles.backgroundColor) {
        $container.css('background-color', design.styles.backgroundColor);
    }

    // Count shape elements that need to be loaded
    if (design.elements) {
        design.elements.forEach(element => {
            if (element.type === 'shape') {
                window.shapeElementsLoading++;
            }
        });
    }

    // Disable action buttons until all shapes are loaded
    disableActionButtons();

    design.elements?.forEach(element => {
        // For address fields, use relative positioning and auto height
        let isAddressField =
            element.type === 'field' &&
            (
                element.properties.fieldName === '[[ADDRESS]]' ||
                element.properties.fieldName === '[[STUDENT_ADDRESS]]' ||
                element.properties.fieldName === '[[FATHER_ADDRESS]]' ||
                element.properties.fieldName === '[[MOTHER_ADDRESS]]'
            );

        const $element = $('<div>').addClass('element').attr('data-type', element.type).css({
            position: isAddressField ? 'relative' : 'absolute',
            left: element.x + 'px',
            top: element.y + 'px',
            width: element.width + 'px',
            height: isAddressField ? 'auto' : (element.height + 'px'),
            zIndex: element.zIndex || 0
        });
        switch (element.type) {
            case 'text':
                $element.html(`<div class="element-content">${element.properties.text || ''}</div>`);
                $element.find('.element-content').css({
                    'font-family': element.properties.font,
                    'font-size': `${element.properties.size}px`,
                    'color': element.properties.color,
                    'font-weight': element.properties.bold ? 'bold' : 'normal',
                    'font-style': element.properties.italic ? 'italic' : 'normal',
                    'text-decoration': element.properties.underline ? 'underline' : 'none',
                    'text-align': element.properties.textAlign || 'left',
                    'background-color': element.properties.backgroundColor || 'transparent',
                    'border': element.properties.strokeWidth ?
                        `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                    'padding': '2px 5px'
                });
                break;

            case 'field':
                const value = getFieldValue(element.properties.fieldName, entityData);
                $element.html(`<div class="element-content">${value}</div>`);

                const fixedHeight = element.height || 48;

                if (
                    element.properties.fieldName === '[[ADDRESS]]' ||
                    element.properties.fieldName === '[[STUDENT_ADDRESS]]' ||
                    element.properties.fieldName === '[[FATHER_ADDRESS]]' ||
                    element.properties.fieldName === '[[MOTHER_ADDRESS]]'
                ) {
                    $element.find('.element-content').css({
                        'font-family': element.properties.font,
                        'font-size': `${element.properties.size}px`,
                        'color': element.properties.color,
                        'font-weight': element.properties.bold ? 'bold' : 'normal',
                        'font-style': element.properties.italic ? 'italic' : 'normal',
                        'text-decoration': element.properties.underline ? 'underline' : 'none',
                        'text-align': element.properties.textAlign || 'left',
                        'background-color': element.properties.backgroundColor || 'transparent',
                        'border': element.properties.strokeWidth ?
                            `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                        'padding': '12px',
                        'width': '100%',
                        'min-height': fixedHeight + 'px',
                        'height': 'auto',
                        'max-height': 'none',
                        'box-sizing': 'border-box',
                        'overflow': 'hidden',
                        'white-space': 'pre-line',
                        'word-break': 'break-word',
                        'line-height': '1.2',
                        'display': 'block',
                        'text-transform': element.properties.textTransform || 'capitalize'
                    });
                } else if (
                    element.properties.fieldName === '[[NAME]]'

                ) {
                    let fontSize = element.properties.size || 18;
                    let nameValue = value || '';
                    let textAlign = element.properties.textAlign || 'left';

                    let elementWidth = element.width || 215; //

                    $element.addClass('name-field');
                   const charWidthFactor = 0.58;
                   const containerWidthInChars = Math.floor(elementWidth / (fontSize * charWidthFactor));
                    // Adjust font size based on both text length and available width
                    if (nameValue.length > containerWidthInChars * 0.8) {
                        // Text is approaching container width limit
                        const ratio = containerWidthInChars / nameValue.length;
                       // More granular font size adjustments
                        if (ratio < 0.3) {
                            // Extremely long text
                            fontSize = Math.max(6, fontSize * 0.45);
                        } else if (ratio < 0.4) {
                            // Very long text
                            fontSize = Math.max(7, fontSize * 0.5);

                        } else if (ratio < 0.5) {
                            // Long text
                            fontSize = Math.max(8, fontSize * 0.6);

                        } else if (ratio < 0.6) {
                            // Moderately long text
                            fontSize = Math.max(9, fontSize * 0.5);
                        } else if (ratio < 0.7) {
                            // Slightly long text
                            fontSize = Math.max(9, fontSize * 0.5);
                        } else if (ratio < 0.8) {
                            // Just a bit too long
                            fontSize = Math.max(9, fontSize * 0.6);
                        }else if(ratio < 0.9){
                            fontSize = Math.max(8, fontSize * 0.7);
                        }else if(ratio < 0.95){
                            fontSize = Math.max(9, fontSize * 0.8);
                        }else if(ratio < 1.2){
                            fontSize = Math.max(9, fontSize * 0.9);
                        }else if(ratio < 1.5) {
                            fontSize = Math.max(10, fontSize * 0.9);
                        }
                    }
                    $element.find('.element-content').css({
                        'font-family': element.properties.font,
                        'font-size': `${fontSize}px`,
                        'color': element.properties.color,
                        'font-weight': element.properties.bold ? 'bold' : 'normal',
                        'font-style': element.properties.italic ? 'italic' : 'normal',
                        'text-decoration': element.properties.underline ? 'underline' : 'none',
                        'text-align': textAlign,
                        'background-color': element.properties.backgroundColor || 'transparent',
                        'border': element.properties.strokeWidth ?
                            `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                        'padding': '6px 8px',
                        'width': '100%',
                        'height':   'auto',
                        'max-height': 'none',
                        'box-sizing': 'border-box',
                        'overflow': 'visible',
                        'white-space': 'normal',
                        'word-break': 'break-word',
                        'line-height': '1.2',
                        'display': 'block',
                        'text-transform': element.properties.textTransform || 'capitalize',

                    });
                } else if (
                    element.properties.fieldName === '[[PARENT_NAME]]' || element.properties.fieldName === '[[FATHER_NAME]]' || element.properties.fieldName === '[[MOTHER_NAME]]'
                ) {
                    let fontSize = element.properties.size || 16;
                    let parentNameValue = value || '';
                    let textAlign = element.properties.textAlign || 'left';
                    let elementWidth = element.width || 215;
                    $element.addClass('parent-name-field');


                    const charWidthFactor = 0.58;
                    const containerWidthInChars = Math.floor(elementWidth / (fontSize * charWidthFactor));

                    // Adjust font size based on both text length and available width
                    if (parentNameValue.length > containerWidthInChars * 0.8) {
                        // Text is approaching container width limit
                        const ratio = containerWidthInChars / parentNameValue.length;
                       // More granular font size adjustments

                        if (ratio < 0.3) {
                            // Extremely long text
                            fontSize = Math.max(6, fontSize * 0.45);
                        } else if (ratio < 0.4) {
                            // Very long text
                            fontSize = Math.max(7, fontSize * 0.5);

                        } else if (ratio < 0.5) {
                            // Long text
                            fontSize = Math.max(8, fontSize * 0.6);

                        } else if (ratio < 0.6) {
                            // Moderately long text
                            fontSize = Math.max(9, fontSize * 0.5);
                        } else if (ratio < 0.7) {
                            // Slightly long text
                            fontSize = Math.max(9, fontSize * 0.5);
                        } else if (ratio < 0.8) {
                            // Just a bit too long
                            fontSize = Math.max(9, fontSize * 0.6);
                        }else if(ratio < 0.9){
                            fontSize = Math.max(10, fontSize * 0.7);
                        }else if(ratio < 0.95){
                            fontSize = Math.max(11, fontSize * 0.8);
                        }else if(ratio < 1.2){
                            fontSize = Math.max(10, fontSize * 0.9);
                        }else if(ratio < 1.5) {
                            fontSize = Math.max(9, fontSize * 0.9);

                        }
                    }

                    $element.find('.element-content').css({
                        'font-family': element.properties.font,
                        'font-size': `${fontSize}px`,
                        'color': element.properties.color,
                        'font-weight': element.properties.bold ? 'bold' : 'normal',
                        'font-style': element.properties.italic ? 'italic' : 'normal',
                        'text-decoration': element.properties.underline ? 'underline' : 'none',
                        'text-align': textAlign,
                        'background-color': element.properties.backgroundColor || 'transparent',
                        'border': element.properties.strokeWidth ?
                            `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                        'padding': '6px 8px',
                        'width': '100%',
                        'height': fixedHeight + 'px',
                        'max-height': fixedHeight + 'px',
                        'box-sizing': 'border-box',
                        'overflow': 'hidden',
                        'white-space': 'normal',
                        'word-break': 'break-word',
                        'line-height': '1.2',
                        'display': 'block',
                        'text-transform': element.properties.textTransform || 'capitalize',
                    });
                }else if (element.properties.fieldName === '[[QR_CODE]]') {
                    if (entityData.qr_code) {
                        // Display QR code number below the QR code image
                        $element.find('.element-content').css({
                            'flex-direction': 'column',
                            'align-items': 'center',
                            'justify-content': 'center'
                        });

                        // Use a QR code image if available, else generate using API
                        let qrCodeImg = '';
                        if (entityData.qr_code_url) {
                            qrCodeImg = `<img src="${entityData.qr_code_url}" alt="QR Code" style="max-width:100%;max-height:100px;display:block;margin-bottom:4px;">`;
                            $element.find('.element-content').html(qrCodeImg);
                        } else {
                            $element.find('.element-content').html('<i class="fa fa-spinner fa-spin fa-2x"></i>');
                            generate_qr_code('qr_code', entityData.qr_code, element.properties.qr_size, function(generatedQRCode) {
                                qrCodeImg = `<img src="${generatedQRCode}" alt="QR Code">`;
                                $element.find('.element-content').html(qrCodeImg);
                            });
                        }
                    } else {
                        $element.find('.element-content').html('<span style="color:#aaa;">No QR Code</span>');
                    }
                }else if (element.properties.fieldName === '[[BAR_CODE]]') {
                    if (entityData.qr_code) {
                        // Display QR code number below the QR code image
                        $element.find('.element-content').css({
                            'flex-direction': 'column',
                            'align-items': 'center',
                            'justify-content': 'center'
                        });

                        // Use a QR code image if available, else generate using API
                        let qrCodeImg = '';
                        if (entityData.qr_code_url) {
                            qrCodeImg = `<img src="${entityData.qr_code_url}" alt="QR Code" style="max-width:100%;max-height:100px;display:block;margin-bottom:4px;">`;
                            $element.find('.element-content').html(qrCodeImg);
                        } else {
                            $element.find('.element-content').html('<i class="fa fa-spinner fa-spin fa-2x"></i>');
                            generate_qr_code('bar_code', entityData.qr_code, element.properties.qr_size, function(generatedQRCode) {
                                qrCodeImg = `<img src="${generatedQRCode}" alt="QR Code">`;
                                $element.find('.element-content').html(qrCodeImg);
                            });
                        }
                    } else {
                        $element.find('.element-content').html('<span style="color:#aaa;">No QR Code</span>');
                    }
                }else {
                    let fontSize = element.properties.size || 11;
                    let NameValue = value || '';
                    let textAlign = element.properties.textAlign || 'left';
                    let elementWidth = element.width || 215;

                    const charWidthFactor = 0.58;
                    const containerWidthInChars = Math.floor(elementWidth / (fontSize * charWidthFactor));
                    // Adjust font size based on both text length and available width
                    if (NameValue.length > containerWidthInChars * 0.8) {
                        // Text is approaching container width limit
                        const ratio = containerWidthInChars / NameValue.length;

                       // More granular font size adjustments
                        if (ratio < 0.3) {
                            // Extremely long text
                            fontSize = Math.max(6, fontSize * 0.45);
                        } else if (ratio < 0.4) {
                            // Very long text
                            fontSize = Math.max(7, fontSize * 0.5);

                        } else if (ratio < 0.5) {
                            // Long text
                            fontSize = Math.max(8, fontSize * 0.6);

                        } else if (ratio < 0.6) {
                            // Moderately long text
                            fontSize = Math.max(9, fontSize * 0.5);
                        } else if (ratio < 0.7) {
                            // Slightly long text
                            fontSize = Math.max(9, fontSize * 0.5);
                        } else if (ratio < 0.8) {
                            // Just a bit too long
                            fontSize = Math.max(9, fontSize * 0.6);
                        } else if (ratio < 0.93) {
                            fontSize = Math.max(8, fontSize * 0.7);
                        } else if (ratio < 0.95) {
                            fontSize = Math.max(8, fontSize * 0.8);
                        } else if (ratio <= 1) {
                            // Check for ratio less than 1.05
                            fontSize = Math.max(8.5, fontSize * 0.8);
                        }else if (ratio < 1.04) {
                            fontSize = Math.max(6, fontSize * 0.8);
                        }else if (ratio < 1.05) {
                            // Check for ratio less than 1.05
                            fontSize = Math.max(8, fontSize * 0.8);
                        } else if (ratio < 1.2) {
                            fontSize = Math.max(9.5, fontSize * 0.8);
                        } else if (ratio < 1.5) {
                            fontSize = Math.max(8, fontSize * 0.9);
                        }
                    }

                    // Apply base styling for all other fields
                    $element.find('.element-content').css({
                        'font-family': element.properties.font,
                        'font-size': `${fontSize}px`,
                        'color': element.properties.color,
                        'font-weight': element.properties.bold ? 'bold' : 'normal',
                        'font-style': element.properties.italic ? 'italic' : 'normal',
                        'text-decoration': element.properties.underline ? 'underline' : 'none',
                        'text-align': element.properties.textAlign || 'left',
                        'background-color': element.properties.backgroundColor || 'transparent',
                        'border': element.properties.strokeWidth ?
                            `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                        'padding': '6px 8px', // more vertical padding for spacing
                        'width': '100%',
                        'height': fixedHeight + 'px',
                        'max-height': fixedHeight + 'px',
                        'box-sizing': 'border-box',
                        'overflow': 'hidden',
                        'white-space': 'normal',
                        'word-break': 'break-word',
                        'line-height': '1.2',
                        'display': 'block',
                        'text-transform': 'uppercase',
                    });
                }
                break;

            case 'shape':
                $element.html('<div class="element-content shape-container"><i class="fa fa-spinner fa-spin fa-2x"></i></div>');
                const $shapeContent = $element.find('.element-content');
                $shapeContent.addClass(`shape-${element.properties.shapeType}`);
                let photoUrl = entityData.photo || element.properties.defaultPhoto || '<?= base_url("assets/img/icons/profile.png") ?>';
                if (photoUrl.includes('wasabisys.com')) {
                    $.ajax({
                        url: '<?php echo site_url("idcards/Idcards_controller/getImageAsBase64"); ?>',
                        type: 'POST',
                        data: {
                            image_url: photoUrl
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success && response.base64) {
                                const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                                $shapeContent.html(`<img id="${imgId}" src="${response.base64}" class="photo-in-shape">`);
                                applyFaceDetection(imgId, response.base64);
                                incrementShapeElementsLoaded();
                            } else {
                                console.warn('Base64 conversion failed, using original URL');
                                const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                                $shapeContent.html(`<img id="${imgId}" src="${photoUrl}" class="photo-in-shape">`);
                                applyFaceDetection(imgId, photoUrl);
                                incrementShapeElementsLoaded();
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Error fetching base64 image:', error);
                            const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                            $shapeContent.html(`<img id="${imgId}" src="${photoUrl}" class="photo-in-shape">`);
                            applyFaceDetection(imgId, photoUrl);
                            incrementShapeElementsLoaded();
                        }
                    });
                } else {
                    const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                    $shapeContent.html(`<img id="${imgId}" src="${photoUrl}" class="photo-in-shape">`);
                    applyFaceDetection(imgId, photoUrl);
                    incrementShapeElementsLoaded();
                }
                $shapeContent.css({
                    'background-color': element.properties.fillColor || 'transparent',
                    'border': `${element.properties.borderWidth || 0}px solid ${element.properties.borderColor || '#000000'}`,
                    'overflow': 'hidden',
                    'border-radius': `${element.properties.borderRadius || ''}px`,
                });
                break;

            case 'image':
                if (element.properties.fieldName === '[[PHOTO]]') {
                    const photoUrl = entityData.photo || element.properties.src || '<?= base_url("assets/images/default-profile.jpg") ?>';
                    const imageClass = element.properties.shapeType === 'circle' ?
                        'element-content photo-in-circle' : 'element-content';
                    const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                    $element.html(`<img id="${imgId}" src="${photoUrl}" class="${imageClass}">`);
                    applyFaceDetection(imgId, photoUrl);
                } else {
                    $element.html(`<img src="${element.properties.src || ''}" class="element-content">`);
                }
                break;
        }

        $container.append($element);
    });
}

// Helper functions for shape element loading - EXACT COPY from orderDetailsVerification.php
function incrementShapeElementsLoaded() {
    window.shapeElementsLoaded++;
    if (window.shapeElementsLoaded >= window.shapeElementsLoading) {
        // All shape elements have loaded, hide modal loading indicator and enable action buttons
        $('#modalLoadingIndicator').fadeOut(300);

        const entityId = window.visibleEntityIds[window.currentEntityIndex];
        const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
        const currentStatus = entityRow.attr('data-status') || 'in review';
        enableActionButtons(currentStatus);
    }
}

// Function to approve ID card with progress bar
function approveIdCard() {
    // Prevent multiple clicks by checking if already processing
    if ($('#approveBtn').prop('disabled') || $('#approvalProgressContainer').is(':visible')) {
        return;
    }

    // Disable button immediately to prevent double clicks
    $('#approveBtn').prop('disabled', true);

    // Confirm approval
    if (!confirm('Are you sure you want to approve this ID card?')) {
        // Re-enable button if user cancels
        $('#approveBtn').prop('disabled', false);
        return;
    }

    // Get current entity data
    const currentEntityType = window.currentEntityType;
    const currentEntityId = window.currentEntityId;
    const currentStudentId = window.currentStudentId;

    if (!currentEntityType || !currentEntityId || !currentStudentId) {
        alert('Missing entity information. Please close and reopen the modal.');
        $('#approveBtn').prop('disabled', false);
        return;
    }

    // Hide approve button and show progress bar
    $('#approveBtn').hide();
    $('#approvalProgressContainer').show();

    // Initialize progress
    let progress = 0;
    updateProgress(progress, 'Initializing approval process...');

    // Simulate progress steps
    const progressSteps = [
        { progress: 20, message: 'Validating ID card data...' },
        { progress: 40, message: 'Processing card images...' },
        { progress: 60, message: 'Updating database records...' },
        { progress: 80, message: 'Finalizing approval...' },
        { progress: 100, message: 'Approval completed successfully!' }
    ];

    let currentStep = 0;

    // Start progress animation
    const progressInterval = setInterval(() => {
        if (currentStep < progressSteps.length) {
            const step = progressSteps[currentStep];
            updateProgress(step.progress, step.message);
            currentStep++;

            // On final step, call the actual approval
            if (currentStep === progressSteps.length) {
                clearInterval(progressInterval);
                // Call the toggleEntityStatus function to activate the entity
                performActualApproval(currentEntityId, currentEntityType, currentStudentId);
            }
        }
    }, 800); // 800ms between steps
}

// Function to update progress bar
function updateProgress(percentage, message) {
    $('#approvalProgressBar').css('width', percentage + '%');
    $('#approvalProgressBar').attr('aria-valuenow', percentage);
    $('#progressText').text(percentage + '%');
    $('#progressStatus').html(`<small>${message}</small>`);
}

// Function to perform actual approval
function performActualApproval(entityId, entityType, studentId) {
    // console.log('Performing approval for:', entityId, entityType, studentId);

    // Find the corresponding entity ID from the page data
    let actualEntityId = null;

    // Try multiple approaches to find the entity ID

    // Approach 1: Look for toggle buttons with exact entity type match
    let toggleButton = $(`button[onclick*="toggleEntityStatus"][onclick*="${entityId}"][onclick*="'${entityType.toLowerCase()}'"]`);

    // Approach 2: If not found, try with different case variations
    if (toggleButton.length === 0) {
        const entityTypeVariations = [entityType, entityType.toLowerCase(), entityType.toUpperCase()];
        for (const variation of entityTypeVariations) {
            toggleButton = $(`button[onclick*="toggleEntityStatus"][onclick*="${entityId}"][onclick*="'${variation}'"]`);
            if (toggleButton.length > 0) break;
        }
    }

    // Approach 3: If still not found, search all toggle buttons and match manually
    if (toggleButton.length === 0) {
        $('button[onclick*="toggleEntityStatus"]').each(function() {
            const onclickAttr = $(this).attr('onclick');
            if (onclickAttr.includes(entityId) && onclickAttr.includes(entityType.toLowerCase())) {
                toggleButton = $(this);
                return false; // Break the loop
            }
        });
    }

    if (toggleButton.length > 0) {
        const onclickAttr = toggleButton.attr('onclick');
        // console.log('Found toggle button onclick:', onclickAttr);

        // Extract entity ID from onclick attribute (last parameter)
        const matches = onclickAttr.match(/toggleEntityStatus\([^,]+,\s*[^,]+,\s*'[^']+',\s*[^,]+,\s*([^)]+)\)/);
        if (matches && matches[1]) {
            actualEntityId = matches[1].trim();
            // console.log('Extracted entity ID:', actualEntityId);
        }
    }

    // If still not found, try to use the entityId directly as actualEntityId
    if (!actualEntityId) {
        console.warn('Could not find entity ID from toggle button, using entityId directly');
        actualEntityId = entityId;
    }

    if (!actualEntityId) {
        updateProgress(100, 'Error: Could not find entity ID');
        setTimeout(() => {
            $('#approvalProgressContainer').hide();
            $('#approveBtn').show().prop('disabled', false);
            alert('Error: Could not find entity information for approval.');
        }, 2000);
        return;
    }

    // Process card images first, then approve with generated URLs
    updateProgress(30, 'Processing ID card images...');

    console.log('Entity details - entityId:', entityId, 'entityType:', entityType, 'studentId:', studentId);
    console.log('Using actualEntityId for image processing:', actualEntityId);

    // Use the proper image processing workflow that generates frontUrl and backUrl
    processCardImages(actualEntityId, entityType);
}

// Placeholder functions for face detection (can be implemented later)
function applyFaceDetection(photoId, photoUrl) {
    if (!photoUrl) {
        return;
    }

    const img = document.getElementById(photoId);
    if (!img) {
        return;
    }

    $(img).css({
        'object-position': 'center top'
    });


    if (photoUrl.includes('wasabisys.com') || !isValidUrl(photoUrl)) {
        return;
    }


    img.crossOrigin = "anonymous";


    if (!img.complete) {
        img.onload = function() {
            centerFaceInImage(img);
        };
        img.onerror = function() {
            // console.log('Image load error, using default positioning');
        };
    } else {

        centerFaceInImage(img);
    }
}

function centerFaceInImage(img) {
    try {

        if (!img.naturalWidth || !img.naturalHeight) {
            return;
        }


        if (img.naturalHeight > img.naturalWidth) {
            $(img).css({
                'object-position': 'center 25%',
                'object-fit': 'fill'
            });
        } else {

            $(img).css({
                'object-position': 'center 35%',
                'object-fit': 'fill'
            });
        }
    } catch (error) {
        // console.log('Error in centerFaceInImage:', error);
    }
}


function generate_qr_code(dataType, qrdata, qr_size, callback){
    $.ajax({
        url: "<?php echo site_url('idcards/Idcards_controller/generate_qr_code_for_idcards') ?>",
        data: {dataType:dataType, qrdata: qrdata, qr_size:qr_size},
        success: function (base64img) {
            callback(base64img);
        }
    });
}

// Make id_card_data available in JavaScript
window.id_card_data = <?php echo json_encode($id_card_data); ?>;

// Track loading state of shape elements - EXACT COPY from orderDetailsVerification.php
window.shapeElementsLoading = 0;
window.shapeElementsLoaded = 0;

// Function to disable action buttons during loading
function disableActionButtons() {
    $('#approveBtn').prop('disabled', true);
    $('#approveBtn').html('<i class="fa fa-spinner fa-spin"></i> LOADING...');
    $('#approvalProgressContainer').hide();
    $('#approvalSuccess').hide();
}

// Function to enable action buttons after loading - EXACT COPY from orderDetailsVerification.php
function enableActionButtons(status) {
    // Clear any pending timeout
    if (window.buttonEnableTimeout) {
        clearTimeout(window.buttonEnableTimeout);
        window.buttonEnableTimeout = null;
    }

    // Only enable if modal loading is complete
    if ($('#modalLoadingIndicator').is(':visible')) {
        // console.log('Modal still loading, buttons will be enabled after loading completes');
        return;
    }

    // Check if entity is already approved/active
    if (checkEntityApprovalStatus(status)) {
        // Hide approve button and show success message
        $('#approveBtn').hide();
        $('#approvalSuccess').show();
    } else {
        // Show approve button only if not currently processing approval
        if (!$('#approvalProgressContainer').is(':visible')) {
            $('#approveBtn').prop('disabled', false);
            $('#approveBtn').html('<i class="fa fa-check"></i> APPROVE');
            $('#approveBtn').show();
            $('#approvalSuccess').hide();
        }
    }
}

// Function to check if entity is already approved/active
function checkEntityApprovalStatus(status) {
    // Get current entity data from the modal context
    const currentEntityType = window.currentEntityType;
    const currentEntityId = window.currentEntityId;

    // Check the toggleEntityStatus function logic - if is_active is 1, then it's approved
    if (currentEntityType && currentEntityId) {
        // Try multiple approaches to find the toggle button
        let toggleButton = $(`button[onclick*="toggleEntityStatus"][onclick*="${currentEntityId}"][onclick*="'${currentEntityType.toLowerCase()}'"]`);

        // If not found, try different case variations
        if (toggleButton.length === 0) {
            const entityTypeVariations = [currentEntityType, currentEntityType.toLowerCase(), currentEntityType.toUpperCase()];
            for (const variation of entityTypeVariations) {
                toggleButton = $(`button[onclick*="toggleEntityStatus"][onclick*="${currentEntityId}"][onclick*="'${variation}'"]`);
                if (toggleButton.length > 0) break;
            }
        }

        if (toggleButton.length > 0) {
            // If button shows "Active" (fa-toggle-on), entity is already approved
            const isActive = toggleButton.find('.fa-toggle-on').length > 0;
            const buttonText = toggleButton.text().trim();
            // console.log('Toggle button found:', buttonText, 'isActive:', isActive);
            return isActive || buttonText.includes('Active');
        }
    }

    // Fallback: check status string
    return status === 'approved' || status === 'active' || status === 'completed';
}



// EXACT COPY of FIELD_MAPPINGS and getFieldValue function from orderDetailsVerification.php
function getFieldValue(fieldName, entityData) {
    const FIELD_MAPPINGS = [
        { field: '[[NAME]]', key: 'name' },
        { field: '[[ID]]', key: 'employee_code' },
        { field: '[[DEPARTMENT]]', key: 'department' },
        { field: '[[DESIGNATION]]', key: 'designation' },
        { field: '[[CONTACT]]', key: 'contact' },
        { field: '[[PARENT_NAME]]', key: 'parent_name' },
        { field: '[[FATHER_NAME]]', key: 'father_name' },
        { field: '[[MOTHER_NAME]]', key: 'mother_name' },
        { field: '[[FATHER_CONTACT]]', key: 'father_contact' },
        { field: '[[MOTHER_CONTACT]]', key: 'mother_contact' },
        { field: '[[BLOOD_GROUP]]', key: 'blood_group' },
        { field: '[[EMERGENCY_CONTACT]]', key: 'emergency_contact' },
        { field: '[[DATE_OF_BIRTH]]', key: 'date_of_birth' },
        { field: '[[DOB]]', key: 'dob' },
        { field: '[[QR_CODE]]', key: 'qr_code' },
        { field: '[[BAR_CODE]]', key: 'bar_code' },
        { field: '[[SIGNATURE]]', key: 'signature' },
        { field: '[[LOGO]]', key: 'logo' },
        { field: '[[ADDRESS]]', key: 'address' },
        { field: '[[EMAIL]]', key: 'email' },
        { field: '[[PHONE]]', key: 'phone' },
        { field: '[[WEBSITE]]', key: 'website' },
        { field: '[[SOCIAL_MEDIA]]', key: 'social_media' },
        { field: '[[QR_CODE_URL]]', key: 'qr_code_url' },
        { field: '[[BAR_CODE_URL]]', key: 'bar_code_url' },
        { field: '[[SIGNATURE_URL]]', key: 'signature_url' },
        { field: '[[LOGO_URL]]', key: 'logo_url' },
        { field: '[[ADDRESS_URL]]', key: 'address_url' },
        { field: '[[EMAIL_URL]]', key: 'email_url' },
        { field: '[[PHONE_URL]]', key: 'phone_url' },
        { field: '[[WEBSITE_URL]]', key: 'website_url' },
        { field: '[[SOCIAL_MEDIA_URL]]', key: 'social_media_url' },
        { field: '[[PHOTO]]', key: 'photo' },
        { field: '[[GRADE_SECTION]]', key: 'grade_section' },
        { field: '[[STUDENT_ADDRESS]]', key: 'address' },
        { field: '[[FATHER_ADDRESS]]', key: 'father_address' },
        { field: '[[MOTHER_ADDRESS]]', key: 'mother_address' },
        { field: '[[FATHER_PHOTO]]', key: 'father_photo' },
        { field: '[[MOTHER_PHOTO]]', key: 'mother_photo' },
        { field: '[[STUDENT_PHOTO]]', key: 'photo' },
        { field: '[[PARENT_PHOTO]]', key: 'photo' },
        { field: '[[GRADE]]', key: 'grade' },
        { field: '[[COMBINATION]]', key: 'combination' },
        { field: '[[ENROLLMENT_NO]]', key: 'enrollment_no' },
        { field: '[[ALPHA_ROLL_NO]]', key: 'alpha_rollnum' },
        { field: '[[ADMISSION_NO]]', key: 'admission_no' },
        { field: '[[RELATION_TYPE]]', key: 'relation_type' },
        { field: '[[SIBLING_NAME]]', key: 'sibling_name' },
        { field: '[[NAME_CLASS]]', key: 'name_class' },
        { field: '[[STUDENT_PICKING_ROUTE]]', key: 'picking_route' },
        { field: '[[STUDENT_DROPPING_ROUTE]]', key: 'dropping_route' },
        { field: '[[SPOUSE_NAME]]', key: 'spouse_name' },
        { field: '[[STAFF_TYPE]]', key: 'staff_type' },

    ];
    const mapping = FIELD_MAPPINGS.find(m => m.field === fieldName);
    return mapping ? entityData[mapping.key] || '' : '';
}

// EXACT COPY of renderSimplePreview function from orderDetailsVerification.php
function renderSimplePreview(entityData) {
    const frontHtml = `
        <div style="width: 100%; height: 100%; position: relative; background-color: #f8f9fa; border: 2px solid #ddd;">
            <!-- X marks -->
            <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" style="position: absolute; top: 0; left: 0;">
                <line x1="0" y1="0" x2="100%" y2="100%" stroke="#ccc" stroke-width="3"/>
                <line x1="100%" y1="0" x2="0" y2="100%" stroke="#ccc" stroke-width="3"/>
            </svg>

            <!-- Content overlay -->
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; z-index: 2;">
                <div style="background-color: rgba(255,255,255,0.9); padding: 15px; border-radius: 5px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                    <div style="font-size: 16px; color: #333; font-weight: bold; margin-bottom: 10px;">
                        ID CARD PREVIEW
                    </div>
                    <div style="color: #666;">
                        <strong>${entityData.name || 'Student Name'}</strong><br>
                        ${entityData.employee_code || entityData.admission_no || 'ID Number'}
                    </div>
                </div>
            </div>
        </div>
    `;

    const backHtml = `
        <div style="width: 100%; height: 100%; position: relative; background-color: #f8f9fa; border: 2px solid #ddd;">
            <!-- X marks -->
            <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" style="position: absolute; top: 0; left: 0;">
                <line x1="0" y1="0" x2="100%" y2="100%" stroke="#ccc" stroke-width="3"/>
                <line x1="100%" y1="0" x2="0" y2="100%" stroke="#ccc" stroke-width="3"/>
            </svg>

            <!-- Content overlay -->
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; z-index: 2;">
                <div style="background-color: rgba(255,255,255,0.9); padding: 15px; border-radius: 5px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                    <div style="font-size: 16px; color: #333; font-weight: bold;">
                        BACK SIDE
                    </div>
                </div>
            </div>
        </div>
    `;

    // Render simple preview in both containers - EXACT COPY from orderDetailsVerification.php
    $('#frontCardPreview').html(frontHtml);
    $('#backCardPreview').html(backHtml);

    // Enable action buttons when using simple preview
    // Get current status from the entity row (adapted for student context)
    const currentStatus = 'in review'; // Default status for student context

    // Enable buttons with proper state
    enableActionButtons(currentStatus);
}

// ID Card Approve Button Click Handler - EXACT COPY from orderDetailsVerification.php
$(document).on('click', '.id-card-approve', function() {
    if ($(this).prop('disabled')) {
        return;
    }

    // Get current entity data
    const entityId = window.visibleEntityIds ? window.visibleEntityIds[window.currentEntityIndex] : <?= $student_uid ?>;
    const relationTypeElement = $('#idCardModalTitle').text();
    const relationTypeMatch = relationTypeElement.match(/ID Card Preview - (.+)/);
    const relationType = relationTypeMatch ? relationTypeMatch[1] : 'Student';

    // Show confirmation dialog
    if (confirm('Are you sure you want to approve this ID card?')) {
        // Disable button and show loading
        $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> APPROVING...');

        // Process card images - EXACT COPY from orderDetailsVerification.php
        processCardImages(entityId, relationType);
    }
});

// Process card images function - EXACT COPY from orderDetailsVerification.php
function processCardImages(student_id, relation_type) {
    const frontEl = document.querySelector('#frontCardPreview');
    const backEl = document.querySelector('#backCardPreview');

    if (!frontEl) {
        console.error('Error: Front card preview not found');
        updateProgress(100, 'Error: Front card preview not found');
        setTimeout(() => {
            $('#approvalProgressContainer').hide();
            $('#approveBtn').show().prop('disabled', false);
            alert('Error: Front card preview not found');
        }, 2000);
        return;
    }

    // Update progress to show we're processing front image
    updateProgress(40, 'Processing front side of ID card...');

    // Process front image first
    processImage(frontEl, 'front.png', student_id, relation_type)
        .then(frontUrl => {
            console.log('Front URL:', frontUrl);

            // Check if back card exists and is visible
            const backCardExists = $('#backCardPreview').children().length > 0;

            if (backCardExists && backEl) {
                // Update progress to show we're processing back image
                updateProgress(60, 'Processing back side of ID card...');

                // Then process back image
                return processImage(backEl, 'back.png', student_id, relation_type)
                    .then(backUrl => {
                        console.log('Back URL:', backUrl);
                        // Both images processed successfully
                        submitApproval(student_id, relation_type, frontUrl, backUrl, window.currentTemplateId);
                    });
            } else {
                // Only front image processed
                console.log('Only front card exists, proceeding with front URL only');
                submitApproval(student_id, relation_type, frontUrl, null, window.currentTemplateId);
            }
        })
        .catch(error => {
            console.error('Error processing images:', error);
            updateProgress(100, 'Error: Failed to process images');
            setTimeout(() => {
                $('#approvalProgressContainer').hide();
                $('#approveBtn').show().prop('disabled', false);
                alert('Error: Failed to process images - ' + (error.message || 'Unknown error'));
            }, 2000);
        });
}

// Process image function - EXACT COPY from orderDetailsVerification.php
function processImage(element, filename, student_id, relation_type) {
    return new Promise((resolve, reject) => {
        // Use html2canvas with better options for more reliable rendering
        const options = {
            useCORS: true,           // Enable CORS for images
            allowTaint: true,        // Allow tainted canvas
            backgroundColor: null,    // Transparent background
            scale: 2                 // Higher quality
        };

        html2canvas(element, options).then(canvas => {
            canvas.toBlob(blob => {
                if (!blob) {
                    reject(new Error(`Failed to render ${filename}`));
                    return;
                }
                uploadToS3(blob, filename, student_id, relation_type)
                    .then(resolve)
                    .catch(reject);
            }, 'image/png');
        }).catch(err => {
            console.error('Error in html2canvas:', err);
            reject(err);
        });
    });
}

// Upload to S3 function - EXACT COPY from orderDetailsVerification.php
function uploadToS3(blob, filename, student_id, relation_type) {
    return new Promise((resolve, reject) => {
        // console.log(`Getting upload URL for ${filename}...`);

        $.ajax({
            url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
            type: 'POST',
            data: {
                file_type: blob.type,
                filename: filename,
                folder: 'idcards'
            },
            success: function(response) {
                try {
                    const { path, signedUrl } = JSON.parse(response);

                    // console.log(`Uploading ${filename} to server...`);

                    $.ajax({
                        url: signedUrl,
                        type: 'PUT',
                        headers: {
                            "Content-Type": blob.type,
                            "x-amz-acl": "public-read"
                        },
                        processData: false,
                        data: blob,
                        xhr: function() {
                            const xhr = $.ajaxSettings.xhr();
                            xhr.upload.onprogress = function(e) {
                                if (e.lengthComputable) {
                                    const progress = (e.loaded / e.total * 100) | 0;
                                    // console.log(`Uploading ${filename}: ${progress}%`);
                                }
                            };
                            return xhr;
                        },
                        success: () => {
                            // console.log(`${filename} uploaded successfully`);
                            resolve(path);
                        },
                        error: (err) => {
                            console.error(`Failed to upload ${filename}:`, err);
                            reject(new Error('Failed to upload to S3'));
                        }
                    });
                } catch (err) {
                    console.error('Invalid server response:', err);
                    reject(new Error('Invalid signed URL response'));
                }
            },
            error: (xhr, status, error) => {
                console.error('Failed to get signed URL:', error);
                reject(new Error('Failed to get signed URL'));
            }
        });
    });
}

// Function to submit approval - EXACT COPY from orderDetailsVerification.php
function submitApproval(studentId, relationType, frontUrl, backUrl, templateId) {
    // Update progress to show we're submitting the approval
    updateProgress(80, 'Submitting ID card approval...');

    $.ajax({
        url: '<?php echo site_url("student/Student_controller/approveIdCard"); ?>',
        type: 'POST',
        data: {
            student_id: <?= $student_uid ?>,
            relation_type: relationType,
            frontUrl: frontUrl,
            backUrl: backUrl,
            idcard_template_order_id: templateId
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Success - show completion
                updateProgress(100, 'ID Card approved successfully!');
                setTimeout(() => {
                    $('#approvalProgressContainer').hide();
                    $('#approvalSuccess').show();

                    // Update status badge
                    updateStatusBadge('approved');

                    // Optionally reload the page to reflect changes
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                }, 1500);

            } else {
                // Error
                const errorMessage = response.message || 'Failed to approve ID card';
                updateProgress(100, 'Error: ' + errorMessage);
                setTimeout(() => {
                    $('#approvalProgressContainer').hide();
                    $('#approveBtn').show().prop('disabled', false);
                    alert('Error: ' + errorMessage + '. Please try again.');
                }, 2000);
            }
        },
        error: function(xhr, status, error) {
            console.error('Approval error:', error);
            updateProgress(100, 'Error: Network error occurred');
            setTimeout(() => {
                $('#approvalProgressContainer').hide();
                $('#approveBtn').show().prop('disabled', false);
                alert('Error: Network error occurred. Please try again.');
            }, 2000);
        }
    });
}

// Function to reset approve button
function resetApproveButton() {
    $('.id-card-approve').prop('disabled', false).html('<i class="fa fa-check"></i> APPROVE');
}

// Function to update status badge
function updateStatusBadge(status) {
    const badge = $('#entityStatusBadge');
    badge.removeClass('status-approved status-in-review status-rejected status-modified');

    switch(status) {
        case 'approved':
            badge.addClass('status-approved').text('Approved').show();
            break;
        case 'in review':
            badge.addClass('status-in-review').text('In Review').show();
            break;
        case 'rejected':
            badge.addClass('status-rejected').text('Rejected').show();
            break;
        case 'modified':
            badge.addClass('status-modified').text('Modified').show();
            break;
        default:
            badge.hide();
    }
}

// ID Card Modify Button Click Handler - Placeholder
$(document).on('click', '.id-card-modify', function() {
    if ($(this).prop('disabled')) {
        return;
    }

    alert('Modify functionality will be implemented in future updates.');
});

// ID Card Remove Button Click Handler - Placeholder
$(document).on('click', '.id-card-remove', function() {
    if ($(this).prop('disabled')) {
        return;
    }

    if (confirm('Are you sure you want to remove this ID card?')) {
        alert('Remove functionality will be implemented in future updates.');
    }
});

// Modal close functionality
$('.id-card-close').click(function() {
    $('#idCardModal').fadeOut(300);
});

// Close modal when clicking outside
$(window).click(function(event) {
    if (event.target.id === 'idCardModal') {
        $('#idCardModal').fadeOut(300);
    }
});

$(function () {
    $('[data-bs-toggle="tooltip"]').tooltip();
});

// Function to toggle entity active/inactive status
function toggleEntityStatus(avatarId, newStatus, relationType, studentId, entityId) {
    if (!avatarId || avatarId == 0) {
        alert('Invalid avatar ID');
        return;
    }

    if (!studentId || studentId == 0) {
        alert('Invalid student ID');
        return;
    }

    if (!entityId || entityId == 0) {
        alert('Invalid entity ID');
        return;
    }

    var statusText = newStatus == 1 ? 'activate' : 'deactivate';
    var confirmMessage = 'Are you sure you want to ' + statusText + ' this ' + relationType + ' ID card?';

    if (!confirm(confirmMessage)) {
        return;
    }

    // Show loading state
    var button = event.target.closest('button');
    var originalHtml = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fa fa-spinner fa-spin mr-1"></i>Processing...';

    $.ajax({
        url: '<?php echo site_url("student/Student_controller/toggleEntityStatus"); ?>',
        type: 'POST',
        data: {
            avatar_id: avatarId,
            is_active: newStatus,
            relation_type: relationType,
            student_id: studentId,
            entity_id: entityId
        },
        dataType: 'json',
        success: function(response) {
            if (response) {
                // Reload the page to reflect changes
                location.reload();
            } else {
                alert('Error: ' + (response.message || 'Failed to update status'));
                // Reset button
                button.disabled = false;
                button.innerHTML = originalHtml;
            }
        },
        error: function(xhr, status, error) {
           
        }
    });
}


</script>

<!-- HTML2Canvas Library for Image Processing - EXACT COPY from orderDetailsVerification.php -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
