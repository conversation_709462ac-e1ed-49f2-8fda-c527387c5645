<!--
 * Name:    Oxygen
 * Author:  <PERSON><PERSON><PERSON><PERSON>hipulusu
 *          <EMAIL>
 *
 * Created:  27 March 2021
 *
 * Description: View class for add TT Template
 *
 * Requirements: PHP5 or above
 *
-->

<div id="errorAlert" class="hide alert alert-danger alert-dismissible" role="alert" auto-close="3000">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <div id="errorAlertText"></div>
</div>

<div id="successAlert" class="hide alert alert-success alert-dismissible" role="alert" auto-close="3000">
    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <div id="successAlertText"></div>
</div>

<!-- START BREADCRUMB -->
<ul class="breadcrumb">
    <li><a href="<?php echo site_url('avatars'); ?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('timetablev2/menu'); ?>">Timetable Menu</a></li>
    <li class="active">Manage Timetables</li>
</ul>

<!-- PAGE CONTENT WRAPPER -->
<div class="col-md-12">
  <div class="card cd_border">
    <div class="card-header panel_heading_new_style_staff_border_v2">
      <div class="row">
        <div class="col-md-12" style="width:100%;">

          <div class="col-md-8">
            <h3 class="card-title panel_title_new_style_staff">
              <a class="back_anchor" href="<?php echo site_url('timetablev2/menu'); ?>">
                <span class="fa fa-arrow-left"></span>
              </a> 
              Manage Timetable for <span id="timetable_template_name"></span>
            </h3>
          </div>

          <div class="col-md-4" style="float:left">
            <div class="col-md-4"></div>
            <div class="col-md-6">
              <select class="form-control selectpicker" onchange="select_timetable_template()" id="select_timetable_template">
                <option value="0">Select Timetable</option>
                <?php
                  foreach ($templates as $template) {
                    if ($template->is_disabled == '1') {
                      $option_style = "style='color:lightgrey;font-weight:200'";
                    } else {
                      $option_style = "style='font-weight:700'";
                    }
                    echo "<option value='$template->id' $option_style>$template->acad_year - $template->name ($template->status)</option>";
                  }
                ?>
              </select>
            </div>

            <div class="col-md-1">
              <a class="btn btn-link" style="cursor:pointer;" data-toggle="modal" data-target="#_add_template_modal" data-mode="create">
                <i class="fa fa-plus"></i>
              </a>
            </div>
            <div id="edit_template_metadata_div" class="col-md-1">
              <a id="edit_template_metadata_link" class="btn btn-link" style="cursor:pointer;" data-toggle="modal" data-template_id='' data-template_name='' data-description='' data-acad_year_id='' data-target="#_add_template_modal" data-mode="update" data-disable_tt=''>
                <i class="fa fa-edit"></i>
              </a>
            </div>
        </div>

        </div>
      </div>
    </div>

    <div class="card-body">
    
      <div class="row" style="margin: 0px;">
        <div class="col-md-12">
	        <div class="m-0 d-flex">
	            <div class="mr-5"><p style="font-size: 18px;font-weight: bold;color: #1e428a">Template/Rules</p></div>
	            <div class="mt-1 flex-fill"><hr></div>
	        </div>
	    </div>

      <div class="col-md-12">

        <div class="row m-0">
          
          <div class="col-md-3" id="create_raw_template_tile">
            <a class="link_class" href="#" data-target='#construct_tt_modal' data-toggle='modal' data-template_id=''>
              <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                <div class="widget-item-left">
                  <img class="img-responsive" src="<?php echo base_url('assets/img/blue48px/create-quiz.png'); ?>">
                </div>
                
                <div class="widget-data">
                  <div class="widget-title">Create Base Template</div>
                  <div class="widget-subtitle" style="color:#929292;">Create base template of the timetable</div>
                </div>
              </div>
            </a>
          </div>

          <div class="col-md-3" id="view_raw_template_tile">
            <a class="link_class" href="#" data-target='#view_time_template' data-toggle='modal' data-template_id='5' data-target='#view_time_template' data-toggle='modal' data-template_id=''>
              <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                <div class="widget-item-left">
                  <img class="img-responsive" src="<?php echo base_url('assets/img/blue48px/new -view-base-template.png'); ?>">
                </div>
                <div class="widget-data">
                  <div class="widget-title">Manage Base Template</div>
                  <div class="widget-subtitle" style="color:#929292;">View/Edit base template of the timetable</div>
                </div>
              </div>
            </a>
          </div>

          <div class="col-md-3" id="manage_section_template_tile">
            <a class="link_class" href="#" data-target='#s1_view_sections_box' data-toggle='modal' data-template_id=''>
              <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                <div class="widget-item-left">
                  <img class="img-responsive" src="<?php echo base_url('assets/img/blue48px/new-section-rules.png'); ?>">
                </div>
                <div class="widget-data">
                  <div class="widget-title">Section rules</div>
                  <div class="widget-subtitle" style="color:#929292;">Define rules for each sections</div>
                </div>
              </div>
            </a>
          </div>

          <div class="col-md-3" id="manage_staff_template_tile">
            <a class="link_class" href="#" data-target='#s1_view_staff_tt_template_box' data-toggle='modal' data-template_id='' >
              <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                <div class="widget-item-left">
                  <img class="img-responsive" src="<?php echo base_url('assets/img/blue48px/new-staff-rules.png'); ?>">
                </div>
                <div class="widget-data">
                  <div class="widget-title">Staff rules</div>
                  <div class="widget-subtitle" style="color:#929292;">Define rules for each staff</div>
                </div>
              </div>
            </a>
          </div>

          <div class="col-md-3" id="manage_room_template_tile">
            <a class="link_class" href="#"data-target='#s1_view_room_tt_template_box' data-toggle='modal' data-template_id='' >
              <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                <div class="widget-item-left">
                  <img class="img-responsive" src="<?php echo base_url('assets/img/blue48px/new-room-rules.png'); ?>">
                </div>
                <div class="widget-data">
                  <div class="widget-title">Room rules</div>
                  <div class="widget-subtitle" style="color:#929292;">Define rules for each room</div>
                </div>
              </div>
            </a>
          </div>

        </div>
      </div>
    </div>

      <div class="row" style="margin: 0px;" id="workload_seperator">
        <div class="col-md-12">
	        <div class="m-0 d-flex">
	            <div class="mr-5"><p style="font-size: 18px;font-weight: bold;color: #1e428a">Workloads</p></div>
	            <div class="mt-1 flex-fill"><hr></div>
	        </div>
	    </div>

      <div class="col-md-12">

        <div class="col-md-3" id="manage_section_workload_tile">
          <a class="link_class" href="#">
            <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
              <div class="widget-item-left">
                <img class="img-responsive" src="<?php echo base_url('assets/img/blue48px/new-manage-section-workload.png'); ?>">
              </div>
              <div class="widget-data">
                <div class="widget-title">Manage Section workload</div>
                <div class="widget-subtitle" style="color:#929292;">Create/view section workload</div>
              </div>
            </div>
          </a>
        </div>

        <div class="col-md-3" id="manage_staff_workload_tile">
          <a class="link_class" href="#">
            <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
              <div class="widget-item-left">
                <img class="img-responsive" src="<?php echo base_url('assets/img/blue48px/new-manage-staff-workload.png'); ?>">
              </div>
              <div class="widget-data">
                <div class="widget-title">Manage Staff/Room workload</div>
                <div class="widget-subtitle" style="color:#929292;">Create/view staff workload</div>
              </div>
            </div>
          </a>
        </div>
      
      
      </div>
    </div>

    <div class="row" style="margin: 0px;" id="construct_seperator">
        <div class="col-md-12">
	        <div class="m-0 d-flex">
	            <div class="mr-5"><p style="font-size: 18px;font-weight: bold;color: #1e428a">Construct</p></div>
	            <div class="mt-1 flex-fill"><hr></div>
	        </div>
	    </div>

      <div class="col-md-12">

        <div class="col-md-3" id="construct_timetable_tile">
          <a class="link_class" href="#">
            <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
              <div class="widget-item-left">
                <img class="img-responsive" src="<?php echo base_url('assets/img/blue48px/new-construct-timetable.png'); ?>">
              </div>
              <div class="widget-data">
                <div class="widget-title">Construct timetable</div>
                <div class="widget-subtitle" style="color:#929292;">Construct timetable</div>
              </div>
            </div>
          </a>
        </div>

        <?php if ($this->authorization->isAuthorized('TIMETABLE.AUTO_ALLOCATE_TIMETABLE')) : ?>
          <div class="col-md-3" id="autoallocate_timetable_tile">
            <a class="link_class" href="#">
              <div class="widget widget-default widget-item-icon new_height animate__animated animate__fadeIn"> 
                <div class="widget-item-left">
                  <img class="img-responsive" src="<?php echo base_url('assets/img/blue48px/new-construct-timetable.png'); ?>">
                </div>
                <div class="widget-data">
                  <div class="widget-title">Auto-allocate timetable</div>
                  <div class="widget-subtitle" style="color:#929292;">Auto-allocate timetable</div>
                </div>
              </div>
            </a>
          </div>
        <?php endif ?>

      </div>
  </div>
</div>
</div>

<!---Generic files--->
<?php $this->load->view("timetablev2/js_library/_tt_library.php"); ?>
<?php $this->load->view("timetablev2/template/inc/_css.php"); ?>

<!---For Creating a new timetable template name --->
<?php $this->load->view("timetablev2/template/inc/_add_view_template/_tt_add_template_modal_box.php"); ?>
<?php $this->load->view("timetablev2/template/inc/_add_view_template/_script.php"); ?>

<!---For Creating and Viewing a raw Timetable tempalte--->
<?php $this->load->view("timetablev2/template/inc/_edit_view_raw_template/_construct_tt_template.php"); ?>
<?php $this->load->view("timetablev2/template/inc/_edit_view_raw_template/_edit_period_times.php"); ?>
<?php $this->load->view("timetablev2/template/inc/_edit_view_raw_template/_script.php"); ?>
<?php $this->load->view("timetablev2/template/inc/_view_raw_template/_time_template_view.php"); ?>

<!---For Creating a section availability tempalte--->
<?php $this->load->view("timetablev2/template/inc/_assign_section_template/_s1_view_sections.php"); ?>
<?php $this->load->view("timetablev2/template/inc/_assign_section_template/_view_section_template.php"); ?>

<!---For Creating a staff availability tempalte--->
<?php $this->load->view("timetablev2/template/inc/_assign_staff_template/_s1_view_staff.php"); ?>
<?php $this->load->view("timetablev2/template/inc/_assign_staff_template/_view_staff_template.php"); ?>

<!-- -For Creating a room availability tempalte-->
<?php $this->load->view("timetablev2/template/inc/_assign_room_template/_s1_view_room.php"); ?>
<?php $this->load->view("timetablev2/template/inc/_assign_room_template/_view_room_template.php"); ?> 

<!---Others--->
<?php $this->load->view("timetablev2/template/inc/_tt_active_inactive_box.php"); ?>

<script language="javascript">

  $(document).ready(function () {
    var num_templates = "<?php echo count($templates) ?>";

    var selected_template_id = 0;

    if (num_templates != 0) {
      selected_template_id = get_cookie('seletected_timetable_template_id');
    }

    if (selected_template_id != null && selected_template_id != 0) {
      $("#select_timetable_template").val(selected_template_id);
    }

    _prepare_template_dashboard(selected_template_id);

  });

  function select_timetable_template() {
    var selected_template_id = $("#select_timetable_template :selected").val();

    //Store the selected template value in a cookie
    if (selected_template_id != 0) {
      set_cookie('seletected_timetable_template_id', selected_template_id);
    }

    _prepare_template_dashboard(selected_template_id);
  }

  async function _prepare_template_dashboard(selected_template_id) {
    $("#timetable_template_name").text($("#select_timetable_template :selected").text());
    var create_raw_tile_ele = $('#create_raw_template_tile');
    var view_raw_tile_ele = $('#view_raw_template_tile');
    var manage_section_template_ele = $('#manage_section_template_tile');
    var manage_staff_template_ele = $('#manage_staff_template_tile');
    var manage_room_template_ele = $('#manage_room_template_tile');
    // var manage_section_workload_ele = $('#manage_section_workload_tile');
    // var manage_staff_workload_ele = $('#manage_staff_workload_tile');
    var construct_timetable_ele = $('#construct_timetable_tile');
    var edit_template_metadata_link_ele = $('#edit_template_metadata_link');
    var edit_template_metadata_div = $('#edit_template_metadata_div');
    var disable_template_checkbox = $('#disable_template_checkbox');
    var workload_separator_div=$("#workload_seperator");
    var construct_separator_div=$("#construct_seperator");

    //Set the template id of the modal parameters to the selected template id
    $('#create_raw_template_tile .link_class').data('template_id', selected_template_id);
    $('#view_raw_template_tile .link_class').data('template_id', selected_template_id);
    $('#manage_staff_template_tile .link_class').data('template_id', selected_template_id);
    $('#manage_room_template_tile .link_class').data('template_id', selected_template_id);
    $('#manage_section_template_tile .link_class').data('template_id', selected_template_id);
    
    //Set the colors as enabled or disabled
    if (selected_template_id == null || selected_template_id == 0) {
      $(".icon").css('color','lightgrey');
      $(".widget-title").css('color','lightgrey');
      $(".widget-subtitle").css('color','lightgrey');
      edit_template_metadata_div.hide();
      return;
    }

    $(".icon").css('color','');
    $(".widget-title").css('color','');
    $(".widget-subtitle").css('color','');

    //Get the full details of the template selected
    response = await ajax_get_timetable_template_detail(selected_template_id);
    template_obj = response['template_detail'];

     //Set the template_id for section workload
    $("#manage_section_workload_tile .link_class").attr('href', "<?php echo base_url('timetablev2/section_workload/index/'); ?>" + template_obj.id);

    //Set the template_id for construct timetable
    $("#construct_timetable_tile .link_class").attr('href', "<?php echo base_url('timetablev2/construct_timetable/index/'); ?>" + template_obj.id);

    //Set the template_id for auto-allocate timetable
    $("#autoallocate_timetable_tile .link_class").attr('href', "<?php echo base_url('timetablev2/construct_timetable/show_autoallocate_ui/'); ?>" + template_obj.id);

    //Set the template_id for section workload
    $("#manage_staff_workload_tile .link_class").attr('href', "<?php echo base_url('timetablev2/staff_workload/index/'); ?>" + template_obj.id);

    //Set up for edit
    edit_template_metadata_div.show();
    edit_template_metadata_link_ele.data('template_id', selected_template_id);
    edit_template_metadata_link_ele.data('template_name', template_obj.name);
    edit_template_metadata_link_ele.data('description', template_obj.description);
    edit_template_metadata_link_ele.data('acad_year_id', template_obj.acad_year_id);
    edit_template_metadata_link_ele.data('disable_tt', template_obj.is_disabled);


    //Enable/disable the links based on whether template is created
    if (template_obj.is_periods_created <= 0) {
      //Raw template periods are not created
      create_raw_tile_ele.show();
      view_raw_tile_ele.hide();
      manage_section_template_ele.hide();
      manage_staff_template_ele.hide();
      manage_room_template_ele.hide();
      // manage_section_workload_ele.hide();
      // manage_staff_workload_ele.hide();
      construct_timetable_ele.hide();
      // workload_separator_div.hide();
      construct_separator_div.hide();
    } else {
      create_raw_tile_ele.hide();
      view_raw_tile_ele.show();
      manage_section_template_ele.show();
      manage_staff_template_ele.show();
      manage_room_template_ele.show();
      // manage_section_workload_ele.show();
      // manage_staff_workload_ele.show();
      construct_timetable_ele.show();
      // workload_separator_div.show();
      construct_separator_div.show();
    }

  }

</script>

<style>
  .new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 2rem !important;
    width: 2rem !important;
    text-align: center;
    vertical-align: middle;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
  }
</style>